package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * 111
 */
@Data
@OTC(value = "custom_111_001",type = OtcEnum.MessageType.NXC, version = "1", comment="111")
public class Custom111001 extends CiResCloud {


}
