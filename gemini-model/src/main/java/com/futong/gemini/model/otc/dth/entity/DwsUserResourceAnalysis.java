package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_user_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsUserResourceAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //用户组织ID
    private String user_id;
    //用户名称
    private String user_name;
    //组织数(个)
    private Integer dept_num;
    //业务系统数(个)
    private Integer project_num;
    //租户数(个)
    private Integer company_num;
    //服务目录数(个)
    private Integer orderGoods_num;
    //用户数
    private Integer user_num;
    //云主机数(台)
    private Integer vm_num;
    //云主机开机数(台)
    private Integer vm_running_num;
    //云主机关机数(台)
    private Integer vm_stopping_num;
    //云主机未知数(台)
    private Integer vm_unknown_num;
    //云主机CPU核数(核)
    private Double vm_cpu_size;
    //云主机内存大小(GB)
    private Double vm_memory_size;
    //云主机存储大小(GB)
    private Double vm_disk_size;
    //云主机已使用CPU核数(核)
    private Double vm_cpu_size_used;
    //云主机已使用内存大小(GB)
    private Double vm_memory_size_used;
    //云主机已使用存储大小(GB)
    private Double vm_disk_size_used;
    //云主机未使用CPU核数(核)
    private Double vm_cpu_size_unused;
    //云主机未使用内存大小(GB)
    private Double vm_memory_size_unused;
    //云主机未使用存储大小(GB)
    private Double vm_disk_size_unused;
    //云主机已使用最大CPU核数(核)
    private Double vm_max_cpu_size_used;
    //云主机已使用最大内存大小(GB)
    private Double vm_max_memory_size_used;
    //云主机已使用最大存储大小(GB)
    private Double vm_max_disk_size_used;
    //云主机未使用最大CPU核数(核)
    private Double vm_max_cpu_size_unused;
    //云主机未使用最大内存大小(GB)
    private Double vm_max_memory_size_unused;
    //云主机未使用最大存储大小(GB)
    private Double vm_max_disk_size_unused;
    //云主机已使用最小CPU核数(核)
    private Double vm_min_cpu_size_used;
    //云主机已使用最小内存大小(GB)
    private Double vm_min_memory_size_used;
    //云主机已使用最小存储大小(GB)
    private Double vm_min_disk_size_used;
    //云主机未使用最小CPU核数(核)
    private Double vm_min_cpu_size_unused;
    //云主机未使用最小内存大小(GB)
    private Double vm_min_memory_size_unused;
    //云主机未使用最小存储大小(GB)
    private Double vm_min_disk_size_unused;
    //云主机平均cpu使用率(%)
    private Double vm_avg_cpu_usage;
    //云主机平均内存使用率(%)
    private Double vm_avg_memory_usage;
    //云主机平均存储使用率(%)
    private Double vm_avg_disk_usage;
    //云主机最大cpu使用率(%)
    private Double vm_max_cpu_usage;
    //云主机最大内存使用率(%)
    private Double vm_max_memory_usage;
    //云主机最大存储使用率(%)
    private Double vm_max_disk_usage;
    //云主机最小平均cpu使用率(%)
    private Double vm_min_cpu_usage;
    //云主机最小内存平均使用率(%)
    private Double vm_min_memory_usage;
    //云主机最小存储使用率(%)
    private Double vm_min_disk_usage;
    //更新时间
    private String update_time;

}
