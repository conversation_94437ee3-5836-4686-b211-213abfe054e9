package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_弹性网络
 */
@Data
@OTC(value = "cmdb_eip_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_弹性网络")
public class CmdbEipRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //固定IP
    private String fixed_ip;
    //浮动IP
    private String floating_ip;
    //弹性公网IP
    private String elastic_ip;
    //带宽类型
    private String bandwidth_type;
    //带宽速率
    private String bandwidth_speed;
    //网段
    private String cidr;
    //EIP状态
    private String status;
    //原始状态
    private String open_status;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
