package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图资源组节点
 */
@Data
@OTC(value = "ca_resource_group_node",type = OtcEnum.MessageType.NXC, version = "1", comment="云图资源组节点")
public class CaResourceGroupNode {

    //主键
    private String id;
    //原始主键信息
    private String open_id;
    //节点名称
    private String open_node_name;
    //节点状态
    private String open_node_status;
    //节点IP
    private String open_node_ip;
    //节点配置
    private String open_node_config;
    //GPU规格
    private String opne_node_gpu_ts;
    //显存可用数量
    private Integer open_video_ram_usable;
    //显存总数
    private Integer open_video_ram_total;
    //CPU可用数量
    private Integer open_cpu_usable;
    //CPU总数
    private Integer open_cpu_total;
    //内存可用数量
    private Double open_ram_usable;
    //内存总数
    private Double open_ram_total;
    //显卡可用数量
    private Integer open_card_usable;
    //显卡总数
    private Integer open_card_total;
    //资源组ID
    private String open_group_id;
    //原始JSON
    private String open_json;
    //云类型
    private String cloud_type;
    //云账号ID
    private String cloud_account_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //是否删除（1是 NULL否）
    private String md5_str;
    //同步时间
    private Long sync_time;
    //子云账号id
    private String sub_account_id;

}
