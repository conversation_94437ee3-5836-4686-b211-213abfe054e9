package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 集群资源统计
 */
@Data
@OTC(value = "dws_cluster_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsClusterAnalysis  {

    private String id;
    //平台
    private String server_ip;
    //集群id
    private String cluster_id;
    //集群名称
    private String cluster_name;
    //cpu使用率
    private Double cpu_usage;
    //内存使用率
    private Double mem_usage;
    //磁盘使用率
    private Double disk_usage;
    //删除标记
    private Integer flag;

}
