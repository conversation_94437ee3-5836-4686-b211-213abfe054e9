package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 算力资产维度数据集
 */
@Data
@OTC(value = "dws_computing_property_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsComputingPropertyAnalysis {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //云类型
    private String cloud_type;
    //云类型
    private String cloud_type_name;
    //资产类型
    private String property_type;
    //资产类型名称
    private String property_type_name;
    //资产数量
    private Integer property_num;
    //更新时间
    private String update_time;
    //资源池名称
    private String pool_name;
    //资源池ID
    private String pool_id;

}
