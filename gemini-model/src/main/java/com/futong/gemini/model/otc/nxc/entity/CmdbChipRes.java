package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_芯片
 */
@Data
@OTC(value = "cmdb_chip_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_芯片")
public class CmdbChipRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //芯片品牌
    private String brand;
    //芯片架构
    private String arch;
    //芯片型号
    private String model;
    //芯片核数
    private Integer size;
    //芯片类型
    private String type;
    //CPU线程数
    private Integer thread_num;
    //CPU插槽数
    private Integer soket_num;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
