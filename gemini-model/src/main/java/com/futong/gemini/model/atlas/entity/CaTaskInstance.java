package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 任务实例信息
 */
@Data
@OTC(value = "ca_task_instance",type = OtcEnum.MessageType.NXC, version = "1", comment="任务实例信息")
public class CaTaskInstance {

    //主键
    private String id;
    //原始ID
    private String open_id;
    //任务ID
    private String task_id;
    //CPU
    private Integer cpu;
    //GPU
    private Integer gpu;
    //内存
    private Double memory;
    //实例名称
    private String name;
    //节点类型
    private String node_type;
    //同步时间
    private Long sync_time;
    //运行时间
    private String run_time;
    //启动时间
    private Long start_time;
    //结束时间
    private Long end_time;
    //挂载点
    private String ip;
    //类型
    private String type;
    //原始状态
    private String open_status;
    //原始JSON
    private String open_json;
    //云账号ID
    private String cloud_account_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //是否删除（1是 NULL否）
    private String md5_str;
    //云类型
    private String cloud_type;
    //子云账号id
    private String sub_account_id;

}
