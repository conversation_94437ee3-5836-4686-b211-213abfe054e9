package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_batch_message",type = OtcEnum.MessageType.DTH, version = "1")
public class MetaBatchMessage  {

    private Long id;
    //云类型
    private String cloud_type;
    //云账号
    private String account_id;
    //数据库
    private String message_type;
    private String db;
    //资源表
    private String model_table;
    private String dispatch_id;
    private String batch_id;
    //版本
    private String version;
    private String body;
    //超时时间
    private Long timeout;
    //创建时间
    private Long create_time;

}
