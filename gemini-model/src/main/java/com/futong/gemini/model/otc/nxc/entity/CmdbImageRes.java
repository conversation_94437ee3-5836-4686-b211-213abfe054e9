package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_镜像
 */
@Data
@OTC(value = "cmdb_image_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_镜像")
public class CmdbImageRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //镜像类型
    private String type;
    //镜像大小(GB)
    private Float size;
    //镜像状态
    private String status;
    //原始状态
    private String open_status;
    //最小磁盘大小
    private Float min_disk;
    //启动方式
    private String hw_firmware_type;
    //描述
    private String desc;
    //镜像范围
    private String visibility;
    //镜像校验
    private String md5;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;
    //磁盘格式
    private String disk_format;
    //镜像来源
    private String image_source;
    //产品编码
    private String product_code;

}
