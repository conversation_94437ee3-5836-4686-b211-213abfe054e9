package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_文件存储
 */
@Data
@OTC(value = "cmdb_file_storage",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_文件存储")
public class CmdbFileStorage extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //CPU核数
    private Integer cpu_size;
    //磁盘大小(GB)
    private Float size;
    //实例状态
    private String status;
    //原始状态
    private String open_status;
    //描述
    private String desc;
    //计算集群
    private String compute_cluster;
    //存储集群
    private String storage_cluster;
    //外网地址
    private String wan_endpoint;
    //内网地址
    private String lan_endpoint;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //过期时间
    private Long expire_time;
    //子账号
    private String sub_account_id;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
