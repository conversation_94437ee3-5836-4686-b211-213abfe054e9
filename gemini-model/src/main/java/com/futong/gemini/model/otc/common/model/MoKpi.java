package com.futong.gemini.model.otc.common.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class MoKpi {
	/**
	 * 资源类型
	 */
	private String resourceType;

	private long objTypeId;

	private long indicatorId;

	private String unit;

	private String kpi;

	private String label;

	public static List<MoKpi> getVmKpiList() {
		Long[] indicatorIds = new Long[]{562958543421441l, 562958543486979l,562958543552537l,562958543552538l,562958543618052l,562958543618061l,562958543618062l};
		String[] kpis = new String[]{"cpuUsage", "memUsage","netInRate","netOutRate", "diskUsage", "diskIOWrite","diskIORead"};
		List<MoKpi> list = new ArrayList<>();
		for(int i = 0; i < indicatorIds.length; i++) {
			MoKpi kpi = new MoKpi();
			kpi.setKpi(kpis[i]);
			kpi.setUnit("%");
			kpi.setResourceType("CLOUD_VM");
			kpi.setObjTypeId(562958543355904l);
			kpi.setIndicatorId(indicatorIds[i]);
			list.add(kpi);
		}
		return list;
	}

	public static List<MoKpi> getHostKpiList() {
		Long[] indicatorIds = new Long[]{1407379178586113l, 1407379178651656l,1407379178717195l,1407379178717196l,1407379178782740l,1407379178782738l,1407379178782739l};
		String[] kpis = new String[]{"cpuUsage", "memUsage","netInRate","netOutRate", "diskUsage", "diskIOWrite","diskIORead"};
		List<MoKpi> list = new ArrayList<>();
		for(int i = 0; i < indicatorIds.length; i++) {
			MoKpi kpi = new MoKpi();
			kpi.setKpi(kpis[i]);
			kpi.setUnit("%");
			kpi.setResourceType("SYS_PhysicalHost");
			kpi.setObjTypeId(1407379178520576l);
			kpi.setIndicatorId(indicatorIds[i]);
			list.add(kpi);
		}
		return list;
	}

}
