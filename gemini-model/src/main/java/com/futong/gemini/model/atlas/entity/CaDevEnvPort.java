package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 开发环境的端口映射
 */
@Data
@OTC(value = "ca_dev_env_port",type = OtcEnum.MessageType.NXC, version = "1", comment="开发环境的端口映射")
public class CaDevEnvPort {

    //ID
    private String id;
    //原始主键信息
    private String open_id;
    //服务名称
    private String service_name;
    //端口
    private String port;
    //节点端口
    private String node_port;
    //描述
    private String description;
    //访问地址
    private String access_address;
    //原始JSON数据
    private String open_json;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //md5字符串
    private String md5_str;
    //同步时间
    private Long sync_time;
    //开发环境id
    private String dev_env_id;
    //云类型
    private String cloud_type;
    //云账号ID
    private String cloud_account_id;
    //子云账号id
    private String sub_account_id;

}
