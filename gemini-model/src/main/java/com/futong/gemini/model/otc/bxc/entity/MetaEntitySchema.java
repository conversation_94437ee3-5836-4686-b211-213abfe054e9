package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;

@Data
@OTC(value = "meta_entity_schema",type = OtcEnum.MessageType.BXC, version = "1")
public class MetaEntitySchema extends CiBiz {

    //ID
    private String id;
    //表名
    private String schema_id;
    //版本
    private String schema_version;
    //主键字段
    private String pri_key;
    //md5值
    private String schema_digest;
    //国际化
    private String schema_name;

}
