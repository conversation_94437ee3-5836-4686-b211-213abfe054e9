package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 资源池维度数据集
 */
@Data
@OTC(value = "dws_resource_pool_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsResourcePoolAnalysis {

    //主键ID
    private String res_id;
    //资源池ID
    private String pool_id;
    //资源池名称
    private String pool_name;
    //资源池数量
    private Integer pool_num;
    //智算资源池数量
    private Integer computing_pool_num;
    //GPU资源池数量
    private Integer gpu_pool_num;
    //显卡总数
    private Integer total_num;
    //剩余显卡数
    private Integer free_total_num;
    //N显卡数
    private Integer n_total_num;
    //剩余N显卡数
    private Integer free_n_total_num;
    //国产显卡数
    private Integer g_total_num;
    //剩余国产显卡数
    private Integer free_g_total_num;
    //卡总算力
    private Double total_tflops;
    //N卡算力
    private Double n_total_tflops;
    //国产卡算力
    private Double g_total_tflops;
    //剩余卡算力
    private Double free_total_tflops;
    //卡售卖率
    private Double filter_rate;
    //已使用卡数
    private Integer card_used_num;
    //算力资源利用率
    private Double graphics_card_usage;
    //N卡算力资源利用率
    private Double n_graphics_card_usage;
    //国产卡算力资源利用率
    private Double g_graphics_card_usage;
    //服务（商品）费用
    private Double goods_price;
    //N卡服务（商品）费用
    private Double n_goods_price;
    //国产卡服务（商品）费用
    private Double g_goods_price;
    //GPU使用率
    private Double gpu_usage;
    //GPU显存使用率
    private Double gpu_core_usage;
    //日期
    private String datadate;
    //更新时间
    private String update_time;
    //资源池类型
    private String pool_type;

}
