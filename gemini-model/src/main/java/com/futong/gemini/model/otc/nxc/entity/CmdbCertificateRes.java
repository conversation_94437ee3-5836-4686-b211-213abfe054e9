package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_证书
 */
@Data
@OTC(value = "cmdb_certificate_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_证书")
public class CmdbCertificateRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //证书类型
    private String type;
    //指纹
    private String fingerprint;
    //证书arn
    private String arn;
    //域名
    private String domain_name;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //过期时间
    private Long expire_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
