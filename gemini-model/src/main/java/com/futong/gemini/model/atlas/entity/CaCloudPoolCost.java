package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 成本列表
 */
@Data
@OTC(value = "ca_cloud_pool_cost",type = OtcEnum.MessageType.NXC, version = "1", comment="成本列表")
public class CaCloudPoolCost {

    //成本的ID
    private String id;
    //云资源池的ID
    private String cloud_pool_id;
    //CPU（元/核/天)
    private Float cpu_price;
    //内存（元/GB/天)
    private Float mem_price;
    //ssd（元/GB/天)
    private Float ssd_price;
    //hdd（元/GB/天)
    private Float hdd_price;
    //GPU（元/卡/天)
    private Float gpu_price;
    //VPC（元/个)
    private Float vpc_price;
    //负载均衡VPC（元/MB)
    private Float slb_price;

}
