package com.futong.gemini.model.otc.nxc.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

/**
 * @ClassName: ResourceDataStore
 * @description: 资源存储信息表
 * @author: 杨力
 * @create: 2024-12-06 13:52
 * @version: v4.0
 **/
@Data
@OTC(value = "cmp_resource_data_store",type = OtcEnum.MessageType.NXC, version = "1", comment="资源存储信息表")
public class ResourceDataStore {

    /**
     * es中的唯一id
     */
    private String id;

    /***
     * 主机或者云主机resID
     */
    private String resId;
    /***
     * 资源类型
     */
    private String resourceType;
    /**
     * 关联dataStoreResId
     */
    private String dataStoreResId;
    /**
     * 云类型
     */
    private String cloudType;
    /**
     * 云账号
     */
    private String accountId;

    /**
     * 写延迟
     */
    private Double writeLatency;
    /**
     * 读延迟
     */
    private Double readLatency;
    /**
     * 读速率
     */
    private Double readAverage;
    /**
     * 写速率
     */
    private Double writeAverage;

    /**
     * 最长延迟（ms）
     */
    private Double longTime;

    /**
     * 读IOPS
     */
    private Double iopsRead;
    /**
     * 写IOPS
     */
    private Double iopsWrite;

    private Double dataStoreUsage;

    /**
     * 记录时间
     */
    private String createTime ;

}
