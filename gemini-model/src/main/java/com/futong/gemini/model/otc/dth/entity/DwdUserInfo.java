package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 用户基础信息表
 */
@Data
@OTC(value = "dwd_user_info",type = OtcEnum.MessageType.DTH, version = "1")
public class DwdUserInfo  {

    private String id;
    //用户id
    private String user_id;
    //用户名称
    private String user_name;
    //邮箱
    private String email;
    //电话
    private String phone;
    //状态
    private String enabled;
    //	描述
    private String description;
    //删除标记
    private Integer flag;

}
