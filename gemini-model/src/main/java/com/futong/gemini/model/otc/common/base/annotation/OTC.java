package com.futong.gemini.model.otc.common.base.annotation;

import com.futong.gemini.model.otc.common.base.OtcEnum;

import java.lang.annotation.*;

@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface OTC {
    OtcEnum.MessageType type();

    OtcEnum.Channel channel() default OtcEnum.Channel.MQ;

    String version();

    String value() default "";//表名

    String comment() default "";//表描述
}
