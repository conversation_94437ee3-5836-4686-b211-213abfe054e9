package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB云版本
 */
@Data
@OTC(value = "tmdb_cloud_version",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbCloudVersion extends CiBiz {

    //云类型
    private String cloud_code;
    //云类型名称
    private String cloud_name;
    //云版本
    private String cloud_version;
    //状态
    private Integer status;
    //创建时间
    private Long create_time;
    //修改时间
    private Long update_time;

}
