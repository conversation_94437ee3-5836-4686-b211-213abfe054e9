package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 集群基础信息表
 */
@Data
@OTC(value = "dwd_cluster_info",type = OtcEnum.MessageType.DTH, version = "1")
public class DwdClusterInfo  {

    private String id;
    //集群ID
    private String cluster_id;
    //集群名称
    private String cluster_name;
    //集群vip
    private String cluster_vip;
    //管理vip
    private String manage_vip;
    //集群状态
    private String cluster_status;
    //节点数量
    private Integer node_num;
    //cpu使用率
    private Double cpu_usage;
    //内存使用率
    private Double mem_usage;
    //	磁盘使用率
    private Double disk_usage;
    //服务地址
    private String server_ip;
    //删除标记
    private Integer flag;

}
