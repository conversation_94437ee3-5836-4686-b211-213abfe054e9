package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图模型调优任务
 */
@Data
@OTC(value = "ca_large_model_fine_tune_task",type = OtcEnum.MessageType.NXC, version = "1", comment="云图模型调优任务")
public class CaLargeModelFineTuneTask {

    //主键
    private String id;
    //原始主键信息
    private String open_id;
    //任务场景
    private String task_scene;
    //任务名称
    private String task_name;
    //任务描述
    private String task_desc;
    //任务类型
    private String task_type;
    //运行时长
    private String run_time;
    //数据集资产ID
    private String dataset_id;
    //数据集名称
    private String dataset_name;
    //算法资产ID
    private String algorithm_id;
    //算法名称
    private String algorithm_name;
    //算法资产版本ID
    private String algorithm_version_id;
    //算法资产版本名称
    private String algorithm_version_name;
    //模型资产ID
    private String model_id;
    //模型名称
    private String model_name;
    //模型版本ID
    private String model_version_id;
    //模型版本名称
    private String model_version_name;
    //镜像资产ID
    private String image_id;
    //镜像名称
    private String image_name;
    //镜像资产版本ID
    private String image_version_id;
    //镜像版本名称
    private String image_version_name;
    //运行状态
    private String open_status;
    //创建时间
    private Long open_create_time;
    //训练配置
    private String train_config_json;
    //资源配置
    private String resource_config_json;
    //云账号ID
    private String cloud_account_id;
    //启动文件
    private String startup_file;
    //算法路径
    private String algorithm_path;
    //任务框架
    private String framework;
    //微调策略ID
    private String strategy_id;
    //是否预置
    private String is_pretrain;
    //执行命令
    private String command;
    //输出目录
    private String out_put_dir;
    //工作路径
    private String working_dir;
    //超参配置
    private String hyper_params;
    //环境变量
    private String env_params;
    //资源组ID
    private String resource_group_id;
    //资源组名称
    private String resource_group_name;
    //创建人
    private String open_create_user_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    private String md5_str;
    //同步时间
    private Long sync_time;
    //云类型
    private String cloud_type;
    //原始数据
    private String open_json;
    //子云账号id
    private String sub_account_id;

}
