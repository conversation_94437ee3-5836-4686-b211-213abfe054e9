package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * GPU模型信息表
 */
@Data
@OTC(value = "ca_cloud_pool_performance_gpu",type = OtcEnum.MessageType.NXC, version = "1", comment="GPU模型信息表")
public class CaCloudPoolPerformanceGpu {

    //主键
    private String id;
    //资源池的外键
    private String cloud_pool_id;
    //GPU型号
    private String model_name;
    //内存大小，表示分配给模型的内存量，单位为GB
    private Float mem_size;
    //总数量，表示模型的总数量或容量
    private Integer total;
    //剩余数量，表示当前剩余可用的模型数量
    private Float mem_remaining;
    //模型权重
    private Float model_weight;
    private String cloud_account_id;
    private String md5_str;
    //同步时间
    private Long sync_time;
    //是否删除
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //子云账号id
    private String sub_account_id;

}
