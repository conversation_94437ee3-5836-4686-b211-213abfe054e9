package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB用户租户关联表
 */
@Data
@OTC(value = "tmdb_user_tenant",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbUserTenant extends CiBiz {

    //租户ID
    private String tenant_id;
    //用户ID
    private String user_id;

}
