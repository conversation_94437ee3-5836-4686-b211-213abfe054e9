package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB租户侧关系
 */
@Data
@OTC(value = "tmdb_tenant_link",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbTenantLink extends CiBiz {

    //租户ID
    private String tenant_id;
    //父级租户ID
    private String parent_tenant_id;
    //创建时间
    private Long create_time;
    //修改时间
    private Long update_time;
    private Integer flag;

}
