package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图资源组
 */
@Data
@OTC(value = "ca_resource_group",type = OtcEnum.MessageType.NXC, version = "1", comment="云图资源组")
public class CaResourceGroup {

    //主键
    private String id;
    //原始主键信息
    private String open_id;
    //资源组名称
    private String open_group_name;
    //资源组状态
    private String open_status;
    //资源池名称
    private String open_pool_name;
    //排队实例数
    private Integer open_queue_num;
    //CPU可用数量
    private Integer open_cpu_usable;
    //CPU总数
    private Integer open_cpu_total;
    //内存可用数量
    private Double open_ram_usable;
    //内存总数
    private Double open_ram_total;
    //显卡可用数量
    private Integer open_card_usable;
    //显卡总数
    private Integer open_card_total;
    //原始JSON
    private String open_json;
    //云类型
    private String cloud_type;
    //云账号ID
    private String cloud_account_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //是否删除（1是 NULL否）
    private String md5_str;
    //同步时间
    private Long sync_time;
    //子云账号id
    private String sub_account_id;

}
