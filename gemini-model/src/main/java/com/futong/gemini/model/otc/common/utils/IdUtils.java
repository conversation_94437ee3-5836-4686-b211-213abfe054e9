package com.futong.gemini.model.otc.common.utils;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.util.RandomUtil;
import com.futong.utils.SecureTool;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;


public class IdUtils {
    public static synchronized String gen() {
        StringBuilder builder = new StringBuilder(System.nanoTime() + "");//时间戳纳秒-15位
        builder.append(Thread.currentThread().hashCode() % 1000);////3位线程标志
        builder.append(RandomUtil.randomInt(99));//随机2位数
        return Base62.encode(new BigInteger(builder.toString()).toByteArray());
    }

    public static synchronized Set<String> gen(int count) {
        Set<String> ids = new HashSet<>();
        System.out.println(count);
        for (int i = 0; i < count; i++) {
            ids.add(gen());
        }
        if (ids.size() < count) {
            ids.addAll(gen(count - ids.size()));
        }
        return ids;
    }
    //加密ID
    public static String encryptId(String... params) {
        return SecureTool.encrypt(StringUtils.join(params, "_"));
    }


    public static void main(String[] args) {
        Set<String> gen = gen(10);
        System.out.println(gen);
    }

    public static void test() throws InterruptedException {
        Set<String> ids = new HashSet<>();
        long start = System.currentTimeMillis();
        System.out.println("开始时间:" + start);
        final CountDownLatch latch = new CountDownLatch(100);
        for (int i = 0; i < 100; i++) {
            new Thread(() -> {
                for (int i1 = 0; i1 < 1000; i1++) {
                    ids.add(gen());
                }
                latch.countDown();
            }).start();
        }
        latch.await();
        long end = System.currentTimeMillis();
        System.out.println("结束时间:" + end);
        System.out.println("耗时:" + (end - start) + "毫秒");
        System.out.println("获取不重复ID数" + ids.size());
        System.out.println(ids);
//        ids.forEach(System.out::println);
    }

}
