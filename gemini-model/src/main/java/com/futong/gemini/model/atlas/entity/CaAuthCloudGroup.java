package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图原平台权限分组
 */
@Data
@OTC(value = "ca_auth_cloud_group",type = OtcEnum.MessageType.NXC, version = "1", comment="云图原平台权限分组")
public class CaAuthCloudGroup {

    //主键
    private String id;
    //权限对象名称原始ID
    private String open_id;
    //权限对象编码
    private String open_code;
    //权限对象名称
    private String open_name;
    //同步时间
    private Long sync_time;
    //原始状态
    private String open_status;
    //云图状态 无效invalid有效 valid
    private String status;
    //原始JSON
    private String open_json;
    //云账号ID
    private String cloud_account_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    private String md5_str;
    //云类型
    private String cloud_type;

}
