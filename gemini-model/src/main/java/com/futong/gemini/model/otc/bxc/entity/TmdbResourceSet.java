package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;

@Data
@OTC(value = "tmdb_resource_set",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbResourceSet extends CiBiz {

    //云类型
    private String cloud_type;
    //云账号
    private String account_id;
    //北新仓资源类型
    private String set_type;
    //北新仓资源表明
    private String set_table;
    //北新仓资源ID
    private String set_id;
    //南新仓资源类型
    private String resource_type;
    //南新仓资源ID
    private String resource_id;
    private Integer flag;

}
