package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_磁盘
 */
@Data
@OTC(value = "cmdb_disk_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_磁盘")
public class CmdbDiskRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //磁盘状态
    private String status;
    //原始状态
    private String open_status;
    //是否是共享磁盘
    private String share_disk;
    //磁盘类型
    private String type;
    //磁盘分类
    private String category;
    //置备类型
    private String provision_type;
    //磁盘性能分类
    private String perf_category;
    //磁盘大小(GB)
    private Float size;
    //磁盘路径
    private String path;
    //lun号
    private String lun_id;
    //所属数据存储
    private String store_name;
    //描述
    private String desc;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;
    //磁盘已使用大小(GB)
    private Float used_size;
    //配置模式
    private String config_mode;

}
