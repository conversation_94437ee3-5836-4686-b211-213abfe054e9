package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 密钥对
 */
@Data
@OTC(value = "ca_key_pair",type = OtcEnum.MessageType.NXC, version = "1", comment="密钥对")
public class CaKeyPair {

    //ID
    private String id;
    //原始数据ID
    private String open_id;
    //名称
    private String name;
    //指纹
    private String fingerprint;
    //生成算法
    private String algorithm;
    //是否托管(true是1 false是0)
    private String is_hosted;
    //同步时间
    private Long sync_time;
    //是否删除（1是 2否）
    private Integer is_delete;
    //更新时间
    private Long delete_time;
    //云账号id
    private String cloud_account_id;
    //md5字符串
    private String md5_str;
    //原始json
    private String open_json;
    //云类型
    private String cloud_type;
    //子云账号id
    private String sub_account_id;

}
