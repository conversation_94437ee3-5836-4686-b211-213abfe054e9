package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB云管平台资源标签
 */
@Data
@OTC(value = "tmdb_resource_set_bak",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbResourceSetBak extends CiBiz {

    //云资源ID
    private String res_id;
    //云账号ID
    private String account_id;
    //资源类型
    private String resource_type;
    //云类型
    private String cloud_type;
    //运维侧标签1
    private String devops_level01;
    //运维侧标签2
    private String devops_level02;
    //运维侧标签3
    private String devops_level03;
    //运维侧标签4
    private String devops_level04;
    //运维侧标签5
    private String devops_level05;
    //运维侧标签6
    private String devops_level06;
    //租户侧标签1
    private String tenant_level01;
    //租户侧标签2
    private String tenant_level02;
    //租户侧标签3
    private String tenant_level03;
    //租户侧标签4
    private String tenant_level04;
    //租户侧标签5
    private String tenant_level05;
    //租户侧标签6
    private String tenant_level06;
    //其他标签1
    private String tags01;
    //其他标签2
    private String tags02;
    //其他标签3
    private String tags03;
    //其他标签4
    private String tags04;
    //其他标签5
    private String tags05;
    //状态
    private Integer status;
    //删除时间
    private Long delete_time;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    private Integer flag;

}
