package com.futong.gemini.model.otc.gjc.entity;

import lombok.Data;

/**
 * @ClassName: ResourcePerfDetail
 * @description: 资源性能数据明细
 * @author: 杨力
 * @create: 2024-09-05 10:00
 * @version: v4.0
 **/
@Data
public class PerfInfoBean {
    /**
     * es中的唯一id
     */
    private String id;

    /**
     * 资源ID
     */
    private String resId;
    /**
     * 云账号ID
     */
    private String accountId;
    /**
     * 云平台原始资源标识
     */
    private String openId;
    /**
     * 云平台原始资源名称
     */
    private String openName;
    /**
     * 云平类型
     */
    private String cloudType;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 创建时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String createTime;
    private Double cpuSize;
    private Double cpuUsage = 0.0;
    public void setCpuUsage(Double cpuUsage) {
        if(cpuUsage == null) return;
        this.cpuUsage = cpuUsage;
    }
    private Double memSize;
    private Double memUsage = 0.0;
    public void setMemUsage(Double memUsage) {
        if(memUsage == null) return;
        this.memUsage = memUsage;
    }
    private Double diskSize;
    private Double diskUsage= 0.0;
    public void setDiskUsage(Double diskUsage) {
        if(diskUsage == null) return;
        this.diskUsage = diskUsage;
    }
    private Double netIo= 0.0;
    public void setNetIo(Double netIo) {
        if(netIo == null) return;
        this.netIo = netIo;
    }
    private Double netIn= 0.0;
    public void setNetIn(Double netIn) {
        if(netIn == null) return;
        this.netIn = netIn;
    }
    private Double netOut= 0.0;
    public void setNetOut(Double netOut) {
        if(netOut == null) return;
        this.netOut = netOut;
    }
    private Double diskIo= 0.0;
    public void setDiskIo(Double diskIo) {
        if(diskIo == null) return;
        this.diskIo = diskIo;
    }
    private Double diskWrite= 0.0;
    public void setDiskWrite(Double diskWrite) {
        if(diskWrite == null) return;
        this.diskWrite = diskWrite;
    }
    private Double diskRead = 0.0;
    public void setDiskRead(Double diskRead) {
        if(diskRead == null) return;
        this.diskRead = diskRead;
    }
    private String source = "API";

}
