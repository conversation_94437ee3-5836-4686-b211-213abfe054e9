package com.futong.gemini.model.atlas.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

import java.util.Date;

/**
 * CaaS集群命名空间实体类
 */
@OTC(value = "ca_caas_cluster_namespace", type = OtcEnum.MessageType.ATLAS, version = "1", comment = "null") // 删除: @OTC注解
@Data
public class CaCaasClusterNamespace {

    /**
     * 资源唯一ID
     */
    private String id;

    /**
     * 命名空间ID
     */
    private String open_id;

    /**
     * 命名空间名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * CPU大小（核）
     */
    private Float cpu_size;

    /**
     * CPU使用量（核）
     */
    private Float cpu_used;

    /**
     * 内存大小（MB）
     */
    private Float memory_size;

    /**
     * 内存使用量（MB）
     */
    private Float memory_used;

    /**
     * GPU核（核）
     */
    private Float gpu_core;

    /**
     * GPU使用量（核）
     */
    private Float gpu_core_used;

    /**
     * GPU显存（MB）
     */
    private Float gpu_memory;

    /**
     * GPU显存使用量（MB）
     */
    private Float gpu_memory_used;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Long open_create_time;

    /**
     * 云类型
     */
    private String cloud_type;

    /**
     * 云账号id
     */
    private String cloud_account_id;

    /**
     * 子云账号id
     */
    private String sub_account_id;

    /**
     * 原始信息JSON
     */
    private String open_json;

    /**
     * 关联集群主键ID
     * ca_caas_cluster.id
     */
    private String relation_cluster;
    /**
     * 关联集群名称
     * ca_caas_cluster.name
     */
    private String relation_cluster_name;

    /**
     * devops集群关联关系
     */
    private String relation_devops_cluster;

    /**
     * devops命名空间关联关系
     */
    private String relation_devops_namespace;

    /**
     * 租户系统租户关联关系
     */
    private String relation_tenant_tenant;

    /**
     * 租户系统项目关联关系
     */
    private String relation_tenant_project;
    /**
     * 关联租户id
     */
    private String relation_tenant_id;
    /**
     * 关联项目id
     */
    private String relation_project_id;
}
