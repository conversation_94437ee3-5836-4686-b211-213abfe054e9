package com.futong.gemini.model.otc.nxc.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

/**
 * @ClassName: ResourceDataStore
 * @description: 资源网络信息表
 * @author: 杨力
 * @create: 2024-12-10 13:52
 * @version: v4.0
 **/
@Data
@OTC(value = "cmp_resource_data_nic",type = OtcEnum.MessageType.NXC, version = "1", comment="资源网络信息表")
public class ResourceDataNic {

    /**
     * es中的唯一id
     */
    private String id;

    /***
     * 主机或者云主机resID
     */
    private String resId;
    /***
     * 资源类型
     */
    private String resourceType;
    /**
     * 关联dataStoreResId
     */
    private String nicResId;
    /**
     * 云类型
     */
    private String cloudType;
    /**
     * 云账号
     */
    private String accountId;

    /**
     * 读
     */
    private Double netIn;
    /**
     * 写
     */
    private Double netOut;
    /**
     * 记录时间
     */
    private String createTime ;

}
