package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_model",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaModel {

    //模型ID
    private String model_id;
    //模型名称
    private String model_name;
    //云类型
    private String cloud_type;
    //继承模型
    private String source_model;
    //内置
    private Integer builtin;
    //是否展示
    private Integer must_display;
    //资源表
    private String model_table;
    //模型分组ID
    private String model_group_id;
    //模型类型
    private String modular_type;
    //字段属性
    private String body;
    //描述信息
    private String description;
    //字段数量
    private Long field_count;
    //资源数量
    private Long count;
    //删除时间
    private Long delete_time;

}
