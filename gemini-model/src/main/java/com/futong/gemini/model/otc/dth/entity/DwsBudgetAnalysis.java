package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 预算维度数据集
 */
@Data
@OTC(value = "dws_budget_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsBudgetAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //预算年
    private String year;
    //预算费用
    private Double cost;
    //已使用预算费用
    private Double used_cost;
    //剩余预算费用
    private Double remaining_cost;
    //更新时间
    private String update_time;

}
