package com.futong.gemini.model.atlas.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@OTC(value = "ca_caas_cluster_storage_class", type = OtcEnum.MessageType.ATLAS, version = "1", comment = "存储-class")
// 删除: @OTC注解
@Data
public class CaCaasClusterStorageClass {
    /**
     * 资源ID
     */
    private String id;

    /**
     * 工作负载ID
     */
    private String open_id;

    /**
     * 工作负载名称
     */
    private String name;

    /**
     * storage是否可扩容[true|Unknow]
     */
    private String allow_scaling;

    /**
     * 存储类型
     */
    private String type;

    /**
     * PVC容量值 （GB）
     */
    private float size;

    /**
     * 是否支持自动供应
     */
    private boolean support_auto_provision;

    /**
     * 是否支持中间件
     */
    private boolean support_middleware;

    /**
     * 创建时间
     */
    private Long open_create_time;

    /**
     * 描述
     */
    private String description;

    /**
     * 云类型
     */
    private String cloud_type;

    /**
     * 云账号id
     */
    private String cloud_account_id;

    /**
     * 子云账号id
     */
    private String sub_account_id;

    /**
     * 原始信息JSON
     */
    private String open_json;

    /**
     * 集群ID
     * ca_caas_cluster.id
     */
    private String relation_cluster;
    /**
     * 关联集群名称
     * ca_caas_cluster.name
     */
    private String relation_cluster_name;

    /**
     * 三层-集群
     */
    private String relation_devops_cluster;




}
