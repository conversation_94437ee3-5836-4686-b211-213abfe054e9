package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * 条目记录附件表
 */
@Data
@OTC(value = "tmdb_dict_item_file",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbDictItemFile extends CiBiz {

    //附件ID
    private String file_id;
    //附件名称
    private String file_name;
    //附件的路径
    private String file_path;
    //条目ID
    private String item_id;

}
