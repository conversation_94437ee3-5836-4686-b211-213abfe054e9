package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 服务器告警数量统计
 */
@Data
@OTC(value = "dws_server_alarm_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsServerAlarmAnalysis  {

    private String id;
    //平台
    private String server_ip;
    //级别
    private String level;
    //服务器名称
    private String name;
    //集群id
    private String cluster_id;
    //集群名称
    private String cluster_name;
    //数量
    private Integer count;
    //删除标记
    private Integer flag;

}
