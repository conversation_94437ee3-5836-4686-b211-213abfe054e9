package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_规格
 */
@Data
@OTC(value = "cmdb_flavor",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_规格")
public class CmdbFlavor extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //cpu架构
    private String cpu_arch;
    //CPU核数
    private Integer cpu_size;
    //内存大小(MB)
    private Integer mem_size;
    //默认系统盘大小(GB)
    private Float default_sysdisk_size;
    //最小系统盘大小(GB)
    private Float min_sysdisk_size;
    //最大系统盘大小(GB)
    private Float max_sysdisk_size;
    //描述
    private String desc;
    //镜像范围
    private String visibility;
    //规格状态
    private String status;
    //原始状态
    private String open_status;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //显卡核心数量
    private Integer gpu_core;
    //显卡型号
    private String gpu_model;
    //显卡大小(GB)
    private Float gpu_size;
    //显卡数量
    private Integer gpu_num;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //规格族编码
    private String specification_class_code;
    //规格族名称
    private String specification_class_name;
    //规格分类
    private String category;
    //产品编码
    private String product_code;

}
