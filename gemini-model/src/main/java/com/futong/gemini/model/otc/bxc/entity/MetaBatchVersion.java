package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;

@Data
@OTC(value = "meta_batch_version",type = OtcEnum.MessageType.BXC, version = "1")
public class MetaBatchVersion extends CiBiz {

    private Long id;
    //任务ID
    private String dispatch_id;
    //任务batch
    private String batch_id;
    //资源ID
    private String pri_id;
    //资源表
    private String model_table;
    //版本
    private Long version;
    //操作时间
    private Long update_time;

}
