package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 业务占比统计
 */
@Data
@OTC(value = "dws_business_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsBusinessAnalysis  {

    private String id;
    //平台
    private String server_ip;
    //集群id
    private String cluster_id;
    //虚拟机id
    private String vm_id;
    //虚拟机名称
    private String vm_name;
    //命名空间
    private String namespace;
    //项目id
    private String project_id;
    //项目名称
    private String project_name;
    //占用率
    private Double usage;
    //删除标记
    private Integer flag;

}
