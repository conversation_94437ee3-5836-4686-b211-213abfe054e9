package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 资源池信息表
 */
@Data
@OTC(value = "ca_cloud_pool_performance",type = OtcEnum.MessageType.NXC, version = "1", comment="资源池信息表")
public class CaCloudPoolPerformance {

    //资源池的唯一标识符
    private String cloud_pool_id;
    //CPU总量，单位为个数
    private Float cpu_total;
    //剩余CPU数量，单位为个数
    private Float cpu_remaining;
    //内存总量，单位为GB
    private Float mem_total;
    //剩余内存容量，单位为GB
    private Float mem_remaining;
    //SSD硬盘总量，单位为GB
    private Float ssd_total;
    //剩余SSD硬盘容量，单位为GB
    private Float ssd_remaining;
    //HDD硬盘总量，单位为GB
    private Float hdd_total;
    //剩余HDD硬盘容量，单位为GB
    private Float hdd_remaining;
    private String cloud_account_id;
    private String md5_str;
    //同步时间
    private Long sync_time;

}
