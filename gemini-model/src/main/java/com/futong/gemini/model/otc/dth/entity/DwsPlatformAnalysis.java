package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 平台数据统计
 */
@Data
@OTC(value = "dws_platform_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsPlatformAnalysis  {

    private String id;
    //平台地址
    private String server_ip;
    //k8s集群数量
    private Integer k8s_num;
    //NS总数
    private Integer ns_num;
    //项目总数
    private Integer project_num;
    //用户总数
    private Integer user_num;
    //PVS总数
    private Integer pvs_num;
    //cpu总量
    private Double total_cpu;
    //cpu请求率
    private Double request_cpu;
    //cpu使用率
    private Double used_cpu;
    //内存总量
    private Double total_memory;
    //内存请求率
    private Double request_memory;
    //内存总量
    private Double used_memory;
    //删除标记
    private Integer flag;

}
