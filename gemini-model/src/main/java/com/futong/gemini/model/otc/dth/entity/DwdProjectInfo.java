package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 项目基础信息表
 */
@Data
@OTC(value = "dwd_project_info",type = OtcEnum.MessageType.DTH, version = "1")
public class DwdProjectInfo  {

    private String id;
    //项目ID
    private String project_id;
    //父id
    private String parent_id;
    //id
    private String domain_id;
    //	是否是域
    private String is_domain;
    //	项目名称
    private String name;
    //描述
    private String description;
    //是否激活
    private String enabled;
    //标签
    private String label;
    //用户数
    private String user_count;
    //cpu已用
    private Double cpu_used;
    //内存已用
    private Double mem_used;
    //cpu最大限制
    private Double cpu_limit;
    //内存最大限制
    private Double mem_limit;
    //删除标记
    private Integer flag;

}
