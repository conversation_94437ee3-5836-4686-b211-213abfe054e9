package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_csp_company_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsCspCompanyResourceAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //云服务商ID
    private String company_id;
    //云服务商名称
    private String company_name;
    //账号下的用户ID
    private String user_ids;
    //账号下的租户ID
    private String company_ids;
    //账号下的组织ID
    private String dept_ids;
    //账号下的业务系统ID
    private String project_ids;
    //账号下的标签键
    private String tag_keys;
    //账号下的标签值
    private String tag_values;
    //用户数
    private Integer user_nums;
    //云服务商数
    private Integer company_nums;
    //组织数
    private Integer dept_nums;
    //业务系统数
    private Integer project_nums;
    //标签键数
    private Integer tag_keynums;
    //标签值数
    private Integer tag_nums;
    //一级组织数(个)
    private Integer first_level_dept_num;
    //二级组织数(个)
    private Integer second_level_dept_num;
    //三级组织数(个)
    private Integer third_level_dept_num;
    //四级组织数(个)
    private Integer four_level_dept_num;
    //五级组织数(个)
    private Integer five_level_dept_num;
    //业务系统数(个)
    private Integer project_num;
    //用户数(个)
    private Integer user_num;
    //服务目录数(个)
    private Integer orderGoods_num;
    //云主机数(台)
    private Integer vm_num;
    //云主机开机数(台)
    private Integer vm_running_num;
    //云主机关机数(台)
    private Integer vm_stopping_num;
    //云主机未知数(台)
    private Integer vm_unknown_num;
    //CPU核数(核)
    private Double cpu_size;
    //内存大小(GB)
    private Double memory_size;
    //存储大小(GB)
    private Double disk_size;
    //已使用CPU核数(核)
    private Double cpu_size_used;
    //已使用内存大小(GB)
    private Double memory_size_used;
    //已使用存储大小(GB)
    private Double disk_size_used;
    //未使用CPU核数(核)
    private Double cpu_size_unused;
    //未使用内存大小(GB)
    private Double memory_size_unused;
    //未使用存储大小(GB)
    private Double disk_size_unused;
    //已使用最大CPU核数(核)
    private Double max_cpu_size_used;
    //已使用最大内存大小(GB)
    private Double max_memory_size_used;
    //已使用最大存储大小(GB)
    private Double max_disk_size_used;
    //未使用最大CPU核数(核)
    private Double max_cpu_size_unused;
    //未使用最大内存大小(GB)
    private Double max_memory_size_unused;
    //未使用最大存储大小(GB)
    private Double max_disk_size_unused;
    //已使用最小CPU核数(核)
    private Double min_cpu_size_used;
    //已使用最小内存大小(GB)
    private Double min_memory_size_used;
    //已使用最小存储大小(GB)
    private Double min_disk_size_used;
    //未使用最小CPU核数(核)
    private Double min_cpu_size_unused;
    //未使用最小内存大小(GB)
    private Double min_memory_size_unused;
    //未使用最小存储大小(GB)
    private Double min_disk_size_unused;
    //云主机CPU平均使用率(%)
    private Double avg_cpu_usage;
    //云主机内存平均使用率(%)
    private Double avg_memory_usage;
    //云主机存储平均使用率(%)
    private Double avg_disk_usage;
    //云主机CPU最大使用率(%)
    private Double max_cpu_usage;
    //云主机内存最大使用率(%)
    private Double max_memory_usage;
    //云主机存储最大使用率(%)
    private Double max_disk_usage;
    //云主机CPU最小使用率(%)
    private Double min_cpu_usage;
    //云主机内存最小使用率(%)
    private Double min_memory_usage;
    //云主机存储最小使用率(%)
    private Double min_disk_usage;
    //配额总量VCPU(核)
    private Double cpu_quota;
    //配额总量内存(GB)
    private Double memory_quota;
    //配额总量存储(GB)
    private Double disk_quota;
    //配额总量云主机数量(个)
    private Integer num_quota;
    //已使用配额总量VCPU(核)
    private Double use_cpu;
    //已使用配额总量内存(GB)
    private Double use_memory;
    //已使用配额总量存储(GB)
    private Double use_disk;
    //已使用配额总量云主机数量(个)
    private Integer useNum;
    //可分配配额总量VCPU(核)
    private Double can_cpu_quotaAllocation;
    //可分配配额总量内存(GB)
    private Double can_memory_quotaAllocation;
    //可分配配额总量存储(GB)
    private Double can_disk_quotaAllocation;
    //可分配配额总量云主机数量(个)
    private Integer can_num_quotaAllocation;
    //已分配配额总量VCPU(核)
    private Double cpu_quotaAllocation;
    //已分配配额总量内存(GB)
    private Double memory_quotaAllocation;
    //已分配配额总量存储(GB)
    private Double disk_quotaAllocation;
    //已分配配额总量云主机数量(个)
    private Integer num_quotaAllocation;
    //可使用配额总量VCPU(核)
    private Double unuse_cpu_quota;
    //可使用配额总量内存(GB)
    private Double unuse_memory_quota;
    //可使用配额总量存储(GB)
    private Double unuse_disk_quota;
    //可使用配额总量云主机数量(个)
    private Integer unuse_num;
    //CPU配额平均使用率(%)
    private Float cpu_quota_usage;
    //内存配额平均使用率(%)
    private Float memory_quota_usage;
    //存储配额使用率(%)
    private Float disk_quota_usage;
    //云服务商数(个)
    private Integer company_num;
    //更新时间
    private String update_time;

}
