package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 业务系统维度数据集
 */
@Data
@OTC(value = "dws_project_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsProjectResourceAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //业务系统ID
    private String project_id;
    //业务系统名称
    private String project_name;
    //组织ID
    private String dept_id;
    //组织名称
    private String dept_name;
    //租户ID
    private String company_id;
    //租户名称
    private String company_name;
    //业务系统数(个)
    private Integer total_project_num;
    //用户数(个)
    private Integer user_num;
    //服务目录数(个)
    private Integer orderGoods_num;
    //云主机数(台)
    private Integer vm_num;
    //云主机开机数(台)
    private Integer vm_running_num;
    //云主机关机数(台)
    private Integer vm_stopping_num;
    //云主机未知数(台)
    private Integer vm_unknown_num;
    //CPU核数(核)
    private Double vm_cpu_size;
    //内存大小(GB)
    private Double vm_memory_size;
    //存储大小(GB)
    private Double vm_disk_size;
    //gpu云主机数(台)
    private Integer gpu_vm_num;
    //gpu云主机开机数(台)
    private Integer gpu_vm_running_num;
    //gpu云主机关机数(台)
    private Integer gpu_vm_stopping_num;
    //gpu云主机未知数(台)
    private Integer gpu_vm_unknown_num;
    //gpu云主机CPU核数(核)
    private Double gpu_vm_cpu_size;
    //gpu云主机内存大小(GB)
    private Double gpu_vm_memory_size;
    //gpu云主机存储大小(GB)
    private Double gpu_vm_disk_size;
    //显存大小
    private Float gpu_size;
    //显卡数量
    private Integer gpu_num;
    //已使用CPU核数(核)
    private Double vm_cpu_size_used;
    //已使用内存大小(GB)
    private Double vm_memory_size_used;
    //已使用存储大小(GB)
    private Double vm_disk_size_used;
    //未使用CPU核数(核)
    private Double vm_cpu_size_unused;
    //未使用内存大小(GB)
    private Double vm_memory_size_unused;
    //未使用存储大小(GB)
    private Double vm_disk_size_unused;
    //已使用最大CPU核数(核)
    private Double vm_max_cpu_size_used;
    //已使用最大内存大小(GB)
    private Double vm_max_memory_size_used;
    //已使用最大存储大小(GB)
    private Double vm_max_disk_size_used;
    //未使用最大CPU核数(核)
    private Double vm_max_cpu_size_unused;
    //未使用最大内存大小(GB)
    private Double vm_max_memory_size_unused;
    //未使用最大存储大小(GB)
    private Double vm_max_disk_size_unused;
    //已使用最小CPU核数(核)
    private Double vm_min_cpu_size_used;
    //已使用最小内存大小(GB)
    private Double vm_min_memory_size_used;
    //已使用最小存储大小(GB)
    private Double vm_min_disk_size_used;
    //未使用最小CPU核数(核)
    private Double vm_min_cpu_size_unused;
    //未使用最小内存大小(GB)
    private Double vm_min_memory_size_unused;
    //未使用最小存储大小(GB)
    private Double vm_min_disk_size_unused;
    //云主机CPU平均使用率(%)
    private Double vm_avg_cpu_usage;
    //云主机内存平均使用率(%)
    private Double vm_avg_memory_usage;
    //云主机存储平均使用率(%)
    private Double vm_avg_disk_usage;
    //云主机CPU最大使用率(%)
    private Double vm_max_cpu_usage;
    //云主机内存最大使用率(%)
    private Double vm_max_memory_usage;
    //云主机存储最大使用率(%)
    private Double vm_max_disk_usage;
    //云主机CPU最小使用率(%)
    private Double vm_min_cpu_usage;
    //云主机内存最小使用率(%)
    private Double vm_min_memory_usage;
    //云主机存储最小使用率(%)
    private Double vm_min_disk_usage;
    //业务系统数(个)
    private Integer project_num;
    //更新时间
    private String update_time;

}
