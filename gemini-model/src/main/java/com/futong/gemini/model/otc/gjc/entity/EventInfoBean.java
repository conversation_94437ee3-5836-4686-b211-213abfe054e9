package com.futong.gemini.model.otc.gjc.entity;

import lombok.Data;

/**
 * 告警对象
 */
@Data
public class EventInfoBean {
    /**
     * es中的唯一id
     */
    private String id;
    /**
     * 云账号ID
     */
    private String accountId;
    /**
     * 云平类型
     */
    private String cloudType;

    /**
     * 资源ID
     */
    private String resId;
    /**
     * 云平台原始资源标识
     */
    private String openId;
    /**
     * 云平台原始资源名称
     */
    private String openName;
    private String openLevel;
    private String eventLevel;
    private String eventType;
    private String eventName;
    /**
     * 告警类型
     */
    private String resourceType;
    private String detail;
    private String beginTime;
    private String endTime;
    /**
     * 告警来源
     */
    private String source = "API";
    /**
     * 其他JSON信息
     */
    private String jsonInfo;


}
