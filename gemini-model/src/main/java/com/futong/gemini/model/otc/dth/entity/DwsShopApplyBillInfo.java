package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_shop_apply_bill_info",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsShopApplyBillInfo  {

    //主键ID
    private String res_id;
    //序号
    private String row_num;
    //申请单号
    private String bill_code;
    //服务类型
    private String goods_type_name;
    //申请用户
    private String user_name;
    //所属组织
    private String dept_name;
    //申请状态
    private String bill_state;
    //申请时间
    private String create_time;
    //日期
    private String datadate;
    //更新时间
    private String update_time;
    //数量
    private Integer num;

}
