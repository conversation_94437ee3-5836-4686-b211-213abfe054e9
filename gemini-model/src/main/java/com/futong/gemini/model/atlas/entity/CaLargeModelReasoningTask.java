package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图模型推理任务
 */
@Data
@OTC(value = "ca_large_model_reasoning_task",type = OtcEnum.MessageType.NXC, version = "1", comment="云图模型推理任务")
public class CaLargeModelReasoningTask {

    //主键
    private String id;
    //原始主键
    private String open_id;
    //任务名称
    private String task_name;
    //备注
    private String task_desc;
    //是否大模型，0否；1是
    private Integer is_large_model;
    //模型精度
    private String model_float;
    //监控阈值
    private Integer rate;
    //监控类型，1:rps 2:concurrency 3:GPU使用率
    private Integer metric;
    //推理服务ID
    private String reasoning_id;
    //创建人名称
    private String create_user_name;
    //创建人ID
    private String create_user_id;
    //创建时间
    private Long open_create_time;
    //原始状态
    private String open_status;
    //日志输出路径
    private String log_dir;
    //修改时间
    private Long open_update_time;
    //框架
    private String framwork;
    //模型ID
    private String model_id;
    //模型版本ID
    private String model_version_Id;
    //请求方法
    private String request_method;
    //挂载路径
    private String volume_path;
    //镜像
    private String image;
    //内存大小
    private Double memory;
    //实例数量
    private Integer instance_sum;
    //资源组名称
    private String resource_group_name;
    //资源组ID
    private String resource_group_id;
    //CPU大小
    private Integer cpu;
    //GPU大小
    private Integer gpu;
    //最小实例数量
    private Integer min_instance_sum;
    //最大实例数
    private Integer max_instance_sum;
    //超参配置
    private String hyper_params;
    //环境变量
    private String env_params;
    //原始JSON
    private String open_json;
    //同步时间
    private Long sync_time;
    //云账号ID
    private String cloud_account_id;
    //创建人
    private String open_create_user_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //md5字符串
    private String md5_str;
    //云类型
    private String cloud_type;
    //子云账号id
    private String sub_account_id;

}
