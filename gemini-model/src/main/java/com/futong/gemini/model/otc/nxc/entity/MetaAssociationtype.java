package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_associationtype",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaAssociationtype {

    //主键ID
    private Long id;
    //关联标识
    private String association_type_id;
    //关联名称
    private String association_type_name;
    //源关联描述
    private String source_association;
    //描述
    private String dest_association;
    //内置
    private Integer builtin;

}
