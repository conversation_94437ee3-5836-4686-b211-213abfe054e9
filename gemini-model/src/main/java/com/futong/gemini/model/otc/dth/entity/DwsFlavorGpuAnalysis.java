package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 规格GPU维度数据集
 */
@Data
@OTC(value = "dws_flavor_gpu_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsFlavorGpuAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //云类型
    private String cloud_type;
    //显卡核心数量
    private Integer gpu_core;
    //显卡型号
    private String gpu_model;
    //显卡大小(GB)
    private Float gpu_size;
    //显卡数量
    private Integer gpu_num;
    //更新时间
    private String update_time;

}
