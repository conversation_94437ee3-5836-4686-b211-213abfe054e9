package com.futong.gemini.model.otc.gjc.entity;

import lombok.Data;

/**
 * 告警对象
 */
@Data
public class AlarmInfoBean {
    /**
     * es中的唯一id
     */
    private String id;
    /**
     * 云账号ID
     */
    private String accountId;
    /**
     * 云平类型
     */
    private String cloudType;

    /**
     * 资源ID
     */
    private String resId;
    /**
     * 告警ID
     */
    private String alarmId;

    /**
     * 告警名称
     */
    private String alarmName;

    /**
     * 告警类型
     */
    private String resourceType;
    /**
     * CMP告警级别
     */
    private String alarmLevel;

    /**
     * 云平台原始资源标识
     */
    private String openId;
    /**
     * 云平台原始资源名称
     */
    private String openName;
    /**
     * 云平台原始级别
     */
    private String openLevel;
    /**
     * 告警详情
     */
    private String detail;
    /***
     * 首次告警时间
     */
    private String firstTime;

    /***
     * 最后一次告警时间
     */
    private String lastTime;

    /**
     * 告警次数
     */
    private Integer count;


    private Boolean closedStatus;

    private String closedTime;
    private String closedUserName;

    private String devopsLevel01Name;
    private String devopsLevel01Code;
    private String devopsLevel02Name;
    private String devopsLevel02Code;
    private String devopsLevel03Name;
    private String devopsLevel03Code;
    private String devopsLevel04Name;
    private String devopsLevel04Code;

    /**
     * 其他JSON信息
     */
    private String jsonInfo;
    /**
     * 数据来源:api、agent
     */
    private String source;

    private String createTime;

}
