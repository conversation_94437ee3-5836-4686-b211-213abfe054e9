package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 任务标签
 */
@Data
@OTC(value = "ca_task_tag",type = OtcEnum.MessageType.NXC, version = "1", comment="任务标签")
public class CaTaskTag {

    //ID
    private String id;
    //资产类型：数据集dataset 模型model 算法algo 镜像image
    private String property_type;
    //原始数据ID
    private String open_id;
    //父级ID
    private String parent_id;
    //标签名称
    private String tag_name;
    //排序
    private Integer sort;
    //同步时间
    private Long sync_time;
    //是否删除（1是 2否）
    private Integer is_delete;
    //更新时间
    private Long delete_time;
    //云账号id
    private String cloud_account_id;
    //md5字符串
    private String md5_str;
    //原始json
    private String open_json;
    //云类型
    private String cloud_type;

}
