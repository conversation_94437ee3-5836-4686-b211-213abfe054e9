package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_块设备
 */
@Data
@OTC(value = "cmdb_block_device_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_块设备")
public class CmdbBlockDeviceRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //块存储状态
    private String status;
    //原始状态
    private String open_status;
    /**
     * lun大小(MB)
     */
    private Integer lun_size;

    /**
     * lun类型:thin-精简配置，thick-厚配置
     */
    private String lun_type;

    /**
     * 硬盘池名称
     */
    private String diskpool_name;

    /**
     * 主机组ID
     */
    private String host_group_id;

    /**
     * 主机组ID
     */
    private String host_group_name;

    /**
     * lun的编号
     */
    private String host_lun_id;

    /**
     * Qos名称
     */
    private String qos_name;

    /**
     * 描述
     */
    private String desc;

//    private String node_pool_name;
//
//    private String pool_name;
//
//    private String old_host_group_id;
//
//    private Integer old_size;





    /**
     * 子账号
     */
    private String sub_account_id;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;

    /**
     * 扩展字段1
     */
    private String extend1;

    /**
     * 扩展字段2
     */
    private String extend2;
    /**
     * 扩展字段3
     */
    private String extend3;

}
