package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_块存储
 */
@Data
@OTC(value = "cmdb_block_storage_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_块存储")
public class CmdbBlockStorageRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //块存储状态
    private String status;
    //原始状态
    private String open_status;
    //块存储类型
    private String disk_type;
    //磁盘分类
    private String category;
    //磁盘卷标识
    private String volume_id;
    //磁盘卷名称
    private String volume_name;
    //磁盘大小(GB)
    private Float disk_size;
    //磁盘路径
    private String disk_path;
    //描述
    private String desc;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
