package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_model_relation",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaModelRelation {

    //主键ID
    private String id;
    //云类型
    private String cloud_type;
    //云账号
    private String account_id;
    //源模型
    private String node1;
    //目标模型
    private String node2;
    //模型名称1
    private String node1_name;
    //模型名称2
    private String node2_name;

}
