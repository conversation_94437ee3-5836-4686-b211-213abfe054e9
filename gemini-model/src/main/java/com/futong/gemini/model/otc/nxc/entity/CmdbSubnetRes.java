package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_子网
 */
@Data
@OTC(value = "cmdb_subnet_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_子网")
public class CmdbSubnetRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //子网状态
    private String status;
    //原始状态
    private String open_status;
    //DHCP启用状态
    private Integer dhcp_enable;
    //IPv4网段
    private String cidr_ipv4;
    //IPv6网段
    private String cidr_ipv6;
    //IPv4网关
    private String gateway_ipv4;
    //IPv6网关
    private String gateway_ipv6;
    //ipv6启用状态
    private Integer ipv6_enable;
    //IPv4可用IP数量
    private Integer available_ip_count_ipv4;
    //IPv6可用IP数量
    private Integer available_ip_count_ipv6;
    //端口
    private String port;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;
    //类型
    private String type;
    //vlan标识符
    private String vlan_id;
    //连接方式
    private String connect_type;

}
