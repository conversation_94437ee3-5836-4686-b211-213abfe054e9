package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图资产版本
 */
@Data
@OTC(value = "ca_datahub_property_version",type = OtcEnum.MessageType.NXC, version = "1", comment="云图资产版本")
public class CaDatahubPropertyVersion {

    //主键
    private String id;
    //原始主键信息
    private String open_id;
    //版本状态
    private String version_status;
    //版本名称
    private String version_name;
    //版本描述
    private String version_desc;
    //创建时间
    private Long open_create_time;
    //更新时间
    private Long open_update_time;
    //原始JSON
    private String open_json;
    //资产ID
    private String propery_id;
    //云账号ID
    private String cloud_account_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    private String md5_str;
    //同步时间
    private Long sync_time;
    //路径
    private String path;
    //云类型
    private String cloud_type;
    //资产类型：数据集dataset 模型model 算法algo 镜像image
    private String property_type;
    //子云账号id
    private String sub_account_id;

}
