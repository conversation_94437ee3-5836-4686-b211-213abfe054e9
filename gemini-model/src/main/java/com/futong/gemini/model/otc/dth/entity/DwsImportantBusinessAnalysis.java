package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 关键业务资源统计
 */
@Data
@OTC(value = "dws_important_business_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsImportantBusinessAnalysis  {

    private String id;
    //关键业务名称
    private String business_name;
    //命名空间
    private String name_space;
    //名称
    private String name;
    //集群id
    private String cluster_id;
    //集群名称
    private String cluster_name;
    //平台地址
    private String server_ip;
    //CPU使用率百分比
    private Double cpu_usage;
    //内存使用率百分比
    private Double mem_usage;
    //发送速率
    private Double tx_speed;
    //接收速率
    private Double rx_speed;
    //删除标记
    private Integer flag;

}
