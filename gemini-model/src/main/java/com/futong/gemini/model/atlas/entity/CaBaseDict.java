package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 镜像类型
 */
@Data
@OTC(value = "ca_base_dict",type = OtcEnum.MessageType.NXC, version = "1", comment="镜像类型")
public class CaBaseDict {

    //ID
    private Integer id;
    //名称
    private String name;
    //镜像类型（1预置 0自定义 ）
    private String show_value;
    //镜像类型参数（1预置 2自定义 ）
    private String para_value;
    //原始json字符串
    private String open_json;
    //云类型
    private String cloud_type;
    //资源类型
    private String resource_type;
    private Integer order_num;

}
