package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 开发环境的环境变量
 */
@Data
@OTC(value = "ca_dev_env_variable",type = OtcEnum.MessageType.NXC, version = "1", comment="开发环境的环境变量")
public class CaDevEnvVariable {

    //ID
    private String id;
    //原始主键信息
    private String open_id;
    //键
    private String env_key;
    //值
    private String env_value;
    //原始JSON数据
    private String open_json;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //md5字符串
    private String md5_str;
    //同步时间
    private Long sync_time;
    //开发环境id
    private String dev_env_id;
    //云类型
    private String cloud_type;
    //云账号ID
    private String cloud_account_id;
    //子云账号id
    private String sub_account_id;

}
