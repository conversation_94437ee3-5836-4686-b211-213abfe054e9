package com.futong.gemini.model.otc.nxc.common;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

import java.util.List;
@Data
@OTC(value = "meta_data_relation",type = OtcEnum.MessageType.NXC, version = "v1")
public class Association {
    private String node1;
    private String node1Id;
    private String node2;
    private List<String> node2Ids;
    private String ext_key;
    private String ext_data;
}
