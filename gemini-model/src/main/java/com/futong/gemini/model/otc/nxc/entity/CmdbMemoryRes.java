package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_内存
 */
@Data
@OTC(value = "cmdb_memory_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_内存")
public class CmdbMemoryRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //内存品牌
    private String brand;
    //内存型号
    private String model;
    //内存频率
    private String frequency;
    //内存大小(MB)
    private Integer size;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
