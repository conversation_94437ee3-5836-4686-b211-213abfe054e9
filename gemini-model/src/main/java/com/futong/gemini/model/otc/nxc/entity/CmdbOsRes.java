package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_操作系统
 */
@Data
@OTC(value = "cmdb_os_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_操作系统")
public class CmdbOsRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //操作系统类型
    private String type;
    //操作系统版本
    private String version;
    //系统架构
    private String cpu_arch;
    //操作系统全称
    private String full_name;
    //操作系统名称
    private String name;
    //发行版名称
    private String distribution;
    //创建时间
    private Long create_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
