package com.futong.gemini.model.route;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "otc")
public class RouteConfiguration {

    private OtcEnum.Channel channel = OtcEnum.Channel.MQ;
    //南新仓-资源
    private RouteInfo nxc = new RouteInfo("cmp_resource_exchange", "cmp_resource");
    //南新仓-资源关联
    private RouteInfo nxcRelation = new RouteInfo("cmp_resource_exchange", "cmp_resource_relation");
    //南新仓-性能数据
    private RouteInfo nxcPerf = new RouteInfo("cmp_resource_exchange", "cmp_resource_perf");


    //北新仓-资源
    private RouteInfo bxc = new RouteInfo("cmp_resource_exchange", "bxc_resource");
    //广积仓-资源
    private RouteInfo gjc = new RouteInfo("cmp_resource_exchange", "gjc_resource");
    //广积仓-性能数据
    private RouteInfo gjcPerf = new RouteInfo("cmp_resource_exchange", "cmp_resource_perf");
    //广积仓-数据存储数据
    private RouteInfo gjcDsPerf = new RouteInfo("cmp_resource_exchange", "cmp_resource_data_store");
    //广积仓-数据存储数据
    private RouteInfo gjcNicPerf = new RouteInfo("cmp_resource_exchange", "cmp_resource_nic");
    //广积仓-告警数据
    private RouteInfo gjcAet = new RouteInfo("cmp_resource_exchange", "cmp_aet_local");
    //广积仓-账单
    private RouteInfo gjcBill = new RouteInfo("cmp_operation_exchange", "cmp_operation_bill");
    //洞庭湖
    private RouteInfo dth = new RouteInfo("cmp_resource_exchange", "dth_resource");
    //实时刷新
    private RouteInfo refresh = new RouteInfo("cmp_resource_exchange", "refresh_resource");
    //实时刷新
    private RouteInfo atlas = new RouteInfo("cmp_atlas_exchange", "cmp_atlas_resource_routing");

}
