package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_data_relation",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaDataRelation {

    //ID
    private String id;
    //云类型
    private String cloud_type;
    //云账号
    private String account_id;
    private String node1;
    private String node1_id;
    private String node2;
    private String node2_id;
    //版本
    private Long version;
    //删除标记
    private Long flag;
    //批次
    private String dispatch_id;

}
