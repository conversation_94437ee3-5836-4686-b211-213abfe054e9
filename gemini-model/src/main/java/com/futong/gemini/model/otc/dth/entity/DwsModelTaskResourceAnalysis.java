package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 模型任务数据集
 */
@Data
@OTC(value = "dws_model_task_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsModelTaskResourceAnalysis {

    //主键ID
    private String res_id;
    //任务ID
    private String task_id;
    //任务类型
    private String task_type;
    //任务状态
    private String task_status;
    //任务名称
    private String task_name;
    //任务数量(个)
    private Integer task_num;
    //运行中数量(个)
    private Integer task_running_num;
    //启动中数量(个)
    private Integer task_starting_num;
    //就绪数量(个)
    private Integer task_ready_num;
    //失败数量(个)
    private Integer task_failed_num;
    //停止中数量(个)
    private Integer task_stoping_num;
    //停止数量(个)
    private Integer task_stoped_num;
    //待创建数量(个)
    private Integer task_creating_num;
    //被动停止中数量(个)
    private Integer task_passive_stop_num;
    //恢复中数量(个)
    private Integer task_recovering_num;
    //终止失败数量(个)
    private Integer task_terminate_fail_num;
    //成功数量(个)
    private Integer task_success_num;
    //实例数量
    private Integer instance_num;
    //CPU核数(核)
    private Double cpu_size;
    //内存大小(GB)
    private Double memory_size;
    //显卡数量
    private Integer gpu_num;
    //日期
    private String datadate;
    //更新时间
    private String update_time;
    //成功数
    private Integer success_num;
    //失败数
    private Integer failed_num;
    //资源池名称
    private String pool_name;
    //资源池ID
    private String pool_id;
    //创建时间
    private String create_time;
    //结束时间
    private String finish_time;
    //CPU平均使用率
    private Double cpu_avg_usage;
    //内存平均使用率
    private Double mem_avg_usage;
    //磁盘平均使用率
    private Double disk_avg_usage;
    //gpu平均使用率
    private Double gpu_avg_usage;
    //显存利用率
    private Double gpu_core_avg_usage;
    //交易金额
    private Double task_price;

}
