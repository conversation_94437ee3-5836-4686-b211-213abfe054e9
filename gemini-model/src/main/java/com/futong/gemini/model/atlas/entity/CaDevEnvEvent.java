package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 开发环境的事件表
 */
@Data
@OTC(value = "ca_dev_env_event",type = OtcEnum.MessageType.NXC, version = "1", comment="开发环境的事件表")
public class CaDevEnvEvent {

    //ID
    private String id;
    //原始主键信息
    private String open_id;
    //事件名称
    private String event_name;
    //事件发生时间
    private Long event_time;
    //事件类型
    private String event_type;
    //事件原因
    private String event_reason;
    //事件消息
    private String event_message;
    //原始创建时间
    private Long open_create_time;
    //原始JSON数据
    private String open_json;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //md5字符串
    private String md5_str;
    //同步时间
    private Long sync_time;
    //开发环境id
    private String dev_env_id;
    //云类型
    private String cloud_type;
    //云账号ID
    private String cloud_account_id;
    //子云账号id
    private String sub_account_id;

}
