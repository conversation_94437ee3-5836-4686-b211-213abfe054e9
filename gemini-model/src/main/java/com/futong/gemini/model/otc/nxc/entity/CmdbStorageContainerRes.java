package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_存储容器
 */
@Data
@OTC(value = "cmdb_storage_container",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_存储容器")
public class CmdbStorageContainerRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //总容量(GB)
    private Float total_size;
    //已使用容量(GB)
    private Float used_size;
    //剩余容量(GB)
    private Float free_size;
    //使用率
    private Float useUsage;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //子账号
    private String sub_account_id;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
