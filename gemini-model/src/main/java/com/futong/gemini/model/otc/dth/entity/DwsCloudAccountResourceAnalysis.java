package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云账号数据集
 */
@Data
@OTC(value = "dws_cloud_account_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsCloudAccountResourceAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //云账号ID
    private String cloud_account_id;
    //云账号名称
    private String cloud_account_name;
    //云账号类别
    private String cloud_account_level;
    //账号下的用户ID
    private String user_ids;
    //账号下的租户ID
    private String company_ids;
    //账号下的组织ID
    private String dept_ids;
    //账号下的业务系统ID
    private String project_ids;
    //账号下的标签ID
    private String tag_values;
    //用户数
    private Integer user_nums;
    //租户数
    private Integer company_nums;
    //组织数
    private Integer dept_nums;
    //业务系统数
    private Integer project_nums;
    //标签值数
    private Integer tag_nums;
    //账号数量
    private Integer num;
    //公有云账号数量
    private Integer public_cloud_account_num;
    //私有云账号数量
    private Integer private_cloud_account_num;
    //虚拟化账号数量
    private Integer vm_cloud_account_num;
    //云主机数(台)
    private Integer vm_num;
    //云主机开机数(台)
    private Integer vm_running_num;
    //云主机关机数(台)
    private Integer vm_stopping_num;
    //云主机未知数(台)
    private Integer vm_unknown_num;
    //云主机CPU核数(核)
    private Double vm_cpu_size;
    //云主机内存大小(GB)
    private Double vm_memory_size;
    //云主机存储大小(GB)
    private Double vm_disk_size;
    //gpu云主机数(台)
    private Integer gpu_vm_num;
    //gpu云主机开机数(台)
    private Integer gpu_vm_running_num;
    //gpu云主机关机数(台)
    private Integer gpu_vm_stopping_num;
    //gpu云主机未知数(台)
    private Integer gpu_vm_unknown_num;
    //gpu云主机CPU核数(核)
    private Double gpu_vm_cpu_size;
    //gpu云主机内存大小(GB)
    private Double gpu_vm_memory_size;
    //gpu云主机存储大小(GB)
    private Double gpu_vm_disk_size;
    //显存大小
    private Float gpu_size;
    //显卡数量
    private Integer gpu_num;
    //云主机已使用CPU核数(核)
    private Double vm_cpu_size_used;
    //云主机已使用内存大小(GB)
    private Double vm_memory_size_used;
    //云主机已使用存储大小(GB)
    private Double vm_disk_size_used;
    //云主机未使用CPU核数(核)
    private Double vm_cpu_size_unused;
    //云主机未使用内存大小(GB)
    private Double vm_memory_size_unused;
    //云主机未使用存储大小(GB)
    private Double vm_disk_size_unused;
    //云主机已使用最大CPU核数(核)
    private Double vm_max_cpu_size_used;
    //云主机已使用最大内存大小(GB)
    private Double vm_max_memory_size_used;
    //云主机已使用最大存储大小(GB)
    private Double vm_max_disk_size_used;
    //云主机未使用最大CPU核数(核)
    private Double vm_max_cpu_size_unused;
    //云主机未使用最大内存大小(GB)
    private Double vm_max_memory_size_unused;
    //云主机未使用最大存储大小(GB)
    private Double vm_max_disk_size_unused;
    //云主机已使用最小CPU核数(核)
    private Double vm_min_cpu_size_used;
    //云主机已使用最小内存大小(GB)
    private Double vm_min_memory_size_used;
    //云主机已使用最小存储大小(GB)
    private Double vm_min_disk_size_used;
    //云主机未使用最小CPU核数(核)
    private Double vm_min_cpu_size_unused;
    //云主机未使用最小内存大小(GB)
    private Double vm_min_memory_size_unused;
    //云主机未使用最小存储大小(GB)
    private Double vm_min_disk_size_unused;
    //云主机平均cpu使用率(%)
    private Double vm_avg_cpu_usage;
    //云主机平均内存使用率(%)
    private Double vm_avg_memory_usage;
    //云主机平均存储使用率(%)
    private Double vm_avg_disk_usage;
    //云主机最大cpu使用率(%)
    private Double vm_max_cpu_usage;
    //云主机最大内存使用率(%)
    private Double vm_max_memory_usage;
    //云主机最大存储使用率(%)
    private Double vm_max_disk_usage;
    //云主机最小平均cpu使用率(%)
    private Double vm_min_cpu_usage;
    //云主机最小内存平均使用率(%)
    private Double vm_min_memory_usage;
    //云主机最小存储使用率(%)
    private Double vm_min_disk_usage;
    //物理机数(台)
    private Integer host_num;
    //物理机开机数(台)
    private Integer host_running_num;
    //物理机关机数(台)
    private Integer host_stopping_num;
    //物理机未知数(台)
    private Integer host_unknown_num;
    //物理机CPU核数(核)
    private Double host_cpu_size;
    //物理机内存大小(GB)
    private Double host_memory_size;
    //物理机存储大小(GB)
    private Double host_disk_size;
    //物理机已使用CPU核数(核)
    private Double host_cpu_size_used;
    //物理机已使用内存大小(GB)
    private Double host_memory_size_used;
    //物理机已使用存储大小(GB)
    private Double host_disk_size_used;
    //物理机未使用CPU核数(核)
    private Double host_cpu_size_unused;
    //物理机未使用内存大小(GB)
    private Double host_memory_size_unused;
    //物理机未使用存储大小(GB)
    private Double host_disk_size_unused;
    //物理机已使用最大CPU核数(核)
    private Double host_max_cpu_size_used;
    //物理机已使用最大内存大小(GB)
    private Double host_max_memory_size_used;
    //物理机已使用最大存储大小(GB)
    private Double host_max_disk_size_used;
    //物理机未使用最大CPU核数(核)
    private Double host_max_cpu_size_unused;
    //物理机未使用最大内存大小(GB)
    private Double host_max_memory_size_unused;
    //物理机未使用最大存储大小(GB)
    private Double host_max_disk_size_unused;
    //物理机已使用最小CPU核数(核)
    private Double host_min_cpu_size_used;
    //物理机已使用最小内存大小(GB)
    private Double host_min_memory_size_used;
    //物理机已使用最小存储大小(GB)
    private Double host_min_disk_size_used;
    //物理机未使用最小CPU核数(核)
    private Double host_min_cpu_size_unused;
    //物理机未使用最小内存大小(GB)
    private Double host_min_memory_size_unused;
    //物理机未使用最小存储大小(GB)
    private Double host_min_disk_size_unused;
    //物理机本地存储总量(GB)
    private Double host_local_storage_size;
    //物理机本地存储已用量(GB)
    private Double host_local_storage_used;
    //物理机本地存储剩余量(GB)
    private Double host_local_storage_free;
    //物理机共享存储总量(GB)
    private Double host_shared_storage_size;
    //物理机共享存储已用量(GB)
    private Double host_shared_storage_used;
    //物理机共享存储剩余量(GB)
    private Double host_shared_storage_free;
    //物理机存储总量本地+共享(GB)
    private Double host_storage_size;
    //物理机平均cpu使用率(%)
    private Double host_avg_cpu_usage;
    //物理机平均内存使用率(%)
    private Double host_avg_memory_usage;
    //物理机平均存储使用率(%)
    private Double host_avg_disk_usage;
    //物理机最大cpu使用率(%)
    private Double host_max_cpu_usage;
    //物理机最大内存使用率(%)
    private Double host_max_memory_usage;
    //物理机最大存储使用率(%)
    private Double host_max_disk_usage;
    //物理机最小平均cpu使用率(%)
    private Double host_min_cpu_usage;
    //物理机最小内存平均使用率(%)
    private Double host_min_memory_usage;
    //物理机最小存储使用率(%)
    private Double host_min_disk_usage;
    //更新时间
    private String update_time;

}
