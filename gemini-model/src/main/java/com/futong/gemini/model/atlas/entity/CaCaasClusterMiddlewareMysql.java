package com.futong.gemini.model.atlas.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@OTC(value = "ca_caas_cluster_middleware_mysql", type = OtcEnum.MessageType.ATLAS, version = "1", comment = "中间件-MySQL")
// 删除: @OTC注解
@Data
public class CaCaasClusterMiddlewareMysql {
    /**
     * 资源ID
     */
    private String id;

    /**
     * 普通任务ID
     */
    private String open_id;

    /**
     * 普通任务名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 部署模式
     */
    private String deploy_mod;

    /**
     * 基线名称
     */
    private String line_name;

    /**
     * 版本
     */
    private String version;

    /**
     * 角色
     */
    private String disaster_role;

    /**
     * 实例数量
     */
    private Integer instance_count;

    /**
     * 创建时间
     */
    private Long open_create_time;

    /**
     * 描述
     */
    private String description;

    /**
     * 云类型
     */
    private String cloud_type;

    /**
     * 云账号id
     */
    private String cloud_account_id;

    /**
     * 子云账号id
     */
    private String sub_account_id;

    /**
     * 原始信息JSON
     */
    private String open_json;

    /**
     * 集群ID
     */
    private String relation_cluster;

    /**
     * 关联集群名称
     * ca_caas_cluster.name
     */
    private String relation_cluster_name;

    /**
     * 三层-集群
     */
    private String relation_devops_cluster;

    /**
     * 命名空间ID
     */
    private String relation_namespace;

    /**
     * 关联命名空间名称
     * ca_caas_cluster_namespace.name
     */
    private String relation_namespace_name;

    /**
     * 三层-命名空间
     */
    private String relation_devops_namespace;
}
