package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * 其他模型演示
 */
@Data
@OTC(value = "custom_qtmxys_001",type = OtcEnum.MessageType.NXC, version = "1", comment="其他模型演示")
public class CustomQtmxys001 extends CiResCloud {

    //云平台原始资源ID
    private String open_id;

}
