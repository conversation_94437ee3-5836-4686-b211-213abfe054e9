package com.futong.gemini.model.otc.common.model;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import lombok.Data;

import java.util.List;

@Data
public class OtcRefreshMessage<T> {
    private String table;
    private String action;//[update,delete]
    private String cloudType;//云类型
    private String accountBizId;//云账号ID
    private OtcEnum.MessageType messageType;
    private Object data;//数据信息
    private List<String> deleteRelationTables;//需要级联删除的表 如["cmdb_disk_res","cmdb_ip_res"]
}