package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_model_group",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaModelGroup {

    //主键ID
    private Long id;
    //资源ID
    private String model_group_id;
    //资源名称
    private String model_group_name;
    //描述信息
    private String description;
    private Long order;
    //父资源节点
    private String parent_id;
    //图标
    private String icon;

}
