package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_field_view_component",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaFieldViewComponent {

    //主键ID
    private Long id;
    //标识ID
    private String component_id;
    //注册名称
    private String component_name;
    //描述
    private String desc;
    //参数配置
    private String parameter;

}
