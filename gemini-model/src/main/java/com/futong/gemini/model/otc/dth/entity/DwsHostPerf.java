package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_host_perf",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsHostPerf  {

    //主键ID
    private String res_id;
    //年
    private String datayear;
    //月
    private String datamonth;
    //日
    private String datadate;
    //资源在CMP上的唯一标识
    private String cmp_id;
    //ip
    private String server_ip;
    //云类型名称
    private String cloud_type_name;
    //云类型
    private String cloud_type;
    //物理机CPU平均使用率(%)
    private Float avg_cpu_usage;
    //物理机CPU最小使用率(%)
    private Float min_cpu_usage;
    //物理机CPU最大使用率(%)
    private Float max_cpu_usage;
    //物理机内存平均使用率(%)
    private Float avg_memory_usage;
    //物理机内存最小使用率(%)
    private Float min_memory_usage;
    //物理机内存最大使用率(%)
    private Float max_memory_usage;
    //物理机存储平均使用率(%)
    private Float avg_disk_usage;
    //物理机存储最小使用率(%)
    private Float min_disk_usage;
    //物理机存储最大使用率(%)
    private Float max_disk_usage;
    //更新时间
    private String update_time;

}
