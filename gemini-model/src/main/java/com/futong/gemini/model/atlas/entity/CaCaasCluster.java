package com.futong.gemini.model.atlas.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@OTC(value = "ca_caas_cluster", type = OtcEnum.MessageType.ATLAS, version = "1", comment = "null") // 删除: @OTC注解
@Data
public class CaCaasCluster {
    /**
     * 资源唯一ID
     */
    private String id;

    /**
     * 集群名称（名字唯一）
     */
    private String open_id;

    /**
     * 集群名称
     */
    private String name;

    /**
     * 节点数
     */
    private Integer node_count;

    /**
     * k8s版本
     */
    private String version;

    /**
     * 状态
     */
    private String status;

    /**
     * 原始创建时间
     */
    private Long open_create_time;

    /**
     * 描述
     */
    private String description;

    /**
     * 云类型
     */
    private String cloud_type;

    /**
     * 云账号id
     */
    private String cloud_account_id;

    /**
     * 子云账号id
     */
    private String sub_account_id;

    /**
     * 原始信息JSON
     */
    private String open_json;
}

