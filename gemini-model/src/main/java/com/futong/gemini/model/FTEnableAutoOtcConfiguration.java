package com.futong.gemini.model;

import com.futong.common.mq.FTRabbitAutoConfiguration;
import com.futong.gemini.model.route.RouteConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import({FTRabbitAutoConfiguration.class, RouteConfiguration.class})
public @interface FTEnableAutoOtcConfiguration {
}
