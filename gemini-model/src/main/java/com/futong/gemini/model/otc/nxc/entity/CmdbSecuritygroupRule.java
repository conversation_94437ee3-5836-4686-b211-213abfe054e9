package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_安全组规则
 */
@Data
@OTC(value = "cmdb_securitygroup_rule",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_安全组规则")
public class CmdbSecuritygroupRule extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //出入控制方向
    private String direction;
    //访问权限
    private String policy;
    //源IP地址段
    private String source_cidr;
    //目的IP地址段
    private String dest_cidr;
    //端口范围
    private String port_range;
    //描述
    private String desc;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;
    //规则优先级
    private String priority;
    //IP协议
    private String ip_protocol;
    //原始direction
    private String open_direction;
    //原始规则优先级
    private String open_policy;

}
