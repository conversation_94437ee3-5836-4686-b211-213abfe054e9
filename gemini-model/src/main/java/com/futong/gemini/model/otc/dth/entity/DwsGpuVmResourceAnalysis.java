package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * GPU云主机数据集
 */
@Data
@OTC(value = "dws_gpu_vm_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsGpuVmResourceAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //cmpID
    private String cmp_id;
    //用户ID
    private String user_id;
    //用户名称
    private String user_name;
    //集群ID
    private String cluster_id;
    //Server_IP
    private String server_ip;
    //租户ID
    private String company_id;
    //租户名称
    private String company_name;
    //组织ID
    private String dept_id;
    //组织名称
    private String dept_name;
    //业务系统ID
    private String project_id;
    //业务系统名称
    private String project_name;
    //标签键ID
    private String key_id;
    //标签值ID
    private String tag_key;
    //标签key
    private String tag_name;
    //标签value
    private String tag_value;
    //云账号id
    private String cloud_account_id;
    //云账号名称
    private String cloud_account_name;
    //云类型
    private String cloud_type;
    //云类型名称
    private String cloud_type_name;
    //云主机名称
    private String vm_name;
    //云主机ID
    private String vm_id;
    //云主机IP
    private String vm_ip;
    //云主机状态
    private String vm_status;
    //物理机ID
    private String host_id;
    //物理机名称
    private String host_name;
    //物理机IP
    private String host_ip;
    //云主机数(台)
    private Integer vm_num;
    //云主机开机数(台)
    private Integer vm_running_num;
    //云主机关机数(台)
    private Integer vm_stopping_num;
    //云主机未知数(台)
    private Integer vm_unknown_num;
    //云主机CPU核数(核)
    private Double vm_cpu_size;
    //云主机内存大小(GB)
    private Double vm_memory_size;
    //云主机存储大小(GB)
    private Double vm_disk_size;
    //显卡型号
    private String gpu_model;
    //显存大小
    private Float gpu_size;
    //显卡数量
    private Integer gpu_num;
    //云主机已使用CPU核数(核)
    private Double vm_cpu_size_used;
    //云主机已使用内存大小(GB)
    private Double vm_memory_size_used;
    //云主机已使用存储大小(GB)
    private Double vm_disk_size_used;
    //云主机未使用CPU核数(核)
    private Double vm_cpu_size_unused;
    //云主机未使用内存大小(GB)
    private Double vm_memory_size_unused;
    //云主机未使用存储大小(GB)
    private Double vm_disk_size_unused;
    //云主机CPU平均使用率(%)
    private Double vm_avg_cpu_usage;
    //云主机内存平均使用率(%)
    private Double vm_avg_memory_usage;
    //云主机存储平均使用率(%)
    private Double vm_avg_disk_usage;
    //云主机CPU最大使用率(%)
    private Double vm_max_cpu_usage;
    //云主机内存最大使用率(%)
    private Double vm_max_memory_usage;
    //云主机存储最大使用率(%)
    private Double vm_max_disk_usage;
    //云主机CPU最小平使用率(%)
    private Double vm_min_cpu_usage;
    //云主机内存最小使用率(%)
    private Double vm_min_memory_usage;
    //云主机存储最小使用率(%)
    private Double vm_min_disk_usage;
    //更新时间
    private String update_time;
    //规格名称
    private String flavor_name;
    //售卖金额
    private Double sell_price;
    //gpu已使用大小
    private Double vm_gpu_size_used;
    //gpu未使用大小
    private Double vm_gpu_size_unused;
    //gpu平均使用率
    private Double vm_avg_gpu_usage;
    //gpu最大使用率
    private Double vm_max_gpu_usage;
    //gpu最小使用率
    private Double vm_min_gpu_usage;
    //gpu显存平均使用率
    private Double vm_avg_gpu_core_usage;
    //gpu显存最大使用率
    private Double vm_max_gpu_core_usage;
    //gpu显存最小使用率
    private Double vm_min_gpu_core_usage;
    //资源池ID
    private String pool_id;
    //资源池名称
    private String pool_name;
}
