package com.futong.gemini.model.atlas.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@OTC(value = "ca_caas_cluster_net_nginx_ingress", type = OtcEnum.MessageType.ATLAS, version = "1", comment = "网络-ingress路由")
// 删除: @OTC注解
@Data
public class CaCaasClusterNetNginxIngress {
    /**
     * 资源ID
     */
    private String id;

    /**
     * 路由ID
     */
    private String open_id;

    /**
     * 路由名称
     */
    private String name;

    /**
     * 负载均衡
     */
    private String ic_name;

    /**
     * 标签
     */
    private String label;

    /**
     * 域名路径
     */
    private String path;

    /**
     * 创建时间
     */
    private Long open_create_time;

    /**
     * 描述
     */
    private String description;

    /**
     * 云类型
     */
    private String cloud_type;

    /**
     * 云账号id
     */
    private String cloud_account_id;

    /**
     * 子云账号id
     */
    private String sub_account_id;

    /**
     * 原始信息JSON
     */
    private String open_json;

    /**
     * 集群ID
     */
    private String relation_cluster;

    /**
     * 三层-集群
     */
    private String relation_devops_cluster;

    /**
     * 命名空间ID
     */
    private String relation_namespace;

    /**
     * 三层-命名空间
     */
    private String relation_devops_namespace;

    /**
     * 关联集群名称
     * ca_caas_cluster.name
     */
    private String relation_cluster_name;

    /**
     * 关联命名空间名称
     * ca_caas_cluster_namespace.name
     */
    private String relation_namespace_name;
}
