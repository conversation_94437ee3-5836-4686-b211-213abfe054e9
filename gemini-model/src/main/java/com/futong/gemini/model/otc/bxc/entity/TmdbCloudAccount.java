package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB云账号
 */
@Data
@OTC(value = "tmdb_cloud_account",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbCloudAccount extends CiBiz {

    //父账号ID
    private String parent_id;
    //云账号名称
    private String account_name;
    //服务地址
    private String server_url;
    //云类型编码
    private String cloud_type;
    //云版本
    private String cloud_version;
    //认证标识
    private String access_key;
    //认证秘钥
    private String secret_access_key;
    //JSON信息
    private String info_json;
    //账号类型
    private String type;
    //success为正常, stop为停用
    private String status;
    //创建时间
    private Long create_time;
    //修改时间
    private Long update_time;
    //备用字段1
    private String extend1;
    //备用字段2
    private String extend2;
    //备用字段3
    private String extend3;
    //健康状态OK 正常 error异常
    private String health_status;

}
