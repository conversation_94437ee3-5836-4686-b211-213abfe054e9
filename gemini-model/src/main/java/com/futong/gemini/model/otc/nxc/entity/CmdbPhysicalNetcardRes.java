package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_物理网卡
 */
@Data
@OTC(value = "cmdb_physical_netcard_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_物理网卡")
public class CmdbPhysicalNetcardRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //网卡分类
    private String category;
    //网卡类型
    private String type;
    //网卡型号
    private String module;
    //IPv4地址
    private String ipv4_address;
    //IPv4地址
    private String ipv6_address;
    //dns网关
    private String dns_config;
    //网卡状态
    private String status;
    //原始状态
    private String open_status;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //MAC地址
    private String mac_address;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
