package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_上行链路组
 */
@Data
@OTC(value = "cmdb_uplink_port_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_上行链路组")
public class CmdbUplinkPortRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //实例状态
    private String status;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //接收包速度(packet/s)
    private Float receive_packet;
    //发送包速度(packet/s)
    private Float send_packet;
    //接收字节速度(KB/s)
    private Float receive_byte;
    //发送字节速度(KB/s)
    private Float send_byte;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //子账号
    private String sub_account_id;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
