package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 商品维度数据集
 */
@Data
@OTC(value = "dws_goods_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsGoodsResourceAnalysis {

    //主键ID
    private String res_id;
    //服务（商品）ID
    private String goods_id;
    //服务名称（商品名称）
    private String goods_name;
    //服务（商品）数量
    private Integer goods_num;
    //服务（商品）费用
    private Double goods_price;
    //日期
    private String datadate;
    //更新时间
    private String update_time;

}
