package com.futong.gemini.model.otc.gjc.entity;

import lombok.Data;

/**
 * 任务信息对象
 * 用于存储云平台的任务/操作记录
 */
@Data
public class TaskInfoBean {
    
    /**
     * ES中的唯一id
     */
    private String id;
    
    /**
     * 云账号ID
     */
    private String accountId;
    
    /**
     * 云平台类型
     */
    private String cloudType;
    
    /**
     * 资源ID（加密后的ID）
     */
    private String resId;
    
    /**
     * 云平台原始任务标识
     */
    private String openId;
    
    /**
     * 云平台原始任务名称
     */
    private String openName;

    /**
     * 云平台原始状态
     */
    private String openStatus;

    /**
     * 云平台原始任务类型
     */
    private String openTaskType;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态
     */
    private String taskStatus;
    
    /**
     * 关联的资源类型
     */
    private String resourceType;

    /**
     * 任务详情/描述
     */
    private String detail;

    /**
     * 任务开始时间
     */
    private String beginTime;

    /**
     * 任务结束时间
     */
    private String endTime;

    /**
     * 任务持续时间
     */
    private Double duration;
    
    /**
     * 任务消息/错误信息
     */
    private String taskMessage;

    /**
     * 数据来源
     */
    private String dataSource;
    
    /**
     * 其他JSON信息
     */
    private String jsonInfo;
}
