package com.futong.gemini.model.otc.common.base;


public interface CloudEnum {
    enum Type {
        PUBLIC_ALI("public_ali"), PRIVATE_CAS("private_cas");
        private String value;

        Type(String value) {
            this.value = value;
        }

        public String getValue() {
            return name().toLowerCase();
        }

        // 根据 value 反向获取枚举实例
        public static Type fromValue(String value) {
            for (Type type : values()) {
                if (type.getValue().equalsIgnoreCase(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("No color with value " + value);
        }
    }
}
