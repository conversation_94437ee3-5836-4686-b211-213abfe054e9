package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图资产配置
 */
@Data
@OTC(value = "ca_datahub_property_config",type = OtcEnum.MessageType.NXC, version = "1", comment="云图资产配置")
public class CaDatahubPropertyConfig {

    //主键
    private String id;
    //原始主键信息
    private String open_id;
    //策略名称
    private String name;
    //创建时间
    private Long open_create_time;
    //修改时间
    private Long open_update_time;
    //启动文件
    private String start_file;
    //版本ID
    private String property_version_id;
    //资产镜像ID
    private String property_image_id;
    //资产镜像版本ID
    private String property_image_version_id;
    //任务类型ID
    private String task_type_id;
    //启动镜像ID
    private String image_id;
    //资产ID
    private String propery_id;
    //环境变量JSON
    private String train_env_params;
    //超参配置JSON
    private String train_hyper_params;
    //原始JSON
    private String open_json;
    //云账号ID
    private String cloud_account_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    private String md5_str;
    //同步时间
    private Long sync_time;
    //云类型
    private String cloud_type;
    //资产类型：数据集dataset 模型model 算法algo 镜像image
    private String property_type;
    //子云账号id
    private String sub_account_id;

}
