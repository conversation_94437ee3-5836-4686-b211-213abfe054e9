package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_对象存储文件
 */
@Data
@OTC(value = "cmdb_bucket_file_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_对象存储文件")
public class CmdbBucketFileRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //文件类型
    private String type;
    //访问权限
    private String acl;
    //存储类型
    private String storage_type;
    //文件大小
    private Float file_size;
    //排序
    private Integer list_display_sort;
    //访问文件的url
    private String url;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
