package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_cloud_type",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaCloudType {

    //主键ID
    private Long id;
    //云类型
    private String cloud_type;
    //云名称
    private String cloud_type_name;

}
