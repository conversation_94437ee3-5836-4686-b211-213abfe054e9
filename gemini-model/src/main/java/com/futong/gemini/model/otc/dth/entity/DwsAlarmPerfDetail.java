package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 告警历史统计
 */
@Data
@OTC(value = "dws_alarm_perf_detail",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsAlarmPerfDetail  {

    private String id;
    //平台
    private String server_ip;
    //创建时间
    private Long create_time;
    //级别
    private String level;
    //数量
    private Integer count;
    //删除标记
    private Integer flag;

}
