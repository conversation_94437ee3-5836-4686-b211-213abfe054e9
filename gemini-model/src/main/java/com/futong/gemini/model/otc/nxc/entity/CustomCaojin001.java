package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * caojin
 */
@Data
@OTC(value = "custom_caojin_001",type = OtcEnum.MessageType.NXC, version = "1", comment="caojin")
public class CustomCaojin001 extends CiResCloud {

    //用户名
    private String username;

}
