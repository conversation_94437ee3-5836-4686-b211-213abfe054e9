package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_field",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaField {

    //主键ID
    private Long id;
    //字段标识
    private String field_id;
    //字段名称
    private String field_name;
    //字段Json
    private String field_json;
    //内置
    private Integer builtin;
    //用户提示
    private Integer user_prompt;
    //字段分组
    private String field_group_id;
    //组件ID
    private Long view_component_id;
    //数据控制
    private String data_control;
    //是否是链接
    private Integer is_link;
    //是否是密码
    private Integer is_password;
    //样式
    private String styles;

}
