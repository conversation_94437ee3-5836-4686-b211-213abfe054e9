package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_虚拟防火墙
 */
@Data
@OTC(value = "cmdb_vfw_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_虚拟防火墙")
public class CmdbVfwRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //实例状态
    private String status;
    //原始状态
    private String open_status;
    //描述
    private String desc;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //过期时间
    private Long expire_time;
    //子账号
    private String sub_account_id;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
