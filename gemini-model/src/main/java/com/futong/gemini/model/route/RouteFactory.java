package com.futong.gemini.model.route;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.model.*;
import com.futong.gemini.model.route.api.ApiRoute;
import com.futong.gemini.model.route.mq.RabbitRoute;
import lombok.extern.slf4j.Slf4j;

import static cn.hutool.extra.spring.SpringUtil.getBean;

@Slf4j
public class RouteFactory {
    public static class Bean {
        public static RouteConfiguration config = getBean(RouteConfiguration.class);
    }

    public static BaseResponse routeMessage(OtcMessage message) {
        RouteInfo routeInfo = chooseInfo(message.getMessageType(), message.getTable());
        return routeMessage(message, routeInfo);
    }

    public static BaseResponse routeRefreshMessage(OtcRefreshMessage message) {
        return RabbitRoute.routeMessage(message, Bean.config.getRefresh());
    }


    public static BaseResponse routeAetMessage(OtcAetMessage message) {
        return RabbitRoute.routeMessage(message, Bean.config.getGjcAet());
    }

    public static BaseResponse routePrefMessage(OtcPerfMessage message) {
        return RabbitRoute.routeMessage(message, Bean.config.getGjcPerf());
    }

    public static BaseResponse routeDsPrefMessage(OtcPerfMessage message) {
        return RabbitRoute.routeMessage(message, Bean.config.getGjcDsPerf());
    }

    public static BaseResponse routeNicPrefMessage(OtcPerfMessage message) {
        return RabbitRoute.routeMessage(message, Bean.config.getGjcNicPerf());
    }

    public static BaseResponse routeBillMessage(OtcBillMessage message) {
        return RabbitRoute.routeMessage(message, Bean.config.getGjcBill());
    }

    public static BaseResponse routeAtlasMessage(AtlasMessage message) {
        RouteInfo routeInfo = new RouteInfo("cmp_atlas_exchange", "cmp_atlas_resource_routing");
//        RouteInfo routeInfo1 = new RouteInfo("cmp_atlas_exchange", "cmp_atlas_resource_test");
//        routeAtlasMessage(message, routeInfo1);
        return routeAtlasMessage(message, routeInfo);
    }

    public static BaseResponse routeAtlasPerfMessage(AtlasMessage message) {
        RouteInfo routeInfo = new RouteInfo("cmp_atlas_exchange", "cmp_gpu_rate");
//        RouteInfo routeInfo1 = new RouteInfo("cmp_atlas_exchange", "cmp_atlas_resource_test");
//        routeAtlasMessage(message, routeInfo1);
        log.info("发送routingkey:{}","cmp_gpu_rate");
        return routeAtlasMessage(message, routeInfo);
    }

    public static BaseResponse routeMessage(OtcMessage message, RouteInfo routeInfo) {
        if (CollUtil.isEmpty(message.getBody())) {
            log.warn("本次发送消息body为空!message:{}", JSON.toJSONString(message));
        }
        if (message.getChannel() == OtcEnum.Channel.MQ) {
            //对于MQ而言只需要发送请求
            return RabbitRoute.routeMessage(message, routeInfo);
        } else {
            //api接口则是请求并响应
            return ApiRoute.routeMessage(message);
        }
    }

    public static BaseResponse routeAtlasMessage(AtlasMessage message, RouteInfo routeInfo) {
        if (CollUtil.isEmpty(message.getBody())) {
            log.warn("本次发送消息body为空!message:{}", JSON.toJSONString(message));
        }
        if (message.getChannel() == OtcEnum.Channel.MQ) {
            log.info("发送mq----routingkey:{}",routeInfo.getRoutingKey());
            //对于MQ而言只需要发送请求
            return RabbitRoute.routeMessage(message, routeInfo);
        } else {
            //api接口则是请求并响应
            return ApiRoute.routeAtlasMessage(message);
        }
    }

    public static RouteInfo chooseInfo(OtcEnum.MessageType messageType, String table) {
        switch (messageType) {
            case NXC:
                if ("meta_data_relation".equals(table)) {
                    return Bean.config.getNxcRelation();
                }
                if ("cmp_resource_perf".equals(table)) {
                    return Bean.config.getNxcPerf();
                }
                return Bean.config.getNxc();
            case BXC:
                return Bean.config.getBxc();
            case GJC:
                return Bean.config.getGjc();
            case DTH:
                return Bean.config.getDth();
            case ATLAS:
                return Bean.config.getAtlas();
            default:
                throw new BaseException(BaseResponse.FAIL_PARAM_EXISTS, "无此消息类型" + messageType.name());
        }
    }

}
