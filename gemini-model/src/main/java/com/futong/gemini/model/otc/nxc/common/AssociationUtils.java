package com.futong.gemini.model.otc.nxc.common;

import cn.hutool.core.util.StrUtil;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class AssociationUtils {
    public static String otcTableName(CiResCloud ci) {
        OTC annotation = ci.getClass().getAnnotation(OTC.class);
        return StrUtil.emptyToDefault(annotation.value(), ci.getClass().getSimpleName());
    }

    public static String otcTableName(Class<?> clazz) {
        OTC annotation = clazz.getAnnotation(OTC.class);
        return StrUtil.emptyToDefault(annotation.value(), clazz.getSimpleName());
    }

    public static Association toAssociation(CiResCloud node1, CiResCloud node2) {
        Association association = new Association();
        association.setNode1(otcTableName(node1));
        association.setNode1Id(node1.getRes_id());
        association.setNode2(otcTableName(node2));
        association.setNode2Ids(Arrays.asList(node2.getRes_id()));
        return association;
    }

    public static Association toAssociation(CiResCloud node1, Class<?> node2clazz, String node2Id) {
        Association association = new Association();
        association.setNode1(otcTableName(node1));
        association.setNode1Id(node1.getRes_id());
        association.setNode2(otcTableName(node2clazz));
        association.setNode2Ids(Arrays.asList(node2Id));
        return association;
    }

    public static Association toAssociation(CiResCloud node1, List<CiResCloud> node2s) {
        Association association = new Association();
        association.setNode1(otcTableName(node1));
        association.setNode1Id(node1.getRes_id());
        association.setNode2(otcTableName(node2s.get(0)));
        association.setNode2Ids(node2s.stream().map(CiResCloud::getRes_id).collect(Collectors.toList()));
        return association;
    }

    public static Association toAssociation(CiResCloud node1, Class<?> node2clazz, String node2Id, String extTey, String extData) {
        Association association = new Association();
        association.setNode1(otcTableName(node1));
        association.setNode1Id(node1.getRes_id());
        association.setNode2(otcTableName(node2clazz));
        association.setNode2Ids(Arrays.asList(node2Id));
        association.setExt_key(extTey);
        association.setExt_data(extData);
        return association;
    }




}
