package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图文件
 */
@Data
@OTC(value = "ca_datahub_file",type = OtcEnum.MessageType.NXC, version = "1", comment="云图文件")
public class CaDatahubFile {

    //主键
    private String id;
    //原始主键信息
    private String open_id;
    //文件大小
    private Double file_size;
    //文件名称
    private String file_name;
    //类型;私有文件1 公共文件 共享文件
    private String type;
    //文件路径
    private String file_path;
    //更新时间
    private Long open_update_time;
    //上传时间
    private Long open_upload_time;
    //父级ID
    private String open_parent_id;
    //文件类型
    private String file_type;
    //文件后缀
    private String file_suffix;
    //原始JSON
    private String open_json;
    //云账号ID
    private String cloud_account_id;
    //可执行权限;1可执行 0不可执行
    private String is_directory;
    //创建人
    private String open_create_user_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    private String md5_str;
    //同步时间
    private Long sync_time;
    //云类型
    private String cloud_type;
    //子云账号id
    private String sub_account_id;

}
