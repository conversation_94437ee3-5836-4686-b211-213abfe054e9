package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 开发环境
 */
@Data
@OTC(value = "ca_dev_env",type = OtcEnum.MessageType.NXC, version = "1", comment="开发环境")
public class CaDevEnv {

    //主键
    private String id;
    //原始主键id
    private String open_id;
    //名称
    private String name;
    //镜像ID
    private String image_id;
    //镜像版本ID
    private String image_version_id;
    //镜像地址
    private String image_url;
    //镜像类型
    private String image_type;
    //资源组ID
    private String resource_group_id;
    //CPU
    private Integer cpu;
    //内存
    private Double memory;
    //GPU整卡个数
    private Integer gpu;
    //vGpu卡数
    private Integer vgpu;
    //vGpu显存
    private Integer vgpu_mem;
    //是否开启ssh
    private String enable_ssh;
    //vGpu算力
    private String vgpu_cores;
    //认证方式
    private String auth_type;
    //ssh密钥对ID
    private String key_pair_id;
    //是否开启共享内存
    private String enable_shared_memory;
    //开发环境状态
    private String status;
    //开发环境原始状态
    private String open_status;
    //Jupyter访问路径
    private String web_path;
    //ssh用户名
    private String ssh_username;
    //ssh登录密码
    private String ssh_password;
    //ssh端口
    private String ssh_port;
    //ssh远程IP
    private String ssh_service_ip;
    //原始JSON
    private String open_json;
    //同步时间
    private Long sync_time;
    //失败原因
    private String error_reason;
    //云账号ID
    private String cloud_account_id;
    //创建人
    private String open_create_user_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    private String md5_str;
    //原始创建时间
    private Long open_create_time;
    //云类型
    private String cloud_type;
    //子云账号id
    private String sub_account_id;

}
