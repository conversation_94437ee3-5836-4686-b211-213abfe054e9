package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 任务实例日志
 */
@Data
@OTC(value = "ca_task_instance_log",type = OtcEnum.MessageType.NXC, version = "1", comment="任务实例日志")
public class CaTaskInstanceLog {

    //ID
    private String id;
    //原始数据ID
    private String open_id;
    //任务实例id
    private String task_instance_id;
    //日志内容
    private String log_content;
    //原始创建时间
    private Long open_create_time;
    //同步时间
    private Long sync_time;
    //是否删除（1是 2否）
    private Integer is_delete;
    //更新时间
    private Long delete_time;
    //云账号id
    private String cloud_account_id;
    //md5字符串
    private String md5_str;
    //原始json字符串
    private String open_json;
    //云类型
    private String cloud_type;
    //子云账号id
    private String sub_account_id;

}
