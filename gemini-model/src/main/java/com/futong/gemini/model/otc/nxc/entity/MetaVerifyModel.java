package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_verify_model",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaVerifyModel {

    //主键ID
    private Long id;
    //模型标识
    private String model_id;
    //校验字段
    private String columns;

}
