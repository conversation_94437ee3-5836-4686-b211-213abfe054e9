package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_Polardbx
 */
@Data
@OTC(value = "cmdb_polardbx",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_Polardbx")
public class CmdbPolardbx extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //实例系列
    private String category;
    //实例类型
    private String db_instance_type;
    //实例网络类型
    private String instance_network_type;
    //描述
    private String desc;
    //实例状态
    private String status;
    //原始状态
    private String open_status;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
