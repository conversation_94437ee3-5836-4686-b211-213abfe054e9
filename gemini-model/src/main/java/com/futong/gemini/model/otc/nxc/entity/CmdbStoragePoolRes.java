package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_存储池
 */
@Data
@OTC(value = "cmdb_storage_pool_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_存储池")
public class CmdbStoragePoolRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //存储状态
    private String status;
    //原始状态
    private String open_status;
    //类型
    private String type;
    //总容量(GB)
    private Float total_size;
    //已使用容量(GB)
    private Float used_size;
    //已分配容量(GB)
    private Float allocation_size;
    //置备容量(GB)
    private Float provision_size;
    //描述
    private String desc;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //子账号
    private String sub_account_id;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;
    //路径
    private String path;
    //磁盘分配模式
    private Integer volume_layout_mode;
    //是否精简
    private String is_thin;
    //扫描时间
    private Long refresh_time;

}
