package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 服务器状态统计
 */
@Data
@OTC(value = "dws_server_status_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsServerStatusAnalysis  {

    private String id;
    //平台
    private String server_ip;
    //状态
    private String status;
    //数量
    private Integer count;
    //删除标记
    private Integer flag;

}
