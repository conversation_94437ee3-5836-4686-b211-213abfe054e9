package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_物理机
 */
@Data
@OTC(value = "cmdb_host_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_物理机")
public class CmdbHostRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //物理机编号
    private String asset_num;
    //物理机型号
    private String model;
    //序列号
    private String sn;
    //厂商
    private String manufacturer;
    //质保年限
    private String warranty_period;
    //服务代码
    private String service_code;
    //物理机状态
    private String status;
    //原始状态
    private String open_status;
    //物理机IP
    private String ip;
    //告警状态
    private String alarm_status;
    //CPU核数
    private Integer cpu_size;
    //内存大小(MB)
    private Integer mem_size;
    //网络在线
    private String on_line;
    //维护模式
    private String maintain_mode;
    //运行时间(H)
    private Integer run_time;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //最后时间
    private Long last_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;
    //总容量(GB)
    private Float total_size;
    //已使用容量(GB)
    private Float used_size;
    //已分配容量(GB)
    private Float allocation_size;

}
