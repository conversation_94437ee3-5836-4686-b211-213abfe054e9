package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB运维侧关系表
 */
@Data
@OTC(value = "tmdb_devops_link",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbDevopsLink extends CiBiz {

    //运维侧ID
    private String devops_id;
    //运维侧父ID
    private String parent_devops_id;
    //创建时间
    private Long create_time;
    //修改时间
    private Long update_time;
    private Integer flag;

}
