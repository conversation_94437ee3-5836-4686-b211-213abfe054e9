package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_resource_view",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaResourceView {

    //主键ID
    private String id;
    //视图名称
    private String resource_name;
    //云类型
    private String cloud_type;
    //云账号
    private String account_id;
    //操作时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //来源
    private String source;
    //描述
    private String desc;
    private String body;

}
