package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;

@Data
@OTC(value = "meta_batch_message",type = OtcEnum.MessageType.BXC, version = "1")
public class MetaBatchMessage extends CiBiz {

    private Long id;
    //云类型
    private String cloud_type;
    //数据库
    private String message_type;
    private String db;
    //资源表
    private String model_table;
    private String dispatch_id;
    private String batch_id;
    //版本
    private String version;
    private String body;
    //创建时间
    private Long create_time;
    //云账号
    private String account_id;
    //超时时间
    private Integer timeout;

}
