package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 告警数据统计
 */
@Data
@OTC(value = "dws_alarm_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsAlarmAnalysis  {

    private String id;
    //平台地址
    private String server_ip;
    //紧急告警数
    private Integer serious_num;
    //重要告警数
    private Integer important_num;
    //次要告警数
    private Integer minor_num;
    //警告数
    private Integer warning_num;
    //事件数
    private Integer event_num;
    //删除标记
    private Integer flag;

}
