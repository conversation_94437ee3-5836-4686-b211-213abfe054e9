package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_tag_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsTagResourceAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //标签值ID
    private String key_id;
    //标签键ID
    private String tag_key;
    //标签键
    private String tag_name;
    //标签值
    private String tag_value;
    //账号下的服务器ID
    private String vm_ids;
    //账号下的服务器开机ID
    private String vm_running_ids;
    //账号下的服务器关机ID
    private String vm_stopping_ids;
    //账号下的服务器未知ID
    private String vm_unknown_ids;
    //账号下的用户ID
    private String user_ids;
    //账号下的租户ID
    private String company_ids;
    //账号下的组织ID
    private String dept_ids;
    //账号下的业务系统ID
    private String project_ids;
    //账号下的标签ID
    private String tag_values;
    //账号下的云账号ID
    private String cloud_account_ids;
    //用户数
    private Integer user_nums;
    //租户数
    private Integer company_nums;
    //组织数
    private Integer dept_nums;
    //业务系统数
    private Integer project_nums;
    //标签值数
    private Integer tag_nums;
    //云账号数
    private Integer cloud_account_nums;
    //云主机数(台)
    private Integer vm_nums;
    //云主机开机数(台)
    private Integer vm_running_nums;
    //云主机关机数(台)
    private Integer vm_stopping_nums;
    //云主机未知数(台)
    private Integer vm_unknown_nums;
    //CPU核数(核)
    private Double vm_cpu_size;
    //内存大小(GB)
    private Double vm_memory_size;
    //存储大小(GB)
    private Double vm_disk_size;
    //已使用CPU核数(核)
    private Double vm_cpu_size_used;
    //已使用内存大小(GB)
    private Double vm_memory_size_used;
    //已使用存储大小(GB)
    private Double vm_disk_size_used;
    //未使用CPU核数(核)
    private Double vm_cpu_size_unused;
    //未使用内存大小(GB)
    private Double vm_memory_size_unused;
    //未使用存储大小(GB)
    private Double vm_disk_size_unused;
    //已使用最大CPU核数(核)
    private Double vm_max_cpu_size_used;
    //已使用最大内存大小(GB)
    private Double vm_max_memory_size_used;
    //已使用最大存储大小(GB)
    private Double vm_max_disk_size_used;
    //未使用最大CPU核数(核)
    private Double vm_max_cpu_size_unused;
    //未使用最大内存大小(GB)
    private Double vm_max_memory_size_unused;
    //未使用最大存储大小(GB)
    private Double vm_max_disk_size_unused;
    //已使用最小CPU核数(核)
    private Double vm_min_cpu_size_used;
    //已使用最小内存大小(GB)
    private Double vm_min_memory_size_used;
    //已使用最小存储大小(GB)
    private Double vm_min_disk_size_used;
    //未使用最小CPU核数(核)
    private Double vm_min_cpu_size_unused;
    //未使用最小内存大小(GB)
    private Double vm_min_memory_size_unused;
    //未使用最小存储大小(GB)
    private Double vm_min_disk_size_unused;
    //云主机CPU平均使用率(%)
    private Double vm_avg_cpu_usage;
    //云主机内存平均使用率(%)
    private Double vm_avg_memory_usage;
    //云主机存储平均使用率(%)
    private Double vm_avg_disk_usage;
    //云主机CPU最大使用率(%)
    private Double vm_max_cpu_usage;
    //云主机内存最大使用率(%)
    private Double vm_max_memory_usage;
    //云主机存储最大使用率(%)
    private Double vm_max_disk_usage;
    //云主机CPU最小使用率(%)
    private Double vm_min_cpu_usage;
    //云主机内存最小使用率(%)
    private Double vm_min_memory_usage;
    //云主机存储最小使用率(%)
    private Double vm_min_disk_usage;
    //更新时间
    private String update_time;

}
