package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB运维侧授权
 */
@Data
@OTC(value = "tmdb_devops_tenant",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbDevopsTenant extends CiBiz {

    //运维侧ID
    private String devops_id;
    //租户侧ID
    private String tenant_id;
    //授权方向;D_TO_T, T_TO_D, ALL
    private String direction;
    //创建时间
    private Long create_time;
    //修改时间
    private Long update_time;

}
