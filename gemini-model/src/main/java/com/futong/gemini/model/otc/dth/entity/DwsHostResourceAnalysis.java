package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_host_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsHostResourceAnalysis  {

    //主键ID
    private String res_id;
    //日期
    private String datadate;
    //cmpID
    private String cmp_id;
    //用户ID
    private String user_id;
    //用户名称
    private String user_name;
    //租户ID
    private String company_id;
    //租户名称
    private String company_name;
    //组织ID
    private String dept_id;
    //组织名称
    private String dept_name;
    //业务系统ID
    private String project_id;
    //业务系统名称
    private String project_name;
    //标签key
    private String tag_key;
    //标签value
    private String tag_value;
    //云账号id
    private String cloud_account_id;
    //云账号名称
    private String cloud_account_name;
    //云类型
    private String cloud_type;
    //云类型名称
    private String cloud_type_name;
    //物理机ID
    private String host_id;
    //server_ip
    private String server_ip;
    //集群ID
    private String cluster_id;
    //云主机名称
    private String host_name;
    //云主机IP
    private String host_ip;
    //物理机数(台)
    private Integer host_num;
    //物理机开机数(台)
    private Integer host_running_num;
    //物理机关机数(台)
    private Integer host_stopping_num;
    //物理机未知数(台)
    private Integer host_unknown_num;
    //物理机CPU核数(核)
    private Double host_cpu_size;
    //物理机内存大小(GB)
    private Double host_memory_size;
    //物理机存储大小(GB)
    private Double host_disk_size;
    //物理机已使用CPU核数(核)
    private Double host_cpu_size_used;
    //物理机已使用内存大小(GB)
    private Double host_memory_size_used;
    //物理机已使用存储大小(GB)
    private Double host_disk_size_used;
    //物理机未使用CPU核数(核)
    private Double host_cpu_size_unused;
    //物理机未使用内存大小(GB)
    private Double host_memory_size_unused;
    //物理机未使用存储大小(GB)
    private Double host_disk_size_unused;
    //物理机本地存储总量(GB)
    private Double host_local_storage_size;
    //物理机本地存储已用量(GB)
    private Double host_local_storage_used;
    //物理机本地存储剩余量(GB)
    private Double host_local_storage_free;
    //物理机共享存储总量(GB)
    private Double host_shared_storage_size;
    //物理机共享存储已用量(GB)
    private Double host_shared_storage_used;
    //物理机共享存储剩余量(GB)
    private Double host_shared_storage_free;
    //物理机存储总量本地+共享(GB)
    private Double host_storage_size;
    //物理机CPU平均使用率(%)
    private Double host_avg_cpu_usage;
    //物理机内存平均使用率(%)
    private Double host_avg_memory_usage;
    //物理机存储平均使用率(%)
    private Double host_avg_disk_usage;
    //物理机CPU最大使用率(%)
    private Double host_max_cpu_usage;
    //物理机内存最大使用率(%)
    private Double host_max_memory_usage;
    //物理机存储最大使用率(%)
    private Double host_max_disk_usage;
    //物理机CPU最小平使用率(%)
    private Double host_min_cpu_usage;
    //物理机内存最小使用率(%)
    private Double host_min_memory_usage;
    //物理机存储最小使用率(%)
    private Double host_min_disk_usage;
    //更新时间
    private String update_time;

}
