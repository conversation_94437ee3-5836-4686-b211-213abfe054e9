package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图模型评估任务
 */
@Data
@OTC(value = "ca_large_model_evaluate_task",type = OtcEnum.MessageType.NXC, version = "1", comment="云图模型评估任务")
public class CaLargeModelEvaluateTask {

    //主键ID
    private String id;
    //原始主键ID
    private String open_id;
    //租户号
    private String tenant_id;
    //乐观锁
    private Integer revision;
    //创建人
    private String open_create_user_id;
    //创建时间
    private Long created_time;
    //更新人
    private String updated_user;
    //更新时间
    private Long updated_time;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    private String md5_str;
    //同步时间
    private String sync_time;
    //云类型
    private String cloud_type;
    //云账号ID
    private String cloud_account_id;
    //子云账号id
    private String sub_account_id;

}
