package com.futong.gemini.model.route.mq;

import cn.hutool.extra.spring.SpringUtil;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.mq.FTRabbitUtils;
import com.futong.gemini.model.route.RouteInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

@Slf4j
public class RabbitRoute {

    public static class Bean {
        public static RabbitTemplate template = SpringUtil.getBean(RabbitTemplate.class);
    }

    //发送MQ消息
    public static BaseResponse routeMessage(Object message, RouteInfo rabbitInfo) {
        return routeMessage(rabbitInfo.getExchange(), rabbitInfo.getRoutingKey(), message);
    }

    //发送MQ消息
    public static BaseResponse routeMessage(String exchange, String routingKey, Object message) {
        try {
            log.info("[MQ发送] exchange: {}, routingKey: {}", exchange, routingKey); // 新增日志
            FTRabbitUtils.sendMessage(exchange, routingKey, message, "data");
        } catch (Exception e) {
            log.error("发送MQ消息失败", e);
            throw new BaseException(BaseResponse.ERROR_SYS, e, "发送MQ消息失败");
        }
        return BaseResponse.SUCCESS;
    }

    //接收MQ消息
    public static BaseResponse receiveMessage(String queueName) {
        Message message;
        try {
            message = Bean.template.receive(queueName);
        } catch (Exception e) {
            log.error("发送MQ消息失败", e);
            throw new BaseException(BaseResponse.ERROR_SYS, e, "发送MQ消息失败");
        }
        return new BaseDataResponse().withData(message);
    }
}
