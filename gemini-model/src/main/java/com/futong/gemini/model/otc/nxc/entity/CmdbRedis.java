package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_Redis
 */
@Data
@OTC(value = "cmdb_redis",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_Redis")
public class CmdbRedis extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //实例容量
    private String capacity;
    //引擎版本
    private String engine_version;
    //是否属RDS管控
    private String rds_control;
    //类型
    private String type;
    //架构类型
    private String architecture_type;
    //实例的规格
    private String instance_flaver;
    //版本类型
    private String edition_type;
    //网络类型
    private String network_type;
    //内网连接地址
    private String connection_domain;
    //端口
    private String port;
    //实例的访问模式
    private String connection_type;
    //实例的连接数限制
    private String connections;
    //备可用区ID
    private String secondary_zone_id;
    //实例的参数详情
    private String config;
    //集群的数据节点数量
    private String shard_count;
    //连接使用的用户名
    private String user_name;
    //分布式实例ID
    private String global_instance_id;
    //节点类型
    private String node_type;
    //销毁实例的时间
    private String destroy_time;
    //实例状态
    private String status;
    //原始状态
    private String open_status;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
