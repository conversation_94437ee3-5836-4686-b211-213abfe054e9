package com.futong.gemini.model.otc.bxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiBiz;
import lombok.Data;


/**
 * TMDB云管平台资源标签
 */
@Data
@OTC(value = "tmdb_resource_item_set",type = OtcEnum.MessageType.BXC, version = "1")
public class TmdbResourceItemSet extends CiBiz {

    //业务资源类型
    private String biz_resource_type;
    //云资源ID
    private String res_id;
    //云账号ID
    private String account_id;
    //云类型
    private String cloud_type;
    //CMDB资源类型
    private String res_resource_type;
    //业务资源JSON信息
    private String res_resource_json;
    //其他标签1
    private String tags01;
    //其他标签2
    private String tags02;
    //其他标签3
    private String tags03;
    //其他标签4
    private String tags04;
    //其他标签5
    private String tags05;
    //状态
    private Integer status;
    //删除时间
    private Long delete_time;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    private Integer flag;

}
