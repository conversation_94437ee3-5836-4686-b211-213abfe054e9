package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图资产
 */
@Data
@OTC(value = "ca_datahub_property",type = OtcEnum.MessageType.NXC, version = "1", comment="云图资产")
public class CaDatahubProperty {

    //主键
    private String id;
    //原始主键信息
    private String open_id;
    //原始名称
    private String name;
    //原始描述
    private String detail;
    //原始更新时间
    private Long open_update_time;
    //是否删除
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //是否预置
    private String is_preset;
    //资产类型：数据集dataset 模型model 算法algo 镜像image
    private String property_type;
    //创建人
    private String open_create_user_id;
    //原始JSON
    private String open_json;
    //云账号ID
    private String cloud_account_id;
    //云类型
    private String cloud_type;
    private String md5_str;
    //同步时间
    private Long sync_time;
    //版本id
    private String version_id;
    //子云账号id
    private String sub_account_id;
    //是否收藏 1收藏 0 未收藏
    private Integer collections;

}
