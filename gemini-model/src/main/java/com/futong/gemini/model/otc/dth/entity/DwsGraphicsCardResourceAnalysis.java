package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 显卡维度数据集
 */
@Data
@OTC(value = "dws_graphics_card_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsGraphicsCardResourceAnalysis {

    //主键ID
    private String res_id;
    //显卡总数量
    private Integer total_num;
    //N显卡数量
    private Integer n_total_num;
    //国产显卡数量
    private Integer g_total_num;
    //N卡已使用数量
    private Integer n_total_used_num;
    //国产卡已使用数量
    private Integer g_total_used_num;
    //总算力
    private Double total_tflops;
    //N卡算力
    private Double n_total_tflops;
    //国产卡算力
    private Double g_total_tflops;
    //国产化适配率
    private Double filter_rate;
    //算力资源利用率
    private Double graphics_card_usage;
    //N卡算力资源利用率
    private Double n_graphics_card_usage;
    //国产卡算力资源利用率
    private Double g_graphics_card_usage;
    //日期
    private String datadate;
    //更新时间
    private String update_time;

}
