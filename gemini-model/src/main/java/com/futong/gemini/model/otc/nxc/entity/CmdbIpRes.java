package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_IP
 */
@Data
@OTC(value = "cmdb_ip_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_IP")
public class CmdbIpRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //IP类型
    private String type;
    //MAC地址
    private String mac;
    //IP地址
    private String address;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
