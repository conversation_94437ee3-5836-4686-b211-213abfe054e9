package com.futong.gemini.model.otc.nxc.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

/**
 * @ClassName: NicDetailBean
 * @description: 网卡
 * @author: 杨力
 * @create: 2024-12-11 14:40
 * @version: v4.0
 **/
@Data
@OTC(value = "cmp_nic_detail",type = OtcEnum.MessageType.NXC, version = "1", comment="网卡性能信息表")
public class NicDetailBean {

    /***
     * 主机或者云主机resID
     */
    private String resId;
    /***
     * 资源类型
     */
    private String resourceType;
    /**
     * 关联网卡的resId
     */
    private String nicResId;
    /**
     * 云类型
     */
    private String cloudType;
    /**
     * 云账号
     */
    private String accountId;
    /**
     * 读速率
     */
    private Double netIn = 0D;
    /**
     * 写速率
     */
    private Double netOut = 0D;

    /**
     * 记录时间
     */
    private String createTime;

}
