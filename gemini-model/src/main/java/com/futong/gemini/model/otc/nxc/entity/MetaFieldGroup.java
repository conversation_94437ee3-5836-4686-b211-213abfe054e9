package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_field_group",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaFieldGroup {

    //主键ID
    private Long id;
    //字段分组
    private String field_group_id;
    //字段分组名称
    private String field_group_name;

}
