package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_快照
 */
@Data
@OTC(value = "cmdb_snapshot_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_快照")
public class CmdbSnapshotRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //快照大小(GB)
    private Float size;
    //快照状态
    private String status;
    //原始状态
    private String open_status;
    //快照类型
    private String type;
    //快照父节点ID
    private String p_snaps_id;
    //快照父节点名称
    private String p_snaps_name;
    //运行状态
    private String running_status;
    //快照对象
    private String target;
    //是否加密
    private Boolean encrypted;
    //快照进度
    private String progress;
    //描述
    private String desc;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
