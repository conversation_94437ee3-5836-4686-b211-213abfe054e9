package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_income_resource_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsIncomeResourceAnalysis  {
    //主键
    private String res_id;
    //日期(年)
    private String data_year;
    //日期(月)
    private String data_month;
    //日期(日)
    private String datadate;
    //服务器ID
    private String cmp_id;
    //云厂商
    private String cloud_type;
    //云厂商名称
    private String cloud_type_name;
    //云账号ID
    private String cloud_account_id;
    //云账号
    private String cloud_account_name;
    //用户ID
    private String user_id;
    //用户名称
    private String user_name;
    //租户ID
    private String company_id;
    //租户名称
    private String company_name;
    //组织ID
    private String dept_id;
    //组织名称
    private String dept_name;
    //组织级别ID
    private String dept_level_id;
    //组织级别名称
    private String dept_level_name;
    //所属一级组织id
    private String first_level_dept_id;
    //所属一级组织名称
    private String first_level_dept_name;
    //所属二级组织id
    private String second_level_dept_id;
    //所属二级组织名称
    private String second_level_dept_name;
    //所属三级组织id
    private String third_level_dept_id;
    //所属三级组织名称
    private String third_level_dept_name;
    //所属四级组织id
    private String four_level_dept_id;
    //所属四级组织名称
    private String four_level_dept_name;
    //所属五级组织id
    private String five_level_dept_id;
    //所属五级组织名称
    private String five_level_dept_name;
    //业务系统ID
    private String project_id;
    //业务系统名称
    private String project_name;
    //资源id
    private String resource_id;
    //资源名称
    private String resource_name;
    //资源类型
    private String resource_type;
    //服务名称
    private String goods_id;
    //服务名称（商品名称）
    private String goods_name;
    //计费组件名称
    private String product_type_name;
    //计费组件
    private String product_type_code;
    //计费组件规格名称
    private String product_component_name;
    //计费组件规格
    private String product_component_id;
    //云产品
    private String policy_type;
    //标签键
    private String tag_name;
    //标签值
    private String tag_value;
    //订单id
    private String order_id;
    //订单编号
    private String order_num;
    //订单商品id
    private String order_goods_id;
    //云服务商id
    private String supplier_id;
    //云服务商名称
    private String supplier_name;
    //1正式 0测试
    private String is_test;
    //云主机费用
    private Double vm_actual_price;
    //当月费用
    private Double cur_month_actual_price;
    //标签费用
    private Double tag_actual_price;
    //租户费用
    private Double companyActualPrice;
    //业务系统费用
    private Double projectActualPrice;
    //组织费用
    private Double deptActualPrice;
    //标签产品费用
    private Double tagGoodsActualPrice;
    //租户产品费用
    private Double companyGoodsActualPrice;
    //业务系统产品费用
    private Double projectGoodsActualPrice;
    //组织产品费用
    private Double deptGoodsActualPrice;
    //标签本身费用
    private Double tag_self_price;
    //实际支付金额(元)
    private Double actual_price;
    //价格(元)
    private Double price;
    //折扣价格(元)
    private Double discount_price;
    //更新时间
    private String update_time;

}
