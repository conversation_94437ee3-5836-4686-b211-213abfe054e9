package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "meta_config_log",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class MetaConfigLog {

    //ID
    private String id;
    //用户
    private String user;
    //操作类型
    private String action;
    //操作表
    private String table;
    //操作主键
    private String primary;
    //操作内容
    private String metainfo;
    //操作时间
    private Long create_time;

}
