package com.futong.gemini.model.otc.common.model;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;

import java.util.ArrayList;
import java.util.List;

public class AtlasMessage<T> {

    private String table;//CI表名
    private String version;//模型版本
    private OtcEnum.MessageType messageType;
    private OtcEnum.Channel channel;
    private Integer timeout;//数据完结标识超时
    private List<T> body;//数据信息
    @JsonIgnore
    private OTC otc;

    public OtcEnum.MessageType getMessageType() {
        return messageType;
    }

    public void setMessageType(OtcEnum.MessageType messageType) {
        this.messageType = messageType;
    }

    public String getTable() {
        return table;
    }

    public String getVersion() {
        return version;
    }

    public List<T> getBody() {
        return body;
    }

    public OtcEnum.Channel getChannel() {
        return channel;
    }

    public void setChannel(OtcEnum.Channel channel) {
        this.channel = channel;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public <TC extends AtlasMessage<T>> TC withBody(List<T> body,Class<T> clazz) {
//        if (CollUtil.isEmpty(body)) {
//            this.body = ListUtil.empty();
//            this.channel = OtcEnum.Channel.MQ;
//            this.table = StrUtil.emptyToDefault(otc.value(), clazz.getSimpleName());
//            return (TC) this;
//        } else {
        this.body = body;
        return withClass(clazz);
//        }
    }

    public <TC extends AtlasMessage<T>> TC addBody(T t) {
        this.body = ObjectUtil.defaultIfNull(this.body, new ArrayList<>());
        this.body.add(t);
        if (StrUtil.isEmpty(this.version)) {
            withClass((Class<T>) t.getClass());
        }
        return (TC) this;
    }

    public <TC extends AtlasMessage<T>> TC withClass(Class<T> clazz) {
        otc = clazz.getAnnotation(OTC.class);
        if (otc == null) {
            throw new BaseException(BaseResponse.ERROR_BIZ, "传入模型信息须包含注解@OTC");
        }
        this.table = StrUtil.emptyToDefault(otc.value(), clazz.getSimpleName());
        this.version = otc.version();
        this.messageType = otc.type();
        this.channel = otc.channel();
        return (TC) this;
    }


}
