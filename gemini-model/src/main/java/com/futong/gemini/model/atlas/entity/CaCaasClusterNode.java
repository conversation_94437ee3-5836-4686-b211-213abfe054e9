package com.futong.gemini.model.atlas.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

/**
 * CaaS集群节点信息表
 */
@OTC(value = "ca_caas_cluster_node", type = OtcEnum.MessageType.ATLAS, version = "1", comment = "集群节点信息表")
// 删除: @OTC注解
@Data
public class CaCaasClusterNode {

    /**
     * 资源ID
     */
    private String id;

    /**
     * Open ID
     */
    private String open_id;

    /**
     * Open Name
     */
    private String name;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * CPU总核数
     */
    private Float cpu_size;

    /**
     * 已用CPU核数
     */
    private Float cpu_used;

    /**
     * 内存总量(MB)
     */
    private Float memory_size;

    /**
     * 已用内存(MB)
     */
    private Float memory_used;

    /**
     * GPU核心总数
     */
    private Float gpu_core;

    /**
     * 已用GPU核心数
     */
    private Float gpu_core_used;

    /**
     * GPU内存总量(MB)
     */
    private Float gpu_memory;

    /**
     * 已用GPU内存(MB)
     */
    private Float gpu_memory_used;

    /**
     * Pod总数
     */
    private Integer pod_count;

    /**
     * 已使用Pod数量
     */
    private Integer pod_used;

    /**
     * 是否可调度（true/false）
     */
    private Boolean schedulable;

    /**
     * 架构
     */
    private String architecture;

    /**
     * 创建时间
     */
    private Long open_create_time;

    /**
     * 描述
     */
    private String description;

    /**
     * 云类型
     */
    private String cloud_type;

    /**
     * 云账号id
     */
    private String cloud_account_id;

    /**
     * 子云账号id
     */
    private String sub_account_id;

    /**
     * 原始信息JSON
     */
    private String open_json;

    /**
     * 集群ID
     */
    private String relation_cluster;

    /**
     * 三层-集群
     */
    private String relation_devops_cluster;

}