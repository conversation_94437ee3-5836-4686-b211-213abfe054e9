package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_虚拟局域网
 */
@Data
@OTC(value = "cmdb_vlan_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_虚拟局域网")
public class CmdbVlanRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //vlan分类
    private String category;
    //vlan链路类型
    private String link_type;
    //vlan标准
    private String standard;
    //网段
    private String cidr;
    //vlan标识
    private String vlan_id;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //描述
    private String desc;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;
    //开始vlan
    private String start_vlan_id;
    //结束vlan
    private String end_vlan_id;

}
