package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 云图算力平台下拉框
 */
@Data
@OTC(value = "ca_cpp_combobox",type = OtcEnum.MessageType.NXC, version = "1", comment="云图算力平台下拉框")
public class CaCppCombobox {

    //主键
    private String id;
    //类型（下拉框数据类型）
    private String type;
    //原始主键信息
    private String open_id;
    //父级ID
    private String open_parent_id;
    //名称
    private String open_name;
    //更新时间
    private Long open_create_time;
    //创建人
    private String open_create_user_id;
    //更新时间
    private Long open_update_time;
    //原始JSON
    private String open_json;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //md5字符串
    private String md5_str;
    //同步时间
    private Long sync_time;
    //云类型
    private String cloud_type;
    //云账号ID
    private String cloud_account_id;
    //子云账号id
    private String sub_account_id;

}
