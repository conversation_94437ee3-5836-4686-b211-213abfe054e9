package com.futong.gemini.model.otc.nxc.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

/**
 * @ClassName: ResourcePerfDetail
 * @description: 资源性能数据明细
 * @author: 杨力
 * @create: 2024-09-05 10:00
 * @version: v4.0
 **/
@Data
@OTC(value = "cmp_resource_perf",type = OtcEnum.MessageType.NXC, version = "1", comment="云资源监控数据")
public class ResourcePerfDetail {
    /**
     * es中的唯一id
     */
    private String id;

    /**
     * 资源ID
     */
    private String resId;

    /**
     * 云账号ID
     */
    private String accountId;
    /**
     * 云平台原始资源标识
     */
    private String openId;
    /**
     * 云平台原始资源名称
     */
    private String openName;
    /**
     * 云平类型
     */
    private String cloudType;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 创建时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String createTime;
    private Double cpuSize;
    private Double cpuUsage;
    private Double memSize;
    private Double memUsage;
    private Double diskSize;
    private Double diskUsage;
    private Double netIo;
    private Double netIn;
    private Double netOut;
    private Double diskIo;
    private Double diskWrite;
    private Double diskRead;
    private Double gpu;
    private Double gpuCore;



}
