package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_对象存储
 */
@Data
@OTC(value = "cmdb_bucket_res",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_对象存储")
public class CmdbBucketRes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //桶类型
    private String type;
    //访问权限
    private String acl;
    //存储类型
    private String storage_type;
    //对象个数
    private String object_number;
    //空间大小(MB)
    private Float size;
    //防盗链策略
    private String policy;
    //版本控制状态
    private String versioning_status;
    //生命周期
    private String lifecycle;
    //访问日志
    private String logging;
    //静态页面
    private String static_page;
    //跨域访问
    private String cross_domain;
    //存储阈值(最大容量GB)
    private Float storage_capacity;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
