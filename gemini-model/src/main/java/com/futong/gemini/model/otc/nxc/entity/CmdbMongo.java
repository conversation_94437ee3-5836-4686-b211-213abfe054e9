package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_Mongo
 */
@Data
@OTC(value = "cmdb_mongo",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_Mongo")
public class CmdbMongo extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //过期时间
    private Long expire_time;
    //开始时间
    private Long start_time;
    //结束时间
    private Long end_time;
    //实例付费类型
    private String charge_type;
    //是否开启了专有网络免密访问功能
    private String vpc_auth_mode;
    //实例的锁定状态
    private String lock_mode;
    //数据库引擎
    private String engine;
    //存储类型
    private String storage_type;
    //存储大小(GB)
    private Float storage;
    //类型
    private String type;
    //实例状态
    private String status;
    //原始状态
    private String open_status;
    //规格
    private String flaver;
    //版本
    private String version;
    //数据库的小版本
    private String small_version;
    //网络类型
    private String network_type;
    //实例过期状态
    private String expired;
    //实例网络类型
    private String protocol_type;
    //只读节点个数
    private String node_readonly;
    //CPU核数
    private Integer cpu_size;
    //内存大小(MB)
    private Integer mem_size;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
