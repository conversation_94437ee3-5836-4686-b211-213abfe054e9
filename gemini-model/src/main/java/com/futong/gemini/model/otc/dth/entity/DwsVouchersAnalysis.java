package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 申领维度数据集
 */
@Data
@OTC(value = "dws_vouchers_analysis",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsVouchersAnalysis  {

    //主键ID
    private String res_id;
    //租户id
    private String company_id;
    //租户名称
    private String company_name;
    //年份
    private String year;
    //已申领金额
    private Double apply_denomination;
    //已兑换金额
    private Double redeem_denomination;
    //申领时间
    private String apply_time;
    //兑换时间
    private String receive_time;
    //日期
    private String datadate;
    //更新时间
    private String update_time;

}
