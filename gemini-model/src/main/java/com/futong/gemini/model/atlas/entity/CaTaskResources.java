package com.futong.gemini.model.atlas.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;


/**
 * 任务资源信息
 */
@Data
@OTC(value = "ca_task_resources",type = OtcEnum.MessageType.NXC, version = "1", comment="任务资源信息")
public class CaTaskResources {

    //主键
    private String id;
    //原始主键ID
    private String open_id;
    //任务ID
    private String task_id;
    //资源数量
    private Integer resources_num;
    //CPU
    private Integer cpu;
    //内存
    private Double memory;
    //GPU整卡个数
    private Integer gpu;
    //GPU共享
    private String gpu_share;
    //节点类型
    private String node_type;
    //同步时间
    private Long sync_time;
    //原始JSON
    private String open_json;
    //云账号ID
    private String cloud_account_id;
    //是否删除（1是 2否）
    private Integer is_delete;
    //删除时间
    private Long delete_time;
    //是否删除（1是 NULL否）
    private String md5_str;
    //云类型
    private String cloud_type;
    //子云账号id
    private String sub_account_id;

}
