package com.futong.gemini.model.otc.gjc.entity;

import lombok.Data;

/**
 * 账单对象
 */
@Data
public class BillInfoBean {
    /**
     * es中的唯一id
     */
    private String id;
    /**
     * 云账号ID
     */
    private String accountId;
    /**
     * 云平类型
     */
    private String cloudType;
    private String billingCycle;
    private String billingDate;
    private String beginTime;
    private String endTime;
    private String method;
    private String region;
    private String zone;
    private String businessName;
    private String productName;
    private String instanceConfig;

    private String openId;
    private String openName;
    private Float price;
    private Float dscPrice;
    private Float actualPrice;
    private Float cashActualPrice;
    private Float deductActualPrice;
    private String sourceJson;
    private String createTime;


}
