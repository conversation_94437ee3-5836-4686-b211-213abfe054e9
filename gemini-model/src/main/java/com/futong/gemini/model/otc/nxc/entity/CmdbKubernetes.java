package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;


/**
 * CMDB_Kubernetes
 */
@Data
@OTC(value = "cmdb_kubernetes",type = OtcEnum.MessageType.NXC, version = "1", comment="CMDB_Kubernetes")
public class CmdbKubernetes extends CiResCloud {

    //云平台原始资源ID
    private String open_id;
    //云平台原始资源名称
    private String open_name;
    //集群类型
    private String cluster_type;
    //集群版本
    private String cluster_version;
    //集群节点数量
    private Integer size;
    //描述
    private String desc;
    //实例状态
    private String status;
    //原始状态
    private String open_status;
    //创建时间
    private Long create_time;
    //更新时间
    private Long update_time;
    //原始创建时间
    private Long open_create_time;
    //原始更新时间
    private Long open_update_time;
    //扩展字段1
    private String extend1;
    //扩展字段2
    private String extend2;
    //扩展字段3
    private String extend3;

}
