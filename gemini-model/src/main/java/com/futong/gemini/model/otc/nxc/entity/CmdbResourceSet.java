package com.futong.gemini.model.otc.nxc.entity;
import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import lombok.Data;

@Data
@OTC(value = "cmdb_resource_set",type = OtcEnum.MessageType.NXC, version = "1", comment="null")
public class CmdbResourceSet extends CiResCloud {

    //北新仓资源类型
    private String set_type;
    //北新仓资源ID
    private String set_id;
    //南新仓资源类型
    private String resource_type;
    //南新仓资源ID
    private String resource_id;

}
