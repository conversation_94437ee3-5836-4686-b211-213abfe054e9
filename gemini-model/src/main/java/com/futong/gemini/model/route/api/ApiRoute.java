package com.futong.gemini.model.route.api;

import cn.hutool.extra.spring.SpringUtil;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.api.proxy.MessageProxy;
import com.futong.gemini.model.otc.common.model.AtlasMessage;
import com.futong.gemini.model.otc.common.model.OtcMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ApiRoute {
    private static class Bean {
        private static MessageProxy proxy = SpringUtil.getBean(MessageProxy.class);
    }

    //发送并响应消息
    public static BaseResponse routeMessage(OtcMessage message) {
        try {
            return Bean.proxy.doMessage(message);
        } catch (Exception e) {
            log.error("执行数据发送失败", e);
            throw new BaseException(BaseResponse.ERROR_SYS, e, "执行数据发送失败");
        }
    }

    public static BaseResponse routeAtlasMessage(AtlasMessage message) {
        try {
            return Bean.proxy.doAtlasMessage(message);
        } catch (Exception e) {
            log.error("执行数据发送失败", e);
            throw new BaseException(BaseResponse.ERROR_SYS, e, "执行数据发送失败");
        }
    }

}
