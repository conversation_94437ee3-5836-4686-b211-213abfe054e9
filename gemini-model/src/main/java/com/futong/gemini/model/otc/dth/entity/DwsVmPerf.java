package com.futong.gemini.model.otc.dth.entity;

import com.futong.gemini.model.otc.common.base.OtcEnum;
import com.futong.gemini.model.otc.common.base.annotation.OTC;
import lombok.Data;

@Data
@OTC(value = "dws_vm_perf",type = OtcEnum.MessageType.DTH, version = "1")
public class DwsVmPerf  {

    //主键ID
    private String res_id;
    //年
    private String datayear;
    //月
    private String datamonth;
    //日
    private String datadate;
    //cmpID
    private String cmp_id;
    //用户ID
    private String user_id;
    //用户名称
    private String user_name;
    //租户ID
    private String company_id;
    //租户名称
    private String company_name;
    //组织ID
    private String dept_id;
    //组织名称
    private String dept_name;
    //业务系统ID
    private String project_id;
    //业务系统名称
    private String project_name;
    //标签键ID
    private String key_id;
    //标签值ID
    private String tag_key;
    //标签键
    private String tag_name;
    //标签值
    private String tag_value;
    //云账号id
    private String cloud_account_id;
    //云账号名称
    private String cloud_account_name;
    //云类型
    private String cloud_type;
    //云类型名称
    private String cloud_type_name;
    //云主机名称
    private String vm_name;
    //云主机Ip
    private String vm_ip;
    //云主机CPU总量(核)
    private Double cpu_size;
    //云主机内存总量(GB)
    private Double memory_size;
    //云主机存储总量(GB)
    private Double disk_size;
    //云主机CPU平均使用率(%)
    private Float avg_cpu_usage;
    //云主机CPU最小使用率(%)
    private Float min_cpu_usage;
    //云主机CPU最大使用率(%)
    private Float max_cpu_usage;
    //云主机内存平均使用率(%)
    private Float avg_memory_usage;
    //云主机内存最小使用率(%)
    private Float min_memory_usage;
    //云主机内存最大使用率(%)
    private Float max_memory_usage;
    //云主机存储平均使用率(%)
    private Float avg_disk_usage;
    //云主机存储最小使用率(%)
    private Float min_disk_usage;
    //云主机存储最大使用率(%)
    private Float max_disk_usage;
    //更新时间
    private String update_time;
    //云主机磁盘IO平均值(KBps)
    private String avg_disk_io;
    //云主机磁盘IO最小值(KBps)
    private String min_disk_io;
    //云主机磁盘IO最大值(KBps)
    private String max_disk_io;
    //云主机网络流量平均值
    private String avg_net_io;
    //云主机网络流量最小值
    private String min_net_io;
    //云主机网络流量最大值
    private String max_net_io;
    //网卡数量
    private String net_size;

}
