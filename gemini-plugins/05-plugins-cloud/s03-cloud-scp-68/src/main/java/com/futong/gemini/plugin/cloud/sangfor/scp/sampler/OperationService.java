package com.futong.gemini.plugin.cloud.sangfor.scp.sampler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.Entry;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.CloudClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.EcsClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.client.OperationClient;
import com.futong.gemini.plugin.cloud.sangfor.scp.convert.Converts;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.DescribeInstanceResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.OperationInstanceResponse;
import com.futong.gemini.plugin.cloud.sangfor.scp.response.PublicKeyResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class OperationService {

    public static BaseResponse updateInstancePassword(BaseCloudRequest request) {
        PublicKeyResponse publicKeyResponse = CloudClient.client.execute(request.getBody(), EcsClient::describePublicKey);
        String publicKey = publicKeyResponse.getData().getPublicKey();
        OperationInstanceResponse result = CloudClient.client.execute(request.getBody(), OperationClient::updateInstancePassword);
        return BaseResponse.SUCCESS;
    }
}
