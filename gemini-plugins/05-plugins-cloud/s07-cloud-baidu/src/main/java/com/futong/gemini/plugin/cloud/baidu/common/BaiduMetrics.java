package com.futong.gemini.plugin.cloud.baidu.common;

import java.util.HashMap;
import java.util.Map;

/**
 * 百度云监控指标常量类
 * 定义百度云BCM监控服务支持的指标名称
 * 与阿里云保持一致的核心监控指标
 */
public class BaiduMetrics {

    /**
     * 百度云实例监控指标集合
     * 只保留与阿里云相同的核心指标
     */
    public static final Map<String, String> INSTANCE_METRICS = new HashMap<String, String>() {
        {
            // CPU相关指标 - 使用百度云官方标准名称
            put("CPUUsagePercent", "CPU使用率");
            
            // 内存相关指标 - 使用百度云官方标准名称
            put("MemUsedPercent", "内存使用率");
            
            // 磁盘相关指标 - 使用百度云官方标准名称
            put("DiskUsedPercent", "磁盘使用率");
            put("vDiskReadOpCountPerSecond", "磁盘读操作数");
            put("vDiskWriteOpCountPerSecond", "磁盘写操作数");
            
            // 网络相关指标 - 使用百度云官方标准名称
            put("vNicInBytes", "网络入流量");
            put("vNicOutBytes", "网络出流量");
        }
    };

    /**
     * 获取实例监控指标名称集合
     * 返回与阿里云一致的核心指标
     */
    public static Map<String, String> getInstanceMetrics() {
        return new HashMap<>(INSTANCE_METRICS);
    }

    /**
     * 获取所有监控指标名称集合
     * 目前只包含实例监控指标
     */
    public static Map<String, String> getAllMetrics() {
        return new HashMap<>(INSTANCE_METRICS);
    }
}
