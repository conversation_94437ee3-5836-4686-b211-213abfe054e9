# 百度云云主机监控实现说明

## 概述
本文档说明百度云云主机监控功能的实现，包括API调用、数据处理和集成方案。

## 百度云云监控API参数说明

### 请求参数
| 名称 | 类型 | 描述 | 是否必须 | 参数位置 |
|------|------|------|----------|----------|
| userId | String | 用户ID | 是 | Body参数 |
| scope | String | 云产品标识，请参考云产品标识列表 | 是 | Body参数 |
| region | String | 区域标识，取值请参考区域列表 | 是 | Body参数 |
| type | String | 监控对象类型，请参考监控对象类型列表 | 是 | Body参数 |
| metricNames | List<String> | 监控指标列表，仅限于使用如下字符集合："0~9、A~Z、a~z"、 "_" | 是 | Body参数 |
| statistics | List<String> | 统计方法类型列表，可选值为average、maximum、minimum、sum、sampleCount | 是 | Body参数 |
| dimensions | List<List<Dimension>> | 维度列表，当需要查询多个实例的数据时，使用二维数组查询 | 是 | Body参数 |
| startTime | String | 查询起始时间，请参考日期与时间，UTC日期表示 | 是 | Body参数 |
| endTime | String | 查询截止时间，请参考日期与时间，UTC日期表示 | 是 | Body参数 |
| cycle | int | 统计周期，60的倍数，单位：秒 | 是 | Body参数 |

## 实现方案

### 1. 注册监控操作
在 `BaiduRegister.java` 中注册云主机监控操作：
```java
// 云主机监控 - 获取云主机监控数据
register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchEcsPerf)
    .addBefore(FetchService::defaultMetricRequest)
    .addBefore(FetchService::defaultEcsMetricNames)
    .addBefore(FetchService::defaultEcsMetricDimensions);
```

### 2. 预处理方法

#### defaultMetricRequest
- 设置默认时间范围和监控周期
- 验证必需参数 regionId
- 设置默认统计周期为300秒（5分钟）

#### defaultEcsMetricNames
- 设置默认监控指标名称
- 使用 `BaiduMetrics.getInstanceMetrics()` 定义的指标

#### defaultEcsMetricDimensions
- 获取云主机列表
- 构建实例ID和实例信息映射
- 为监控查询准备维度信息

### 3. 核心监控数据获取

#### fetchEcsPerf 方法实现
```java
public static BaseResponse fetchEcsPerf(BaseCloudRequest request) {
    // 1. 获取监控指标名称集合
    // 2. 获取BCM客户端
    // 3. 遍历每个监控指标，构造 MultiDimensionalMetricsRequest
    // 4. 设置完整的API参数：
    //    - userId: 从认证信息获取
    //    - scope: "BCE_BCC" (云主机)
    //    - type: "BCE_BCC" (监控对象类型)
    //    - metricNames: 监控指标列表
    //    - statistics: ["average", "sum", "minimum", "maximum", "sampleCount"]
    //    - dimensions: 实例ID维度列表
    //    - startTime/endTime: UTC格式时间
    //    - cycle: 统计周期（60的倍数）
    //    - region: 地域标识
    // 5. 调用 bcmClient.getAllDataMetricV2(metricRequest)
    // 6. 转换数据为 PerfInfoBean 格式
    // 7. 发送到性能监控系统
}
```

### 4. API调用示例
```java
MultiDimensionalMetricsRequest metricRequest = new MultiDimensionalMetricsRequest();
metricRequest.setUserId("453bf9********************9090dc");
metricRequest.setScope("BCE_BCC");
metricRequest.setType("BCE_BCC");  // 新增：监控对象类型
metricRequest.setRegion("bj");
metricRequest.setMetricNames(Arrays.asList("CPUUsagePercent", "MemUsedPercent"));
metricRequest.setStartTime("2024-03-26T07:01:00Z");
metricRequest.setEndTime("2024-03-26T07:05:00Z");
metricRequest.setCycle(300);  // 新增：统计周期（秒）
metricRequest.setStatistics(Arrays.asList("average", "sum", "minimum", "maximum", "sampleCount"));

List<List<Dimension>> dimensions = new ArrayList<>();
List<Dimension> d1 = new ArrayList<>();
d1.add(new Dimension("InstanceId", "i-M9****s1"));
dimensions.add(d1);
metricRequest.setDimensions(dimensions);

TsdbMetricAllDataResult result = bcmClient.getAllDataMetricV2(metricRequest);
```

### 4.1 API返回数据示例
```json
{
  "metadata": {
    "bceRequestId": "9cc92f21-d697-4133-bf2d-bf5f944ce752",
    "transferEncoding": "chunked",
    "contentLength": -1,
    "contentType": "application/json;charset=UTF-8",
    "date": 1711508479000
  },
  "requestId": "9cc92f21-d697-4133-bf2d-bf5f944ce752",
  "code": "success",
  "message": "",
  "metrics": [
    {
      "region": "bj",
      "scope": "BCE_BCC",
      "userId": "453bf9********************9090dc",
      "resourceId": "i-8y****gG",
      "metricName": "CPUUsagePercent",
      "dimensions": [
        {
          "name": "InstanceId",
          "value": "i-8y****gG"
        }
      ],
      "dataPoints": [
        {
          "timestamp": "2024-03-26T07:01:00Z",
          "average": 7.9346566231995,
          "sum": 15.869313246399
        }
      ]
    }
  ]
}
```

### 5. 数据转换
- 解析百度云BCM API返回的数据结构：
  ```json
  {
    "metrics": [
      {
        "resourceId": "i-8y****gG",
        "metricName": "CPUUsagePercent",
        "dimensions": [...],
        "dataPoints": [
          {
            "timestamp": "2024-03-26T07:01:00Z",
            "average": 7.9346566231995,
            "sum": 15.869313246399
          }
        ]
      }
    ]
  }
  ```
- 提取实例ID（优先使用resourceId，备用dimensions中的InstanceId）
- 处理时间戳（ISO格式字符串转换为时间戳）
- 获取监控值（优先使用average，备用sum）
- 使用动态映射将监控指标转换为 `PerfInfoBean` 属性
- 填充实例基本信息（CPU、内存、创建时间等）

### 6. 配置调度
在 `add_dispatch.json` 中配置监控任务的调度信息：
```json
{
  "action": "FETCH_COMPUTE_INSTANCE_PERF",
  "cron": "0 */5 * * * ?",
  "description": "获取云主机监控数据"
}
```

## 关键特性

### 1. 完整的API参数支持
- ✅ userId: 用户ID
- ✅ scope: 云产品标识
- ✅ type: 监控对象类型
- ✅ region: 区域标识
- ✅ metricNames: 监控指标列表
- ✅ statistics: 统计方法类型
- ✅ dimensions: 维度列表
- ✅ startTime/endTime: 时间范围
- ✅ cycle: 统计周期

### 2. 错误处理
- 参数验证和默认值设置
- 单个指标失败不影响其他指标
- 完整的异常处理和日志记录

### 3. 性能优化
- 批量查询多个实例
- 一次性获取多个指标
- 支持分页处理

### 4. 数据格式兼容
- 返回 `Map<String, PerfInfoBean>` 格式
- 与腾讯云等其他云厂商保持一致的接口
- 支持动态指标映射

## 监控指标支持

与阿里云保持一致的核心监控指标（使用百度云官方标准指标名称）：

### CPU相关指标
- CPUUsagePercent: CPU使用率

### 内存相关指标
- MemUsedPercent: 内存使用率

### 磁盘相关指标
- DiskUsedPercent: 磁盘使用率
- vDiskReadOpCountPerSecond: 磁盘读操作数（IOPS）
- vDiskWriteOpCountPerSecond: 磁盘写操作数（IOPS）

### 网络相关指标
- vNicInBytes: 网络入流量
- vNicOutBytes: 网络出流量

## 使用说明

1. 确保已正确配置百度云账号信息
2. 监控任务会自动调度执行（默认每5分钟）
3. 支持自定义监控指标和时间范围
4. 监控数据会自动发送到性能监控系统

## 注意事项

1. 统计周期必须是60的倍数
2. 时间格式必须为UTC格式
3. 监控指标名称只能包含指定字符
4. 需要确保实例ID的有效性
5. 百度云返回的时间戳是ISO格式字符串，需要转换为时间戳
6. 监控值优先使用average，如果没有则使用sum
7. 实例ID优先使用resourceId，备用dimensions中的InstanceId
8. 统计方式包含所有支持的类型：average（平均值）、sum（和值）、minimum（最小值）、maximum（最大值）、sampleCount（数据点数）
