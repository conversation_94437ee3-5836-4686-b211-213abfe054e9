# 百度云监控指标更新总结

## 更新概述

根据百度云官方监控指标文档，我们已经更新了监控指标配置，使用百度云的标准指标名称。

## 更新内容

### 1. 监控指标名称更新

#### 更新前（非标准名称）
- CPUUtilization (CPU使用率)
- MemoryUtilization (内存使用率)
- DiskUsage (磁盘使用率)
- DiskReadOps (磁盘读操作数)
- DiskWriteOps (磁盘写操作数)
- NetworkInRate (网络入流量速率)
- NetworkOutRate (网络出流量速率)

#### 更新后（百度云官方标准名称）
- **CPUUsagePercent** (CPU使用率)
- **MemUsedPercent** (内存使用率)
- **DiskUsedPercent** (磁盘使用率)
- **vDiskReadOpCountPerSecond** (磁盘读操作数)
- **vDiskWriteOpCountPerSecond** (磁盘写操作数)
- **vNicInBytes** (网络入流量)
- **vNicOutBytes** (网络出流量)

### 2. 更新的文件

#### BaiduMetrics.java
- 更新了 `INSTANCE_METRICS` 映射中的指标名称
- 使用百度云官方标准指标名称

#### Convert.java
- 更新了 `perfMappingBean` 映射
- 添加了新的百度云官方指标名称映射
- 保持了向后兼容性（同时支持新旧指标名称）

#### 文档更新
- 更新了 `云主机监控实现说明.md`
- 更新了 `百度云监控数据缺失排查指南.md`
- 记录了新的指标名称和映射关系

## 百度云官方指标说明

根据百度云官方文档，主要监控指标包括：

### CPU指标
- `CPUUsagePercent`: CPU使用率（linux、Windows服务器具备）

### 内存指标
- `MemUsedPercent`: 内存使用率（linux、Windows服务器具备）

### 磁盘指标
- `DiskUsedPercent`: 整台服务器的磁盘使用率
- `vDiskReadOpCountPerSecond`: 每秒磁盘IO读取次数
- `vDiskWriteOpCountPerSecond`: 每秒磁盘IO写入次数

### 网络指标
- `vNicInBytes`: 网卡输入流量
- `vNicOutBytes`: 网卡输出流量

## 兼容性说明

为了保持向后兼容性，我们在 `Convert.java` 中同时支持新旧指标名称：

```java
// 支持旧指标名称
perfMappingBean.put("CPUUtilization", PerfInfoBean::setCpuUsage);
perfMappingBean.put("DiskUsage", PerfInfoBean::setDiskUsage);

// 支持新指标名称
perfMappingBean.put("CPUUsagePercent", PerfInfoBean::setCpuUsage);
perfMappingBean.put("DiskUsedPercent", PerfInfoBean::setDiskUsage);
```

## 测试建议

1. **重新运行监控接口**：使用新的指标名称测试
2. **检查百度云控制台**：验证指标名称是否正确
3. **对比日志输出**：确认API调用是否成功
4. **验证数据转换**：检查监控数据是否正确转换

## 下一步

如果新的指标名称仍然无法获取数据，建议：

1. 检查实例状态和监控服务配置
2. 尝试单个指标测试
3. 联系百度云技术支持
4. 考虑使用降级机制

## 注意事项

- 百度云监控指标名称区分大小写
- 某些指标可能只在特定操作系统上可用
- 监控数据可能有延迟，建议扩大查询时间范围
- 实例必须有足够的负载才能产生监控数据
