# 百度云监控数据缺失排查指南

## 问题现象
百度云监控接口返回的 `dataPoints` 中只包含时间戳，缺少实际的监控值（如 `average`、`sum`、`maximum`、`minimum`）。

## 排查步骤

### 1. 检查实例状态
```bash
# 通过百度云控制台或API检查实例状态
# 确保实例处于 "运行中" 状态
```

### 2. 验证监控指标名称
确认当前使用的指标名称是否正确：

**当前使用的指标（已更新为百度云官方标准名称）：**
- CPUUsagePercent (CPU使用率)
- MemUsedPercent (内存使用率)
- DiskUsedPercent (磁盘使用率)
- vDiskReadOpCountPerSecond (磁盘读操作数)
- vDiskWriteOpCountPerSecond (磁盘写操作数)
- vNicInBytes (网络入流量)
- vNicOutBytes (网络出流量)

**百度云官方标准指标名称：**
- CPUUsagePercent (CPU使用率)
- MemUsedPercent (内存使用率)
- DiskUsedPercent (磁盘使用率)
- vDiskReadOpCountPerSecond (磁盘读操作数)
- vDiskWriteOpCountPerSecond (磁盘写操作数)
- vNicInBytes (网络入流量)
- vNicOutBytes (网络出流量)

### 3. 检查时间范围
确保请求的时间范围内有监控数据：
- 检查实例的创建时间
- 确保查询时间在实例运行期间
- 尝试扩大时间范围（如查询最近1小时的数据）

### 4. 验证监控服务配置
1. 登录百度云控制台
2. 进入 "云监控" 服务
3. 检查是否已开通监控服务
4. 确认监控数据采集间隔设置

### 5. 测试单个指标查询
建议先测试单个指标，使用百度云官方示例中的指标名称：
- CPUUsagePercent
- MemUsedPercent

### 6. 检查区域和类型配置
确保以下配置正确：
- `region`: "bd" (北京)
- `type`: "BCE_BCC" (云服务器)
- `scope`: "BCE_BCC" (云服务器)

## 调试建议

### 1. 使用百度云控制台验证
1. 登录百度云控制台
2. 进入云监控服务
3. 手动查询相同时间段的监控数据
4. 对比API返回的数据结构

### 2. 调整查询参数
```java
// 尝试不同的时间范围
startTime = "2025-08-26T07:00:00Z";  // 更早的时间
endTime = "2025-08-26T08:00:00Z";    // 更晚的时间

// 尝试不同的统计周期
cycle = 60;  // 1分钟统计周期
cycle = 300; // 5分钟统计周期
```

### 3. 检查实例是否有负载
确保实例在查询时间段内有：
- CPU使用
- 内存使用
- 磁盘I/O
- 网络流量

## 常见解决方案

### 1. 更新指标名称
如果指标名称不正确，需要更新 `BaiduMetrics.java` 中的指标映射。

### 2. 调整查询时间
如果时间范围不对，可以调整查询的时间范围。

### 3. 检查实例状态
确保实例在查询时间段内处于运行状态。

### 4. 联系百度云技术支持
如果以上步骤都无法解决问题，建议联系百度云技术支持，提供：
- 实例ID
- 查询的API请求参数
- 返回的响应数据
- 期望的监控数据时间范围

## 临时解决方案

如果监控数据确实无法获取，可以考虑：
1. 使用模拟数据进行开发和测试
2. 实现降级机制，当监控数据不可用时使用默认值
3. 添加更详细的错误处理和用户提示
