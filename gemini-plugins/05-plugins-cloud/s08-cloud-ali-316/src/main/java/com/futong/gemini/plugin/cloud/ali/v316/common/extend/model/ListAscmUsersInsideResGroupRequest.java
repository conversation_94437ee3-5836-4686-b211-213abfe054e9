package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import lombok.Data;
/**
 * 创建用户
 *
 * <AUTHOR>
 * @since 2024/10/16 17:03
 */
@Data
public class ListAscmUsersInsideResGroupRequest extends TeaModel {

    /**
     * 说明：若资源集ID和角色ID都不传或仅传其中一个请求参数的话，则都返回报错。
     * 资源组/资源集ID。
     */
    @NameInMap("resourceGroupId")
    public Integer resourceGroupId;

    /**
     * 分页页码，默认值为 1
     */
    @NameInMap("currentPage")
    public Integer currentPage;

    /**
     * 分页大小
     */
    @NameInMap("pageSize")
    public Integer pageSize;

    /**
     * 当前用户登录名
     */
    @NameInMap("loginName")
    public String loginName;

}
