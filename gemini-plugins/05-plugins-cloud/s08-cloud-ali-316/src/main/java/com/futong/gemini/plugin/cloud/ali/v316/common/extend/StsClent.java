package com.futong.gemini.plugin.cloud.ali.v316.common.extend;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.sts20150401.Client;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaException;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teaopenapi.models.Params;
import com.aliyun.teautil.models.RuntimeOptions;

import java.util.HashMap;
public class StsClent extends Client {

    public StsClent(Config config) throws Exception {
        super(config);
    }

    public java.util.Map<String, ?> callApi(Params params, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        if (com.aliyun.teautil.Common.isUnset(TeaModel.buildMap(params))) {
            throw new TeaException(TeaConverter.buildMap(
                    new TeaPair("code", "ParameterMissing"),
                    new TeaPair("message", "'params' can not be unset")
            ));
        }
        if (CollUtil.isEmpty(request.getQuery())) {
            HashMap<String, String> map = new HashMap<>();
            request.setQuery(map);
            request.getQuery().put("Product", "Sts");
        } else if (!request.getQuery().containsKey("Product")) {
            request.getQuery().put("Product", "Sts");
        }

        return this.doRPCRequest(params.action, params.version, params.protocol, params.method, params.authType, params.bodyType, request, runtime);
    }
}
