package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.ecs20140526.models.DescribeVSwitchesResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeVSwitchesPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeVSwitchesPriResponseBody body;

    @Data
    public static class DescribeVSwitchesPriResponseBody extends TeaModel{
        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;

        @NameInMap("VSwitches")
        public DescribeVSwitchesResponseBodyVSwitches vSwitches;


    }
    @Data
    public static class DescribeVSwitchesResponseBodyVSwitches extends TeaModel{
        @NameInMap("VSwitch")
        public java.util.List<DescribeVSwitchesResponseBodyVSwitchesVSwitchs> vSwitch;
    }

    @Data
    public static class DescribeVSwitchesResponseBodyVSwitchesVSwitchs extends DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;

        @NameInMap("Ipv6CidrBlock")
        public String ipv6CidrBlock;
    }
}
