package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.ecs20140526.models.DescribeSecurityGroupsResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeSecurityGroupsPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeSecurityGroupsPriResponseBody body;

    @Data
    public static class DescribeSecurityGroupsPriResponseBody extends TeaModel{
        @NameInMap("NextToken")
        public String nextToken;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RegionId")
        public String regionId;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;

        @NameInMap("SecurityGroups")
        public DescribeSecurityGroupsResponseBodySecurityGroups securityGroups;


    }
    @Data
    public static class DescribeSecurityGroupsResponseBodySecurityGroups extends TeaModel{
        @NameInMap("SecurityGroup")
        public java.util.List<DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroups> securityGroup;
    }

    @Data
    public static class DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroups extends DescribeSecurityGroupsResponseBody.DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroup {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
