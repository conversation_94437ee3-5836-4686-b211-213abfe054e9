package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import com.aliyun.vpc20160428.models.DescribeNatGatewaysResponseBody;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeNatGatewaysPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeNatGatewaysPriResponseBody body;

    @Data
    public static class DescribeNatGatewaysPriResponseBody extends TeaModel{
        @NameInMap("NextToken")
        public String nextToken;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RegionId")
        public String regionId;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;

//        @NameInMap("RouteTables")
//        public DescribeRouteTableListPriResponseBodyData routeTables;
        @NameInMap("NatGateways")
        public DescribeNatGatewaysPriResponseBodyData natGateways;


    }
    @Data
    public static class DescribeNatGatewaysPriResponseBodyData extends TeaModel{
//        @NameInMap("RouteTable")
//        public java.util.List<DescribeRouteTableListPriResponseBodyDescribeRouteTableList> routeTable;
        @NameInMap("NatGateway")
        public java.util.List<DescribeNatGatewaysPriResponseBodyDataResult> natGateway;
    }

    @Data
    public static class DescribeNatGatewaysPriResponseBodyDataResult extends DescribeNatGatewaysResponseBody.DescribeNatGatewaysResponseBodyNatGatewaysNatGateway {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
