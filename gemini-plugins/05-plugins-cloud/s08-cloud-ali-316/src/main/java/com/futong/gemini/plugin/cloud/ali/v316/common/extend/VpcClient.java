package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.*;
import com.aliyun.tea.interceptor.InterceptorChain;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyun.vpc20160428.models.*;
import com.futong.gemini.plugin.cloud.ali.v316.common.extend.model.*;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;

import java.util.HashMap;
import java.util.Map;
public class VpcClient extends com.aliyun.vpc20160428.Client {

    private final static InterceptorChain interceptorChain = InterceptorChain.create();

    /**
     * 产品类型
     */
    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "Vpc")));

    public VpcClient(Config config) throws Exception {
        super(config);
    }

    /**
     * 查询区域
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeRegionsResponse describeRegionsWithOptions(DescribeRegionsRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_2".equals(auth.getVersion())) {
//            req.query.put("RegionId", super._regionId);
//        }
        return TeaModel.toModel(this.doRPCRequest("DescribeRegions", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRegionsResponse());
    }

    @Override
    public CreateVpcResponse createVpcWithOptions(CreateVpcRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", Common.toMap(request)), product}));
        return TeaModel.toModel(this.doRPCRequest("CreateVpc", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new CreateVpcResponse());
    }

    @Override
    public DescribeVpcsResponse describeVpcsWithOptions(DescribeVpcsRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", Common.toMap(request)), product}));
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_8".equals(auth.getVersion())) {
//            this._endpoint = "vpc.res.zwww-sn.gxncloud.com";
//        }
        return (DescribeVpcsResponse) TeaModel.toModel(this.doRPCRequest("DescribeVpcs", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeVpcsResponse());
    }

    /**
     * 查询 Eip
     *
     * @param request
     * @return
     * @throws Exception
     */
    public DescribeEipAddressesPriResponse describeEipAddresse(DescribeEipAddressesRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeEipAddresses", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeEipAddressesPriResponse());
    }

    /**
     * 删除 Eip
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ReleaseEipAddressResponse releaseEipAddressWithOptions(ReleaseEipAddressRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ReleaseEipAddress", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new ReleaseEipAddressResponse());
    }

    /**
     * 修改 Eip
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyEipAddressAttributeResponse modifyEipAddressAttributeWithOptions(ModifyEipAddressAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyEipAddressAttribute", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyEipAddressAttributeResponse());
    }

    /**
     * 创建 Eip
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public AllocateEipAddressResponse allocateEipAddressWithOptions(AllocateEipAddressRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("AllocateEipAddress", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new AllocateEipAddressResponse());
    }

    /**
     * 绑定 Eip
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public AssociateEipAddressResponse associateEipAddressWithOptions(AssociateEipAddressRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("AssociateEipAddress", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new AssociateEipAddressResponse());
    }

    /**
     * 解绑 Eip
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public UnassociateEipAddressResponse unassociateEipAddressWithOptions(UnassociateEipAddressRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("UnassociateEipAddress", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new UnassociateEipAddressResponse());
    }

    /**
     * 查询 NAT 网关
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeNatGatewaysResponse describeNatGatewaysWithOptions(DescribeNatGatewaysRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeNatGateways", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeNatGatewaysResponse());
    }

    public DescribeNatGatewaysPriResponse describeNatGatewayList(DescribeNatGatewaysRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeNatGateways", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeNatGatewaysPriResponse());
    }

    /**
     * 创建 NAT 网关
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateNatGatewayResponse createNatGatewayWithOptions(CreateNatGatewayRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateNatGateway", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new CreateNatGatewayResponse());
    }

    /**
     * 删除 NAT 网关
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteNatGatewayResponse deleteNatGatewayWithOptions(DeleteNatGatewayRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteNatGateway", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteNatGatewayResponse());
    }

    /**
     * 修改 NAT 网关
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyNatGatewayAttributeResponse modifyNatGatewayAttributeWithOptions(ModifyNatGatewayAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyNatGatewayAttribute", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyNatGatewayAttributeResponse());
    }

    /**
     * 查询 DNAT
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeForwardTableEntriesResponse describeForwardTableEntriesWithOptions(DescribeForwardTableEntriesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeForwardTableEntries", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeForwardTableEntriesResponse());
    }

    public DescribeForwardTableEntriesPriResponse describeForwardTableEntriess(DescribeForwardTableEntriesExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeForwardTableEntries", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeForwardTableEntriesPriResponse());
    }

    /**
     * 查询 SNAT
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeSnatTableEntriesResponse describeSnatTableEntriesWithOptions(DescribeSnatTableEntriesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSnatTableEntries", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSnatTableEntriesResponse());
    }

    public DescribeSnatTableEntriesPriResponse describeSnatTableEntriess(DescribeSnatTableEntriesExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSnatTableEntries", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSnatTableEntriesPriResponse());
    }

    /**
     * 删除 SNAT
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteSnatEntryResponse deleteSnatEntryWithOptions(DeleteSnatEntryRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteSnatEntry", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteSnatEntryResponse());
    }

    /**
     * 删除 DNAT
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteForwardEntryResponse deleteForwardEntryWithOptions(DeleteForwardEntryRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteForwardEntry", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteForwardEntryResponse());
    }

    /**
     * 修改 DNAT
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyForwardEntryResponse modifyForwardEntryWithOptions(ModifyForwardEntryRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyForwardEntry", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyForwardEntryResponse());
    }

    /**
     * 修改 SNAT
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifySnatEntryResponse modifySnatEntryWithOptions(ModifySnatEntryRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifySnatEntry", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySnatEntryResponse());
    }

    /**
     * 查询路由表
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public DescribeRouteTableListPriResponse describeRouteTableLists(DescribeRouteTableListRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeRouteTableList", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRouteTableListPriResponse());
    }

    /**
     * 查询路由规则
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeRouteTablesResponse describeRouteTablesWithOptions(DescribeRouteTablesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeRouteTables", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRouteTablesResponse());
    }

    /**
     * 查询路由规则
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeRouteEntryListResponse describeRouteEntryListWithOptions(DescribeRouteEntryListRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeRouteEntryList", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRouteEntryListResponse());
    }

    /**
     * 创建路由
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateRouteTableResponse createRouteTableWithOptions(CreateRouteTableRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateRouteTable", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new CreateRouteTableResponse());
    }

    /**
     * 修改路由
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyRouteTableAttributesResponse modifyRouteTableAttributesWithOptions(ModifyRouteTableAttributesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyRouteTableAttributes", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyRouteTableAttributesResponse());
    }

    /**
     * 删除路由
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteRouteTableResponse deleteRouteTableWithOptions(DeleteRouteTableRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteRouteTable", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteRouteTableResponse());
    }

    /**
     * 绑定路由
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public AssociateRouteTableResponse associateRouteTableWithOptions(AssociateRouteTableRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("AssociateRouteTable", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new AssociateRouteTableResponse());
    }

    public DeleteVpcResponse deleteVpcWithOptions(DeleteVpcRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", Common.toMap(request))}));
        return TeaModel.toModel(this.doRPCRequest("DeleteVpc", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteVpcResponse());
    }

    public DeleteVSwitchResponse deleteVSwitchWithOptions(DeleteVSwitchRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", Common.toMap(request))}));
        return (DeleteVSwitchResponse) TeaModel.toModel(this.doRPCRequest("DeleteVSwitch", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteVSwitchResponse());
    }

    /**
     * 解绑路由
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public UnassociateRouteTableResponse unassociateRouteTableWithOptions(UnassociateRouteTableRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("UnassociateRouteTable", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime), new UnassociateRouteTableResponse());
    }

    public CreateVSwitchResponse createVSwitchWithOptions(CreateVSwitchRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", Common.toMap(request))}));
        Map<String, ?> map = this.doRPCRequest("CreateVSwitch", "2016-04-28", "HTTPS", "POST", "AK", "json", req, runtime);
        return (CreateVSwitchResponse) TeaModel.toModel(map, new CreateVSwitchResponse());
    }

    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        TeaModel.validateParams(request, "request");
        if (CollUtil.isEmpty(request.getQuery())) {
            HashMap<String, String> map = new HashMap<>();
            request.setQuery(map);
            request.getQuery().put("Product", "Vpc");
        } else if (!request.getQuery().containsKey("Product")) {
            request.getQuery().put("Product", "Vpc");
        }
//        JSONObject reqData = ActionFactory.requestData.get();
        JSONObject reqData = BaseClient.bodys.get();
        HashMap body = (HashMap) request.getBody();
        if (CollUtil.isEmpty(body)) {
            body = new HashMap();
            request.setBody(body);
        }
        if(CollUtil.isEmpty(request.getHeaders())){
            request.setHeaders(new HashMap());
        }
//        if (!body.containsKey("ResourceGroup") && ObjectUtil.isNotNull(reqData) && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//            request.getHeaders().put("x-acs-resourcegroupid", reqData.getString("resourceGroupId"));
//        }else{
//            request.getHeaders().put("x-acs-resourcegroupid", String.valueOf(body.get("ResourceGroup")));
//        }
//        if (!body.containsKey("Department") && ObjectUtil.isNotNull(reqData) && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//            request.getHeaders().put("x-acs-organizationid", reqData.getString("departmentId"));
//        }else{
//            request.getHeaders().put("x-acs-organizationid", String.valueOf(body.get("Department")));
//        }
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_4".equals(auth.getVersion())) {
//            request.query.put("regionId", super._regionId);
//        } else if ("v3_1_2".equals(auth.getVersion()) || "v3_1_8".equals(auth.getVersion()) ) {
//            request.query.put("RegionId", super._regionId);
//        }
        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, _readTimeout)),
                new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = "/";
                request_.query = TeaConverter.merge(String.class,
                        TeaConverter.buildMap(
                                new TeaPair("Action", action),
                                new TeaPair("Format", "json"),
                                new TeaPair("Version", version),
                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
                                new TeaPair("SignatureNonce", Common.getNonce())
                        ),
                        request.query
                );
                Map<String, String> headers = this.getRpcHeaders();
                if (Common.isUnset(headers)) {
                    // endpoint is setted in product client
                    request_.headers = TeaConverter.buildMap(
                            new TeaPair("host", _endpoint),
                            new TeaPair("x-acs-version", version),
                            new TeaPair("x-acs-action", action),
                            new TeaPair("user-agent", this.getUserAgent())
                    );
                } else {
                    request_.headers = TeaConverter.merge(String.class,
                            TeaConverter.buildMap(
                                    new TeaPair("host", _endpoint),
                                    new TeaPair("x-acs-version", version),
                                    new TeaPair("x-acs-action", action),
                                    new TeaPair("user-agent", this.getUserAgent())
                            ),
                            headers
                    );
                }
                if (CollUtil.isNotEmpty(request.headers)){
                    request_.headers.putAll(request.getHeaders());
                }
//                if ("v3_1_4".equals(auth.getVersion())) {
                    String hostEndpoint = _endpoint.substring(0, _endpoint.indexOf("/asapi"));
                    request_.headers.put("host", hostEndpoint);
                    request_.pathname = "/asapi/v3/";
//                }
                if (!Common.isUnset(request.body)) {
                    Map<String, Object> m = Common.assertAsMap(request.body);
                    Map<String, Object> tmp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
                    request_.body = Tea.toReadable(Common.toFormString(tmp));
                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
                }

                if (!Common.equalString(authType, "Anonymous")) {
                    String accessKeyId = this.getAccessKeyId();
                    String accessKeySecret = this.getAccessKeySecret();
                    String securityToken = this.getSecurityToken();
                    if (!Common.empty(securityToken)) {
                        request_.query.put("SecurityToken", securityToken);
                    }

                    request_.query.put("SignatureMethod", "HMAC-SHA1");
                    request_.query.put("SignatureVersion", "1.0");
                    request_.query.put("AccessKeyId", accessKeyId);
                    Map<String, Object> t = null;
                    if (!Common.isUnset(request.body)) {
                        t = Common.assertAsMap(request.body);
                    }

                    Map<String, String> signedParam = TeaConverter.merge(String.class,
                            request_.query,
                            com.aliyun.openapiutil.Client.query(t)
                    );
                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
                }

                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
                _lastResponse = response_;

                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
                    Object _res = Common.readAsJSON(response_.body);
                    Map<String, Object> err = Common.assertAsMap(_res);
                    Object requestId = com.aliyun.teaopenapi.Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + com.aliyun.teaopenapi.Client.defaultAny(err.get("Code"), err.get("code")) + ""),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + com.aliyun.teaopenapi.Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
                            new TeaPair("data", err)
                    ));
                }

                if (Common.equalString(bodyType, "binary")) {
                    Map<String, Object> resp = TeaConverter.buildMap(
                            new TeaPair("body", response_.body),
                            new TeaPair("headers", response_.headers)
                    );
                    return resp;
                } else if (Common.equalString(bodyType, "byte")) {
                    byte[] byt = Common.readAsBytes(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", byt),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (Common.equalString(bodyType, "string")) {
                    String str = Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (Common.equalString(bodyType, "json")) {
                    Object obj = Common.readAsJSON(response_.body);
                    Map<String, Object> res = Common.assertAsMap(obj);
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (Common.equalString(bodyType, "array")) {
                    Object arr = Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!Common.isUnset(_lastResponse)
                        && !Common.isUnset(_lastResponse.response)
                        && !Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }

}
