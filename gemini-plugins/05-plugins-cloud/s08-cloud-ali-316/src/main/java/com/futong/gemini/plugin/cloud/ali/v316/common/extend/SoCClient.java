//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.JSONPath;
//import com.aliyun.tea.TeaPair;
//import com.aliyun.teaopenapi.Client;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.model.cloud.PlatSafetyQueryDepartmentRequest;
//import com.futong.action.model.cloud.PlatSafetySyncRegionRequest;
//
//import java.util.Map;
//import java.util.Objects;
//
//import static com.aliyun.tea.TeaConverter.buildMap;
//public class SoCClient extends Client{
//
//    private static final TeaPair product = new TeaPair("query", buildMap(new TeaPair("Product", "yundunnextsoc")));
//
//    public SoCClient(Config config) throws Exception {
//        super(config);
//    }
//
//    public JSONObject getRegionMap(PlatSafetySyncRegionRequest request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("GetRegionMap", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject getDeptMap(PlatSafetyQueryDepartmentRequest request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("GetDeptMap", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject queryDataDistribution(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("QueryDataDistribution", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        JSONObject result = (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//        if (result.containsKey("Message")){
//            result =new JSONObject();
//            result.put("Message",JSONPath.read(JSONObject.toJSONString(map), "$.body.Message"));
//        }
//        return  result;
//    }
//
//    public JSONObject describeAttacks(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("QueryAttackMerges", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject queryAttackMergeInfo(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("QueryAttackMergeInfo", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject describeAlarms(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeAlarms", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject describeSuspEvents(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeSuspEvents", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeSuspEventDetail(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeSuspEventDetail", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeVulFrangs(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeVulFrangs", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject describeVulNewDetail(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                //product,
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeVulNewDetail", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject describeHcFrangs(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeHcFrangs", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject describeHcDetail(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request) )
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeHcDetail", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject describePBLineFrangs(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribePBLineFrangs", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject describePBLineDetail(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribePBLineDetail", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.Data");
//    }
//
//    public JSONObject queryLogs4Admin(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        request.entrySet().removeIf(entry -> Objects.isNull(entry.getValue()) ||"".equals(entry.getValue()));
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("QueryLogs4Admin", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject queryDataCount(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("QueryDataCount", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject getAssetCount(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("GetAssetCount", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeAlarmTypeDistribution(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeAlarmTypeDistribution", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeAttackTypeDistribution(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeAttackTypeDistribution", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeTopAttackedUsers(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeTopAttackedUsers", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeTopUnprocessedAlarmUsers(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeTopUnprocessedAlarmUsers", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject queryThreatChartData(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("QueryThreatChartData", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeSecurityTrends(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeSecurityTrends", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject getEcsProtectCount(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("GetEcsProtectCount", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeLatestAlarms(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeLatestAlarms", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//
//    public JSONObject describeLatestAttacks(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        request.put("Product", "yundunnextsoc");
//        OpenApiRequest req = OpenApiRequest.build(buildMap(
//                new TeaPair("query",OpenApiRequest.toMap(request))
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeLatestAttacks", "2019-07-23", "HTTPS", "GET", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
//    }
//}
