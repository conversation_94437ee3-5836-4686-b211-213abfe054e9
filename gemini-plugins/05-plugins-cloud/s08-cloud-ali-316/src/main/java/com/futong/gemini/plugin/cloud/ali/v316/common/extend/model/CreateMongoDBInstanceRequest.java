package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.dds20151201.models.CreateDBInstanceRequest;
import com.aliyun.tea.NameInMap;
import lombok.Data;
@Data
public class CreateMongoDBInstanceRequest extends CreateDBInstanceRequest implements IResourceGroup{

    @NameInMap("ResourceGroup")
    public String resourceGroup;

    @NameInMap("Department")
    public String department;
}
