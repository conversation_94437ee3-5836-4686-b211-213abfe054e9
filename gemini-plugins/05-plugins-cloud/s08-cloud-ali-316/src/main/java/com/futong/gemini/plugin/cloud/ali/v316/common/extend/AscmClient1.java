package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.aliyun.resourcemanager20200331.models.ListResourceGroupsRequest;
import com.aliyun.tea.*;
import com.aliyun.tea.interceptor.InterceptorChain;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teaopenapi.models.Params;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.futong.gemini.plugin.cloud.ali.v316.common.extend.model.*;
import lombok.extern.log4j.Log4j2;

import java.util.Map;
@Log4j2
public class AscmClient1 extends Client {

    public AscmClient1(Config config) throws Exception {
        super(config);
    }

    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "ascm")));

    private final static InterceptorChain interceptorChain = InterceptorChain.create();

    public JSONArray listResourceGroups(ListResourceGroupsRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("ListResourceGroup", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 获取资源集列表。
     *
     * @param request
     * @return
     * @throws Exception
     */
    public JSONArray listResourceGroups_V318(ListResourceGroupsRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", com.aliyun.openapiutil.Client.query(Common.toMap(request)))
        ));
        Params params = Params.build(TeaConverter.buildMap(
                new TeaPair("action", "ListResourceGroup"),
                new TeaPair("version", "2019-05-10"),
                new TeaPair("protocol", "HTTPS"),
                new TeaPair("pathname", "/ascm/auth/resource_group/list_resource_group"),
                new TeaPair("method", "POST"),
                new TeaPair("authType", "AK"),
                new TeaPair("style", "ROA"),
                new TeaPair("reqBodyType", "formData"),
                new TeaPair("bodyType", "json")
        ));
        Map<String, ?> map = this.callApi(params, req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    public JSONObject queryOrganizationTreeByRole(Map<String, Object> request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("GetCrossOrgTreeByRole", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    public JSONObject queryOrganizationTreeByRole_V316(Map<String, Object> request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("GetOrganizationTreeByRole", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    public JSONArray selectCommonSpec(Map<String, Object> hashmap) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", hashmap),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("SelectCommonSpec", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    public JSONArray groupCommonSpec(Map<String, Object> hashmap) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", hashmap),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("GroupCommonSpec", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 创建策略
     *
     * @param request
     * @return
     * @throws Exception
     */
    public JSONObject createLoginPolicy(CreateLoginPolicyRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("AddLoginPolicy", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 获取登录策略列表。
     *
     * @param hashmap
     * @return
     * @throws Exception
     */
    public JSONArray getListLoginPolicies(Map<String, Object> hashmap) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", hashmap),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("ListLoginPolicies", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 创建⼀个组织
     *
     * @param request
     * @return
     * @throws Exception
     */
    public JSONObject createOrganization(CreateOrganizationRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("CreateOrganization", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 获取组织列表。
     *
     * @param request
     * @return
     * @throws Exception
     */
    public GetOrganizationListResponse listOrganizations(GetOrganizationListRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("GetOrganizationList", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime), new GetOrganizationListResponse());
    }

    /**
     * 获取组织列表。
     *
     * @param request
     * @return
     * @throws Exception
     */
    public JSONArray listOrganizations_V318(GetOrganizationListRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", com.aliyun.openapiutil.Client.query(Common.toMap(request)))
        ));
        Params params = Params.build(TeaConverter.buildMap(
                new TeaPair("action", "GetOrganizationList"),
                new TeaPair("version", "2019-05-10"),
                new TeaPair("protocol", "HTTPS"),
                new TeaPair("pathname", "/ascm/auth/organization/queryList"),
                new TeaPair("method", "POST"),
                new TeaPair("authType", "AK"),
                new TeaPair("style", "ROA"),
                new TeaPair("reqBodyType", "formData"),
                new TeaPair("bodyType", "json")
        ));
        Map<String, ?> map = this.callApi(params, req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }
    /**
     * 获取可用于绑定的角色列表
     *
     * @param hashmap
     * @return
     * @throws Exception
     */
    public JSONArray getListActiveRoles(Map<String, Object> hashmap) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", hashmap),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("ListActiveRoles", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }


    /**
     * 创建用户
     *
     * @param request
     * @return
     * @throws Exception
     */
    public JSONObject createUserInfo(CreateTenantRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("AddUser", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 获取用户信息
     *
     * @param hashmap
     * @return
     * @throws Exception
     */
    public JSONObject getUserInfo(Map<String, Object> hashmap) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", hashmap),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("GetUserInfo", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 获取可见的用户列表。
     *
     * @param hashmap
     * @return
     * @throws Exception
     */
    public JSONArray getListUsers(Map<String, Object> hashmap) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", hashmap),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("ListUsers", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

//    public JSONObject getRamRoleAndRamPolicy(PaasNeutronElbSetAclWhiteRequest request, JSONObject user) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("Product", "ascm");
//        parameters.put("Action", "GetRamRoleAndRamPolicy");
//        parameters.put("Version", "2019-05-10");
//        Integer organizationId = user.getJSONObject("organization").getInteger("id");
//        parameters.put("OrganizationId", organizationId);
//        parameters.put("RegionId", request.getRegionId());
//        parameters.put("AscmUserId", user.getInteger("id"));
//        Map<String, String> headers = new HashMap<>();
//        headers.put("SignatureMethod", "HMAC-SHA1");
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.POST);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.data");
//    }
//
//    public <Q extends BaseCloudRequest> JSONObject getRamRoleAndRamPolicy(Q request, JSONObject user) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("Product", "ascm");
//        parameters.put("Action", "GetRamRoleAndRamPolicy");
//        parameters.put("Version", "2019-05-10");
//        Integer organizationId = user.getJSONObject("organization").getInteger("id");
//        parameters.put("OrganizationId", organizationId);
//        parameters.put("RegionId", request.getRegionId());
//        parameters.put("AscmUserId", user.getInteger("id"));
//        Map<String, String> headers = new HashMap<>();
//        headers.put("SignatureMethod", "HMAC-SHA1");
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.POST);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.data");
//    }

    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        TeaModel.validateParams(request, "request");
//        CloudAccessBean auth = ActionFactory.credentials.get();

        Map<String, Object> runtime_ = TeaConverter.buildMap(new TeaPair[]{new TeaPair("timeouted", "retry"), new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, this._readTimeout)), new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, this._connectTimeout)), new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, this._httpProxy)), new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, this._httpsProxy)), new TeaPair("noProxy", Common.defaultString(runtime.noProxy, this._noProxy)), new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, this._socks5Proxy)), new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, this._socks5NetWork)), new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, this._maxIdleConns)), new TeaPair("retry", TeaConverter.buildMap(new TeaPair[]{new TeaPair("retryable", runtime.autoretry), new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))})), new TeaPair("backoff", TeaConverter.buildMap(new TeaPair[]{new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")), new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))})), new TeaPair("ignoreSSL", runtime.ignoreSSL)});
        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;

        while (Tea.allowRetry((Map) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }

            ++_retryTimes;

            Map res;
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = Common.defaultString(this._protocol, protocol);
                request_.method = method;
                request_.pathname = "/";
                request_.query = TeaConverter.merge(String.class, new Map[]{TeaConverter.buildMap(new TeaPair[]{new TeaPair("Action", action), new TeaPair("Format", "json"), new TeaPair("Version", version), new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()), new TeaPair("SignatureNonce", Common.getNonce())}), request.query});
                Map<String, String> headers = this.getRpcHeaders();
                if (Common.isUnset(headers)) {
                    request_.headers = TeaConverter.buildMap(new TeaPair[]{new TeaPair("host", this._endpoint), new TeaPair("x-acs-version", version), new TeaPair("x-acs-action", action), new TeaPair("user-agent", this.getUserAgent())});
                } else {
                    request_.headers = TeaConverter.merge(String.class, new Map[]{TeaConverter.buildMap(new TeaPair[]{new TeaPair("host", this._endpoint), new TeaPair("x-acs-version", version), new TeaPair("x-acs-action", action), new TeaPair("user-agent", this.getUserAgent())}), headers});
                }
//                if ("v3_1_4".equals(auth.getVersion())) {
//                    String hostEndpoint = _endpoint.substring(0, _endpoint.indexOf("/asapi"));
//                    request_.headers.put("host", hostEndpoint);
//                    request_.pathname = "/asapi/v3/";
//                }
//                if ("v3_1_2".equals(auth.getVersion())) {
//                    request.query.put("RegionId", super._regionId);
//                }
//                if ("v3_1_8".equals(auth.getVersion()) && "GetOrganizationList".equals(action)) {
////                    String hostEndpoint = _endpoint.substring(0, _endpoint.indexOf("/asapi"));
////                    request_.headers.put("host", hostEndpoint);
//                    request_.pathname = "/asapi/v3/";
//                }
                Map resp;
                if (!Common.isUnset(request.body)) {
                    Map<String, Object> m = Common.assertAsMap(request.body);
                    resp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
                    request_.body = Tea.toReadable(Common.toFormString(resp));
                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
                }

                Map t;
                String str;
                if (!Common.equalString(authType, "Anonymous")) {
                    String accessKeyId = this.getAccessKeyId();
                    str = this.getAccessKeySecret();
                    String securityToken = this.getSecurityToken();
                    if (!Common.empty(securityToken)) {
                        request_.query.put("SecurityToken", securityToken);
                    }

                    request_.query.put("SignatureMethod", "HMAC-SHA1");
                    request_.query.put("SignatureVersion", "1.0");
                    request_.query.put("AccessKeyId", accessKeyId);
                    t = null;
                    if (!Common.isUnset(request.body)) {
                        t = Common.assertAsMap(request.body);
                    }

                    Map<String, String> signedParam = TeaConverter.merge(String.class, new Map[]{request_.query, com.aliyun.openapiutil.Client.query(t)});
                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, str));
                }

                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
                _lastResponse = response_;
                Object arr;
                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
                    arr = Common.readAsJSON(response_.body);
                    res = Common.assertAsMap(arr);
                    Object requestId = defaultAny(res.get("RequestId"), res.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(new TeaPair[]{new TeaPair("code", "" + defaultAny(res.get("Code"), res.get("code")) + ""), new TeaPair("message", "code: " + response_.statusCode + ", " + defaultAny(res.get("Message"), res.get("message")) + " request id: " + requestId + ""), new TeaPair("data", res)}));
                }

                if (Common.equalString(bodyType, "binary")) {
                    resp = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", response_.body), new TeaPair("headers", response_.headers)});
                    res = resp;
                    return res;
                }

                if (Common.equalString(bodyType, "byte")) {
                    byte[] byt = Common.readAsBytes(response_.body);
                    res = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", byt), new TeaPair("headers", response_.headers)});
                    return res;
                }

                if (Common.equalString(bodyType, "string")) {
                    str = Common.readAsString(response_.body);
                    res = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", str), new TeaPair("headers", response_.headers)});
                    return res;
                }

                if (Common.equalString(bodyType, "json")) {
                    arr = Common.readAsJSON(response_.body);
                    res = Common.assertAsMap(arr);
                    t = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", res), new TeaPair("headers", response_.headers)});
                    return t;
                }

                if (!Common.equalString(bodyType, "array")) {
                    resp = TeaConverter.buildMap(new TeaPair[]{new TeaPair("headers", response_.headers)});
                    return resp;
                }

                arr = Common.readAsJSON(response_.body);
                res = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", arr), new TeaPair("headers", response_.headers)});
            } catch (Exception var26) {
                if (Tea.isRetryable(var26)) {
                    _lastException = var26;
                    continue;
                }

                throw var26;
            } finally {
                if (!Common.isUnset(_lastResponse) && !Common.isUnset(_lastResponse.response) && !Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }

            }

            return res;
        }

        throw new TeaUnretryableException((TeaRequest) _lastRequest, _lastException);
    }

//    public DescribeAccountTenantResponse queryAccountTenant(Map<String, Object> request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", request),
//                product
//        ));
//
//        Map<String, ?> map = this.doRPCRequest("ListUsers", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
//
//        return TeaModel.toModel(map, new DescribeAccountTenantResponse());
//    }
//
//    public DescribeAccountRoleResponse queryAccountRole(Map<String, Object> hashmap) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", hashmap),
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("ListActiveRoles", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
//        // log.info("同步私有云角色数据, 原始数据为【{}】", JSONObject.toJSONString(map));
//        return TeaModel.toModel(map, new DescribeAccountRoleResponse());
//    }
//
//    public DescribeAccountPolicyResponse queryAccountPolicy(Map<String, Object> hashmap) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", hashmap),
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("ListLoginPolicies", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
//        // log.info("同步私有云租户登录策略数据, 原始数据为【{}】", JSONObject.toJSONString(map));
//        return TeaModel.toModel(map, new DescribeAccountPolicyResponse());
//    }

    /**
     * 用户绑定角色
     * @param request 请求参数
     * @return 绑定结果
     * @throws Exception 异常抛出
     */
    public JSONObject addRoleToUser(AddRoleToTenantRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("AddRoleToUser", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        // log.info("用户绑定角色, 原始返回数据为【{}】", JSONObject.toJSONString(map));
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    /**
     * 用户和资源集进行绑定
     * @param request 请求参数
     * @return 绑定结果
     * @throws Exception 异常抛出
     */
    public JSONObject bindAscmUserAndResourceGroup(BindResGroupToTenantRequest request) throws Exception {

        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("BindAscmUserAndResourceGroup", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        // log.info("用户和资源集进行绑定, 原始返回数据为【{}】", JSONObject.toJSONString(map));
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");

        /*RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", com.aliyun.openapiutil.Client.query(com.aliyun.teautil.Common.toMap(request)))
        ));
        Params params = Params.build(TeaConverter.buildMap(
                new TeaPair("action", "BindAscmUserAndResourceGroup"),
                new TeaPair("version", "2019-05-10"),
                new TeaPair("protocol", "HTTPS"),
                new TeaPair("pathname", "/ascm/auth/resource_group/add_ascm_users"),
                new TeaPair("method", "POST"),
                new TeaPair("authType", "AK"),
                new TeaPair("style", "ROA"),
                new TeaPair("reqBodyType", "formData"),
                new TeaPair("bodyType", "json")
        ));
        Map<String, ?> map = this.callApi(params, req, runtime);
        log.info("用户和资源集进行绑定, 原始返回数据为【{}】", JSONObject.toJSONString(map));
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");*/
    }

    /**
     * 获取资源集下的用户列表
     * @param request 请求参数
     * @return 用户列表
     * @throws Exception 异常抛出
     */
    public JSONArray ListAscmUsersInsideResourceGroup(ListAscmUsersInsideResGroupRequest request) throws Exception {

        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("ListAscmUsersInsideResourceGroup", "2019-05-10", "HTTPS", "POST", "AK", "json", req, runtime);
        // log.info("获取资源集下的用户列表, 原始返回数据为【{}】", JSONObject.toJSONString(map));
        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");

    }

}
