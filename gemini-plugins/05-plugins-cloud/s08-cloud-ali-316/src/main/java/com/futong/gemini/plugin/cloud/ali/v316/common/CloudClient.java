package com.futong.gemini.plugin.cloud.ali.v316.common;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.rds20140815.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.HttpClientConfig;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.v316.common.extend.EcsClient;
import com.futong.gemini.plugin.cloud.ali.v316.common.extend.OSSClientAsapi;
import com.futong.gemini.plugin.cloud.ali.v316.common.extend.VpcClient;
import com.futong.gemini.plugin.cloud.sdk.client.BaseCloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
@Slf4j
public class CloudClient extends BaseCloudClient {
    public static final CloudClient client = new CloudClient();

    //获取Client对象
    @Override
    public <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            if (clazz == OSSClient.class) {
                return (C) ossClient(body);
            }  else if (clazz == OSSClientAsapi.class) {
                return  (C) new OSSClientAsapi();
            } else if (clazz == IAcsClient.class) {
                return  IAcsClient(body);
            } else {
                //请求Client对象配置信息
                Config config = new Config();
                config.setAccessKeyId(body.getAccess().getUsername());
                config.setAccessKeySecret(body.getAccess().getPassword());
                config.setProtocol(body.getAccess().getProtocol());
                config.setRegionId(body.getCloud().getString("regionId"));
                //设置代理
                if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                    config.setHttpProxy(body.getAuth().getString("proxyAddr"));
                }
//                if(ObjUtil.isNotEmpty(body.getAuth().getJSONObject("jsonStr")) && ObjUtil.isNotEmpty(body.getAuth().getJSONObject("jsonStr").getString("regionId"))){
//
//                }
                config.setEndpoint(StrUtil.replace(body.getAccess().getServerIp(), "{regionId}", body.getCloud().getString("regionId")));
//                config.setEndpoint("internal.asapi.cn-hangzhou-zjjk-d01.ops.zj.zjhealthcloud.cn");
//                config.setEndpoint("public.asapi.cn-hangzhou-zjjk-d01.res.zj.zjhealthcloud.cn/asapi/v3");
                //判断是否使用扩展类
                if (clazz == com.aliyun.cms20190101.Client.class) {
                    config.setEndpoint(StrUtil.format("metrics.{}.aliyuncs.com", body.getCloud().getString("regionId")));
//                    clazz = (Class<C>) CmsClient.class;
                } else if (clazz == com.aliyun.ecs20140526.Client.class) {
                    clazz = (Class<C>) EcsClient.class;
                } else if (clazz == com.aliyun.vpc20160428.Client.class) {
                    clazz = (Class<C>) VpcClient.class;
                }
                if (clazz == Client.class) {
                    config.setReadTimeout(20000);
                }
                return clazz.getConstructor(Config.class).newInstance(config);
            }
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    private static final String ossEndpoint = "oss-{}.aliyuncs.com";

    private static OSS ossClient(BaseCloudRequestBody body) {
        try {
            String endpoint = body.getAuth().getString("endpoint");
            endpoint = StrUtil.emptyToDefault(endpoint, StrUtil.format(ossEndpoint, body.getCloud().getString("regionId")));
            endpoint = StrUtil.format(endpoint, body.getCloud().getString("regionId"));
            ClientBuilderConfiguration configuration = new ClientBuilderConfiguration();
            if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                URL url = new URL(body.getAuth().getString("proxyAddr"));
                configuration.setProxyHost(url.getHost());
                configuration.setProxyPort(url.getPort());
            }
            return new OSSClientBuilder().build(endpoint,
                    body.getAccess().getUsername(),
                    body.getAccess().getPassword(),
                    configuration);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    public static <C> C IAcsClient(BaseCloudRequestBody body) {
        try {
//            CloudAccessBean cloudClient = ActionFactory.credentials.get();
            String user = body.getAccess().getUsername();
            String password = body.getAccess().getPassword();
            if (body.getAuth().containsKey("ywUsername")) {
                user = body.getAuth().getString("ywUsername");
            }
            if (body.getAuth().containsKey("ywPassword")) {
                password = body.getAuth().getString("ywPassword");
            }
            DefaultProfile profile = DefaultProfile.getProfile(body.getCloud().getString("regionId"), user, password);
            HttpClientConfig httpClientConfig = profile.getHttpClientConfig();
            if ("https".equals(body.getAccess().getProtocol())) {
                httpClientConfig.setProtocolType(ProtocolType.valueOf("HTTPS"));
                httpClientConfig.setIgnoreSSLCerts(true);
            } else {
                httpClientConfig.setProtocolType(ProtocolType.valueOf("HTTP"));
            }
            if (StrUtil.isNotEmpty(body.getAuth().getString("proxyAddr"))) {
                httpClientConfig.setHttpProxy(body.getAuth().getString("proxyAddr"));
            }

            IAcsClient client = new DefaultAcsClient(profile);
            return (C) client;
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

}

