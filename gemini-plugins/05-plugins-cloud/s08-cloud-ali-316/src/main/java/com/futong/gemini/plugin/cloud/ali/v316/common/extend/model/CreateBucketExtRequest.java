package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.tea.NameInMap;
import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
public class CreateBucketExtRequest extends CreateBucketRequest implements IResourceGroup {

    @NameInMap("ResourceGroup")
    public String resourceGroup;

    @NameInMap("Department")
    public String department;

    public CreateBucketExtRequest(String bucketName) {
        super(bucketName);
    }
}
