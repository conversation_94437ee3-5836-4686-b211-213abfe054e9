package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.ecs20140526.models.DescribeKeyPairsResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeKeyPairsPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeKeyPairsPriResponseBody body;

    @Data
    public static class DescribeKeyPairsPriResponseBody extends TeaModel{
        @NameInMap("KeyPairs")
        public DescribeKeyPairsResponseBodyKeyPairss keyPairs;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;


    }
    @Data
    public static class DescribeKeyPairsResponseBodyKeyPairss extends TeaModel{
        @NameInMap("KeyPair")
        public java.util.List<DescribeKeyPairsResponseBodyKeyPairsKeyPairs> keyPair;
    }

    @Data
    public static class DescribeKeyPairsResponseBodyKeyPairsKeyPairs extends DescribeKeyPairsResponseBody.DescribeKeyPairsResponseBodyKeyPairsKeyPair {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
