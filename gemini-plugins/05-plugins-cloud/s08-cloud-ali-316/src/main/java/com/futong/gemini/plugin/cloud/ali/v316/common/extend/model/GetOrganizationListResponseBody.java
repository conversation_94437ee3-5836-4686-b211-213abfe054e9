package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;

import java.util.List;
public class GetOrganizationListResponseBody extends TeaModel {
    @NameInMap("code")
    public String code;
    @NameInMap("message")
    public String message;
    @NameInMap("data")
    public List<GetOrganizationListResponseBodyData> data;

    public static GetOrganizationListResponseBody build(java.util.Map<String, ?> map) {
        GetOrganizationListResponseBody self = new GetOrganizationListResponseBody();
        return TeaModel.build(map, self);
    }

    public String getCode() {
        return code;
    }

    public GetOrganizationListResponseBody setCode(String code) {
        this.code = code;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public GetOrganizationListResponseBody setMessage(String message) {
        this.message = message;
        return this;
    }

    public List<GetOrganizationListResponseBodyData> getData() {
        return data;
    }

    public GetOrganizationListResponseBody setData(List<GetOrganizationListResponseBodyData> data) {
        this.data = data;
        return this;
    }

    public static class GetOrganizationListResponseBodyData extends TeaModel {
        @NameInMap("id")
        public Integer id;
        @NameInMap("muserId")
        public String muserId;
        @NameInMap("internal")
        public Boolean internal;
        @NameInMap("multiCloudStatus")
        public String multiCloudStatus;
        @NameInMap("supportRegionList")
        public List<String> supportRegionList;
        @NameInMap("level")
        public String level;
        @NameInMap("name")
        public String name;
        @NameInMap("alias")
        public String alias;
        @NameInMap("supportRegions")
        public String supportRegions;
        @NameInMap("cuserId")
        public String cuserId;
        @NameInMap("uuid")
        public String uuid;
        @NameInMap("parentId")
        public String parentId;

        public static GetOrganizationListResponseBodyData build(java.util.Map<String, ?> map) {
            GetOrganizationListResponseBodyData self = new GetOrganizationListResponseBodyData();
            return TeaModel.build(map, self);
        }

        public Integer getId() {
            return id;
        }

        public GetOrganizationListResponseBodyData setId(Integer id) {
            this.id = id;
            return this;
        }

        public String getMuserId() {
            return muserId;
        }

        public GetOrganizationListResponseBodyData setMuserId(String muserId) {
            this.muserId = muserId;
            return this;
        }

        public Boolean getInternal() {
            return internal;
        }

        public GetOrganizationListResponseBodyData setInternal(Boolean internal) {
            this.internal = internal;
            return this;
        }

        public String getMultiCloudStatus() {
            return multiCloudStatus;
        }

        public GetOrganizationListResponseBodyData setMultiCloudStatus(String multiCloudStatus) {
            this.multiCloudStatus = multiCloudStatus;
            return this;
        }

        public List<String> getSupportRegionList() {
            return supportRegionList;
        }

        public GetOrganizationListResponseBodyData setSupportRegionList(List<String> supportRegionList) {
            this.supportRegionList = supportRegionList;
            return this;
        }

        public String getLevel() {
            return level;
        }

        public GetOrganizationListResponseBodyData setLevel(String level) {
            this.level = level;
            return this;
        }

        public String getName() {
            return name;
        }

        public GetOrganizationListResponseBodyData setName(String name) {
            this.name = name;
            return this;
        }

        public String getAlias() {
            return alias;
        }

        public GetOrganizationListResponseBodyData setAlias(String alias) {
            this.alias = alias;
            return this;
        }

        public String getSupportRegions() {
            return supportRegions;
        }

        public GetOrganizationListResponseBodyData setSupportRegions(String supportRegions) {
            this.supportRegions = supportRegions;
            return this;
        }

        public String getCuserId() {
            return cuserId;
        }

        public GetOrganizationListResponseBodyData setCuserId(String cuserId) {
            this.cuserId = cuserId;
            return this;
        }

        public String getUuid() {
            return uuid;
        }

        public GetOrganizationListResponseBodyData setUuid(String uuid) {
            this.uuid = uuid;
            return this;
        }

        public String getParentId() {
            return parentId;
        }

        public GetOrganizationListResponseBodyData setParentId(String parentId) {
            this.parentId = parentId;
            return this;
        }
    }
}
