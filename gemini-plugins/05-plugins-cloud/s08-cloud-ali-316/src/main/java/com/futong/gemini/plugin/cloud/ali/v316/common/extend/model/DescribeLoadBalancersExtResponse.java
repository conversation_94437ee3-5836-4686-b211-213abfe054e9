package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
@Data
public class DescribeLoadBalancersExtResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeLoadBalancersResponseBodyExt body;

    public static DescribeLoadBalancersExtResponse build(java.util.Map<String, ?> map) {
        DescribeLoadBalancersExtResponse self = new DescribeLoadBalancersExtResponse();
        return TeaModel.build(map, self);
    }

    public DescribeLoadBalancersExtResponse setHeaders(java.util.Map<String, String> headers) {
        this.headers = headers;
        return this;
    }
    public java.util.Map<String, String> getHeaders() {
        return this.headers;
    }

    public DescribeLoadBalancersExtResponse setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
        return this;
    }
    public Integer getStatusCode() {
        return this.statusCode;
    }

    public DescribeLoadBalancersExtResponse setBody(DescribeLoadBalancersResponseBodyExt body) {
        this.body = body;
        return this;
    }
    public DescribeLoadBalancersResponseBodyExt getBody() {
        return this.body;
    }
}
