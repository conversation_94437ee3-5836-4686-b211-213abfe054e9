package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.ecs20140526.models.DescribeVpcsResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeVpcsPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeVpcsPriResponseBody body;

    @Data
    public static class DescribeVpcsPriResponseBody extends TeaModel{
        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;

        @NameInMap("Vpcs")
        public DescribeVpcsResponseBodyVpcs vpcs;


    }
    @Data
    public static class DescribeVpcsResponseBodyVpcs extends TeaModel{
        @NameInMap("Vpc")
        public java.util.List<DescribeVpcsResponseBodyVpcsVpcs> vpc;
    }

    @Data
    public static class DescribeVpcsResponseBodyVpcsVpcs extends DescribeVpcsResponseBody.DescribeVpcsResponseBodyVpcsVpc {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
