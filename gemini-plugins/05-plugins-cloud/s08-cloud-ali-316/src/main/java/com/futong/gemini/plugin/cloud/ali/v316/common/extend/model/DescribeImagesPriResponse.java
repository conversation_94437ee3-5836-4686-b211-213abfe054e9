package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.ecs20140526.models.DescribeImagesResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeImagesPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeImagesPriResponseBody body;

    @Data
    public static class DescribeImagesPriResponseBody extends TeaModel{
        @NameInMap("Images")
        public DescribeImagesResponseBodyImages images;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RegionId")
        public String regionId;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;


    }
    @Data
    public static class DescribeImagesResponseBodyImages extends TeaModel{
        @NameInMap("Image")
        public java.util.List<DescribeImagesResponseBodyImagesImages> image;
    }

    @Data
    public static class DescribeImagesResponseBodyImagesImages extends DescribeImagesResponseBody.DescribeImagesResponseBodyImagesImage {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
