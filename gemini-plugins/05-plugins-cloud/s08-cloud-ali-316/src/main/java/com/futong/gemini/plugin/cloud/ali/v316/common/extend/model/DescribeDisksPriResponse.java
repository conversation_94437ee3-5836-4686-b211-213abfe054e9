package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.ecs20140526.models.DescribeDisksResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeDisksPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeDisksPriResponseBody body;

    @Data
    public static class DescribeDisksPriResponseBody extends TeaModel{
        @NameInMap("Disks")
        public DescribeDisksResponseBodyDisks disks;

        @NameInMap("NextToken")
        public String nextToken;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;


    }
    @Data
    public static class DescribeDisksResponseBodyDisks extends TeaModel{
        @NameInMap("Disk")
        public java.util.List<DescribeDisksResponseBodyDisksDisks> disk;
    }

    @Data
    public static class DescribeDisksResponseBodyDisksDisks extends DescribeDisksResponseBody.DescribeDisksResponseBodyDisksDisk {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
