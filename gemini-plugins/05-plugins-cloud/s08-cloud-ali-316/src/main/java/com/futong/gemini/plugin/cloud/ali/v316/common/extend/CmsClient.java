//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.cms20190101.Client;
//import com.aliyun.cms20190101.models.*;
//import com.aliyun.tea.*;
//import com.aliyun.tea.interceptor.InterceptorChain;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teautil.Common;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionFactory;
//import com.futong.bean.CloudAccessBean;
//import com.futong.common.utils.RedisUtils;
//import lombok.extern.log4j.Log4j2;
//
//import java.util.HashMap;
//import java.util.Map;
//@Log4j2
//public class CmsClient extends Client {
//
//    private static final InterceptorChain interceptorChain = InterceptorChain.create();
//
//    /**
//     * 产品类型
//     */
//    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "Cms")));
//
//    public CmsClient(Config config) throws Exception {
//        super(config);
//    }
//
//    public DescribeMetricListResponse describeMetricListWithOptions(DescribeMetricListRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeMetricList", "2019-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeMetricListResponse());
//    }
//
//
//    @Override
//    public DescribeAlertLogListResponse describeAlertLogListWithOptions(DescribeAlertLogListRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        Map<String, Object> query = new HashMap();
//        if (!Common.isUnset(request.contactGroup)) {
//            query.put("ContactGroup", request.contactGroup);
//        }
//
//        if (!Common.isUnset(request.endTime)) {
//            query.put("EndTime", request.endTime);
//        }
//
//        if (!Common.isUnset(request.groupBy)) {
//            query.put("GroupBy", request.groupBy);
//        }
//
//        if (!Common.isUnset(request.groupId)) {
//            query.put("GroupId", request.groupId);
//        }
//
//        if (!Common.isUnset(request.lastMin)) {
//            query.put("LastMin", request.lastMin);
//        }
//
//        if (!Common.isUnset(request.level)) {
//            query.put("Level", request.level);
//        }
//
//        if (!Common.isUnset(request.metricName)) {
//            query.put("MetricName", request.metricName);
//        }
//
//        if (!Common.isUnset(request.namespace)) {
//            query.put("Namespace", request.namespace);
//        }
//
//        if (!Common.isUnset(request.pageNumber)) {
//            query.put("PageNumber", request.pageNumber);
//        }
//
//        if (!Common.isUnset(request.pageSize)) {
//            query.put("PageSize", request.pageSize);
//        }
//
//        if (!Common.isUnset(request.product)) {
//            query.put("Product", request.product);
//        }
//
//        if (!Common.isUnset(request.ruleId)) {
//            query.put("RuleId", request.ruleId);
//        }
//
//        if (!Common.isUnset(request.ruleName)) {
//            query.put("RuleName", request.ruleName);
//        }
//
//        if (!Common.isUnset(request.searchKey)) {
//            query.put("SearchKey", request.searchKey);
//        }
//
//        if (!Common.isUnset(request.sendStatus)) {
//            query.put("SendStatus", request.sendStatus);
//        }
//
//        if (!Common.isUnset(request.startTime)) {
//            query.put("StartTime", request.startTime);
//        }
//
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeAlertLogList", "2019-01-01", "HTTPS", "POST", "AK", "json", req, runtime);
//        return TeaModel.toModel(map, new DescribeAlertLogListResponse());
//    }
//
//    @Override
//    public DescribeSystemEventAttributeResponse describeSystemEventAttributeWithOptions(DescribeSystemEventAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        Map<String, Object> query = new HashMap();
//        if (!Common.isUnset(request.endTime)) {
//            query.put("EndTime", request.endTime);
//        }
//
//        if (!Common.isUnset(request.eventType)) {
//            query.put("EventType", request.eventType);
//        }
//
//        if (!Common.isUnset(request.groupId)) {
//            query.put("GroupId", request.groupId);
//        }
//
//        if (!Common.isUnset(request.level)) {
//            query.put("Level", request.level);
//        }
//
//        if (!Common.isUnset(request.name)) {
//            query.put("Name", request.name);
//        }
//
//        if (!Common.isUnset(request.pageNumber)) {
//            query.put("PageNumber", request.pageNumber);
//        }
//
//        if (!Common.isUnset(request.pageSize)) {
//            query.put("PageSize", request.pageSize);
//        }
//
//        if (!Common.isUnset(request.product)) {
//            query.put("Product", request.product);
//        }
//
//        if (!Common.isUnset(request.searchKeywords)) {
//            query.put("SearchKeywords", request.searchKeywords);
//        }
//
//        if (!Common.isUnset(request.startTime)) {
//            query.put("StartTime", request.startTime);
//        }
//
//        if (!Common.isUnset(request.status)) {
//            query.put("Status", request.status);
//        }
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return (DescribeSystemEventAttributeResponse) TeaModel.toModel(this.doRPCRequest("DescribeSystemEventAttribute", "2019-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSystemEventAttributeResponse());
//    }
//
//    @Override
//    public DescribeCustomEventAttributeResponse describeCustomEventAttributeWithOptions(DescribeCustomEventAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        Map<String, Object> query = new HashMap();
//        if (!Common.isUnset(request.endTime)) {
//            query.put("EndTime", request.endTime);
//        }
//
//        if (!Common.isUnset(request.eventId)) {
//            query.put("EventId", request.eventId);
//        }
//
//        if (!Common.isUnset(request.groupId)) {
//            query.put("GroupId", request.groupId);
//        }
//
//        if (!Common.isUnset(request.name)) {
//            query.put("Name", request.name);
//        }
//
//        if (!Common.isUnset(request.pageNumber)) {
//            query.put("PageNumber", request.pageNumber);
//        }
//
//        if (!Common.isUnset(request.pageSize)) {
//            query.put("PageSize", request.pageSize);
//        }
//
//        if (!Common.isUnset(request.searchKeywords)) {
//            query.put("SearchKeywords", request.searchKeywords);
//        }
//
//        if (!Common.isUnset(request.startTime)) {
//            query.put("StartTime", request.startTime);
//        }
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return (DescribeCustomEventAttributeResponse) TeaModel.toModel(this.doRPCRequest("DescribeCustomEventAttribute", "2019-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeCustomEventAttributeResponse());
//    }
//
//
//    @Override
//    public DescribeAlertHistoryListResponse describeAlertHistoryListWithOptions(DescribeAlertHistoryListRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        Map<String, Object> query = OpenApiRequest.buildMap(request);
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        String key = RedisUtils.getKey(RedisUtils.ALI_PRI_INSTANCEID, auth.getUsername() + auth.getPassword());
//        if (RedisUtils.hasKey(key)) {
//            query.put("InstanceId", RedisUtils.get(key));
//            RedisUtils.del(key);
//        }
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.openapiutil.Client.query(query)),
//                product
//        ));
//        JSONObject reqData = ActionFactory.requestData.get();
//        if (reqData.containsKey("request")) {
//            reqData = reqData.getJSONObject("request");
//        }
//        HashMap body = (HashMap) req.getBody();
//        if (CollUtil.isEmpty(body)) {
//            body = new HashMap();
//            req.setBody(body);
//        }
//        //body.put("ResourceGroup", "105");
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        //body.put("Department", "73");
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        Map<String, ?> map = this.doRPCRequest("DescribeAlertHistoryList", "2019-01-01", "HTTPS", "POST", "AK", "json", req, runtime);
//        return (DescribeAlertHistoryListResponse) TeaModel.toModel(map, new DescribeAlertHistoryListResponse());
//    }
//
//
//    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        TeaModel.validateParams(request, "request");
//        Map<String, Object> runtime_ = TeaConverter.buildMap(new TeaPair[]{new TeaPair("timeouted", "retry"), new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, this._readTimeout)), new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, this._connectTimeout)), new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, this._httpProxy)), new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, this._httpsProxy)), new TeaPair("noProxy", Common.defaultString(runtime.noProxy, this._noProxy)), new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, this._socks5Proxy)), new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, this._socks5NetWork)), new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, this._maxIdleConns)), new TeaPair("retry", TeaConverter.buildMap(new TeaPair[]{new TeaPair("retryable", runtime.autoretry), new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))})), new TeaPair("backoff", TeaConverter.buildMap(new TeaPair[]{new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")), new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))})), new TeaPair("ignoreSSL", runtime.ignoreSSL)});
//        TeaRequest _lastRequest = null;
//        Exception _lastException = null;
//        TeaResponse _lastResponse = null;
//        long _now = System.currentTimeMillis();
//        int _retryTimes = 0;
//
//        while (Tea.allowRetry((Map) runtime_.get("retry"), _retryTimes, _now)) {
//            if (_retryTimes > 0) {
//                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
//                if (backoffTime > 0) {
//                    Tea.sleep(backoffTime);
//                }
//            }
//
//            ++_retryTimes;
//
//            Map t;
//            try {
//                TeaRequest request_ = new TeaRequest();
//                request_.protocol = Common.defaultString(this._protocol, protocol);
//                request_.method = method;
//                request_.pathname = "/";
//                request_.query = TeaConverter.merge(String.class, new Map[]{TeaConverter.buildMap(new TeaPair[]{new TeaPair("Action", action), new TeaPair("Format", "json"), new TeaPair("Version", version), new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()), new TeaPair("SignatureNonce", Common.getNonce())}), request.query});
//                Map<String, String> headers = this.getRpcHeaders();
//                if (Common.isUnset(headers)) {
//                    request_.headers = TeaConverter.buildMap(new TeaPair[]{new TeaPair("host", this._endpoint), new TeaPair("x-acs-version", version), new TeaPair("x-acs-action", action), new TeaPair("user-agent", this.getUserAgent())});
//                } else {
//                    request_.headers = TeaConverter.merge(String.class, new Map[]{TeaConverter.buildMap(new TeaPair[]{new TeaPair("host", this._endpoint), new TeaPair("x-acs-version", version), new TeaPair("x-acs-action", action), new TeaPair("user-agent", this.getUserAgent())}), headers});
//                }
//                CloudAccessBean auth = ActionFactory.credentials.get();
//                if ("v3_1_4".equals(auth.getVersion())) {
//                    request_.pathname = "/asapi/v3/";
//                }
//                if ("v3_1_4".equals(auth.getVersion()) || "v3_1_2".equals(auth.getVersion())) {
//                    request_.query.put("RegionId", super._regionId);
//                }
//                Map resp;
//                if (!Common.isUnset(request.body)) {
//                    Map<String, Object> m = Common.assertAsMap(request.body);
//                    resp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
//                    request_.body = Tea.toReadable(Common.toFormString(resp));
//                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
//                }
//                if (((HashMap) request.body).containsKey("Department")) {
//                    request_.headers.put("x-acs-organizationid", String.valueOf(((HashMap) request.body).get("Department")));
//                    request_.query.put("x-acs-organizationid", String.valueOf(((HashMap) request.body).get("Department")));
//                }
//                if (((HashMap) request.body).containsKey("ResourceGroup")) {
//                    request_.headers.put("x-acs-resourcegroupid", String.valueOf(((HashMap) request.body).get("ResourceGroup")));
//                    request_.query.put("x-acs-resourcegroupid", String.valueOf(((HashMap) request.body).get("ResourceGroup")));
//                }
//                String str;
//                if (!Common.equalString(authType, "Anonymous")) {
//                    String accessKeyId = this.getAccessKeyId();
//                    str = this.getAccessKeySecret();
//                    String securityToken = this.getSecurityToken();
//                    if (!Common.empty(securityToken)) {
//                        request_.query.put("SecurityToken", securityToken);
//                    }
//
//                    request_.query.put("SignatureMethod", "HMAC-SHA1");
//                    request_.query.put("SignatureVersion", "1.0");
//                    request_.query.put("AccessKeyId", accessKeyId);
//                    t = null;
//                    if (!Common.isUnset(request.body)) {
//                        t = Common.assertAsMap(request.body);
//                    }
//
//                    Map<String, String> signedParam = TeaConverter.merge(String.class, new Map[]{request_.query, com.aliyun.openapiutil.Client.query(t)});
//                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, str));
//                }
//
//
//                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
//                _lastResponse = response_;
//                Object arr;
//                Map res;
//                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
//                    arr = Common.readAsJSON(response_.body);
//                    res = Common.assertAsMap(arr);
//                    Object requestId = defaultAny(res.get("RequestId"), res.get("requestId"));
//                    throw new TeaException(TeaConverter.buildMap(new TeaPair[]{new TeaPair("code", "" + defaultAny(res.get("Code"), res.get("code")) + ""), new TeaPair("message", "code: " + response_.statusCode + ", " + defaultAny(res.get("Message"), res.get("message")) + " request id: " + requestId + ""), new TeaPair("data", res)}));
//                }
//
//                if (Common.equalString(bodyType, "binary")) {
//                    resp = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", response_.body), new TeaPair("headers", response_.headers)});
//                    res = resp;
//                    return res;
//                }
//
//                if (Common.equalString(bodyType, "byte")) {
//                    byte[] byt = Common.readAsBytes(response_.body);
//                    res = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", byt), new TeaPair("headers", response_.headers)});
//                    return res;
//                }
//
//                if (Common.equalString(bodyType, "string")) {
//                    str = Common.readAsString(response_.body);
//                    res = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", str), new TeaPair("headers", response_.headers)});
//                    return res;
//                }
//
//                if (!Common.equalString(bodyType, "json")) {
//                    //log.info("阿里私有云返回结果：{}", JSONObject.toJSONString(Common.readAsJSON(response_.body)));
//                    if (Common.equalString(bodyType, "array")) {
//                        arr = Common.readAsJSON(response_.body);
//                        res = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", arr), new TeaPair("headers", response_.headers)});
//                        return res;
//                    }
//
//                    resp = TeaConverter.buildMap(new TeaPair[]{new TeaPair("headers", response_.headers)});
//                    return resp;
//                }
//
//                arr = Common.readAsJSON(response_.body);
//                res = Common.assertAsMap(arr);
//                t = TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", res), new TeaPair("headers", response_.headers)});
//            } catch (Exception var26) {
//                if (Tea.isRetryable(var26)) {
//                    _lastException = var26;
//                    continue;
//                }
//
//                throw var26;
//            } finally {
//                if (!Common.isUnset(_lastResponse) && !Common.isUnset(_lastResponse.response) && !Common.isUnset(_lastResponse.response.body())) {
//                    _lastResponse.response.close();
//                }
//
//            }
//
//            return t;
//        }
//
//        throw new TeaUnretryableException((TeaRequest) _lastRequest, _lastException);
//    }
//
//}
