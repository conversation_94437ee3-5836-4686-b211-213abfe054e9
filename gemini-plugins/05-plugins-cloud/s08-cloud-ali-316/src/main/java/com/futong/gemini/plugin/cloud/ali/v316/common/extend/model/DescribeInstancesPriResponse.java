package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.ecs20140526.models.DescribeInstancesResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeInstancesPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeInstancesPriResponseBody body;

    @Data
    public static class DescribeInstancesPriResponseBody extends TeaModel{
        @NameInMap("Instances")
        public DescribeInstancesResponseBodyInstances instances;

        @NameInMap("NextToken")
        public String nextToken;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalCount")
        public Integer totalCount;


    }
    @Data
    public static class DescribeInstancesResponseBodyInstances extends TeaModel{
        @NameInMap("Instance")
        public java.util.List<DescribeInstancesResponseBodyInstancesInstances> instance;
    }

    @Data
    public static class DescribeInstancesResponseBodyInstancesInstances extends DescribeInstancesResponseBody.DescribeInstancesResponseBodyInstancesInstance {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
