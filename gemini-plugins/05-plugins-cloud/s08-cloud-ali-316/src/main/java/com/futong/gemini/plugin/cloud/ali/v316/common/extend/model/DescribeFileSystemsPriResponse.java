package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.nas20170626.models.DescribeFileSystemsResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class DescribeFileSystemsPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public FileSystemsResponseBody body;

    @Data
    public static class FileSystemsResponseBody extends TeaModel {
        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("TotalCount")
        public Integer totalCount;

        @NameInMap("FileSystems")
        public FileSystemsDataResponseBody fileSystems;
    }

    @Data
    public static class FileSystemsDataResponseBody extends TeaModel {
        @NameInMap("FileSystem")
        public List<DescribeFileSystemsResponseBodyFileSystemsFileSystems> fileSystem;
    }

    @Data
    public static class DescribeFileSystemsResponseBodyFileSystemsFileSystems extends DescribeFileSystemsResponseBody.DescribeFileSystemsResponseBodyFileSystemsFileSystem {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;

        /**
         * 私有云容量参数
         */
        @NameInMap("QuotaSize")
        public Long quotaSize;
    }
}
