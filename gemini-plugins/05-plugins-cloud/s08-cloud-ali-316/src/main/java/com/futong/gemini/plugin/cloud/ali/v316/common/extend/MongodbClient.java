//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.dds20151201.models.*;
//import com.aliyun.tea.*;
//import com.aliyun.tea.interceptor.InterceptorChain;
//import com.aliyun.teaopenapi.Client;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teautil.Common;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionFactory;
//import com.futong.bean.CloudAccessBean;
//
//import java.util.HashMap;
//public class MongodbClient extends com.aliyun.dds20151201.Client {
//
//    private final static InterceptorChain interceptorChain = InterceptorChain.create();
//
//    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "dds")));
//
//    /**
//     * Init client with Config
//     *
//     * @param config config contains the necessary information to create a client
//     */
//    public MongodbClient(Config config) throws Exception {
//        super(config);
//    }
//
//    @Override
//    public java.util.Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        TeaModel.validateParams(request, "request");
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getBody();
//        if (CollUtil.isEmpty(body)){
//            body = new HashMap();
//            request.setBody(body);
//        }
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_2".equals(auth.getVersion())) {
//            request.query.put("RegionId", super._regionId);
//        }
//        java.util.Map<String, Object> runtime_ = TeaConverter.buildMap(
//                new TeaPair("timeouted", "retry"),
//                new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, _readTimeout)),
//                new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
//                new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, _httpProxy)),
//                new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, _httpsProxy)),
//                new TeaPair("noProxy", Common.defaultString(runtime.noProxy, _noProxy)),
//                new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
//                new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
//                new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
//                new TeaPair("retry", TeaConverter.buildMap(
//                        new TeaPair("retryable", runtime.autoretry),
//                        new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))
//                )),
//                new TeaPair("backoff", TeaConverter.buildMap(
//                        new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")),
//                        new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))
//                )),
//                new TeaPair("ignoreSSL", runtime.ignoreSSL)
//        );
//
//        TeaRequest _lastRequest = null;
//        Exception _lastException = null;
//        TeaResponse _lastResponse = null;
//        long _now = System.currentTimeMillis();
//        int _retryTimes = 0;
//        while (Tea.allowRetry((java.util.Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
//            if (_retryTimes > 0) {
//                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
//                if (backoffTime > 0) {
//                    Tea.sleep(backoffTime);
//                }
//            }
//            _retryTimes = _retryTimes + 1;
//            try {
//                TeaRequest request_ = new TeaRequest();
//                request_.protocol = Common.defaultString(_protocol, protocol);
//                request_.method = method;
//                request_.pathname = "/";
//                request_.query = TeaConverter.merge(String.class,
//                        TeaConverter.buildMap(
//                                new TeaPair("Action", action),
//                                new TeaPair("Format", "json"),
//                                new TeaPair("Version", version),
//                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
//                                new TeaPair("SignatureNonce", Common.getNonce())
//                        ),
//                        request.query
//                );
//                java.util.Map<String, String> headers = this.getRpcHeaders();
//                if (Common.isUnset(headers)) {
//                    // endpoint is setted in product client
//                    request_.headers = TeaConverter.buildMap(
//                            new TeaPair("host", _endpoint),
//                            new TeaPair("x-acs-version", version),
//                            new TeaPair("x-acs-action", action),
//                            new TeaPair("user-agent", this.getUserAgent())
//                    );
//                } else {
//                    request_.headers = TeaConverter.merge(String.class,
//                            TeaConverter.buildMap(
//                                    new TeaPair("host", _endpoint),
//                                    new TeaPair("x-acs-version", version),
//                                    new TeaPair("x-acs-action", action),
//                                    new TeaPair("user-agent", this.getUserAgent())
//                            ),
//                            headers
//                    );
//                }
//
//                if (!Common.isUnset(request.body)) {
//                    java.util.Map<String, Object> m = Common.assertAsMap(request.body);
//                    java.util.Map<String, Object> tmp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
//                    request_.body = Tea.toReadable(Common.toFormString(tmp));
//                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
//                }
//
//                if (!Common.equalString(authType, "Anonymous")) {
//                    String accessKeyId = this.getAccessKeyId();
//                    String accessKeySecret = this.getAccessKeySecret();
//                    String securityToken = this.getSecurityToken();
//                    if (!Common.empty(securityToken)) {
//                        request_.query.put("SecurityToken", securityToken);
//                    }
//
//                    request_.query.put("SignatureMethod", "HMAC-SHA1");
//                    request_.query.put("SignatureVersion", "1.0");
//                    request_.query.put("AccessKeyId", accessKeyId);
//                    java.util.Map<String, Object> t = null;
//                    if (!Common.isUnset(request.body)) {
//                        t = Common.assertAsMap(request.body);
//                    }
//
//                    java.util.Map<String, String> signedParam = TeaConverter.merge(String.class,
//                            request_.query,
//                            com.aliyun.openapiutil.Client.query(t)
//                    );
//                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
//                }
//
//                _lastRequest = request_;
//                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
//                _lastResponse = response_;
//
//                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
//                    Object _res = Common.readAsJSON(response_.body);
//                    java.util.Map<String, Object> err = Common.assertAsMap(_res);
//                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
//                    throw new TeaException(TeaConverter.buildMap(
//                            new TeaPair("code", "" + Client.defaultAny(err.get("Code"), err.get("code")) + ""),
//                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
//                            new TeaPair("data", err)
//                    ));
//                }
//
//                if (Common.equalString(bodyType, "binary")) {
//                    java.util.Map<String, Object> resp = TeaConverter.buildMap(
//                            new TeaPair("body", response_.body),
//                            new TeaPair("headers", response_.headers)
//                    );
//                    return resp;
//                } else if (Common.equalString(bodyType, "byte")) {
//                    byte[] byt = Common.readAsBytes(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", byt),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "string")) {
//                    String str = Common.readAsString(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", str),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "json")) {
//                    Object obj = Common.readAsJSON(response_.body);
//                    java.util.Map<String, Object> res = Common.assertAsMap(obj);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", res),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "array")) {
//                    Object arr = Common.readAsJSON(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", arr),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else {
//                    return TeaConverter.buildMap(
//                            new TeaPair("headers", response_.headers)
//                    );
//                }
//
//            } catch (Exception e) {
//                if (Tea.isRetryable(e)) {
//                    _lastException = e;
//                    continue;
//                }
//                throw e;
//            } finally {
//                if (!Common.isUnset(_lastResponse)
//                        && !Common.isUnset(_lastResponse.response)
//                        && !Common.isUnset(_lastResponse.response.body())){
//                    _lastResponse.response.close();
//                }
//            }
//        }
//        throw new TeaUnretryableException(_lastRequest, _lastException);
//    }
//
//    @Override
//    public DescribeDBInstanceAttributeResponse describeDBInstanceAttributeWithOptions(DescribeDBInstanceAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstanceAttribute", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstanceAttributeResponse());
//    }
//
//    @Override
//    public DescribeDBInstancesResponse describeDBInstancesWithOptions(DescribeDBInstancesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstances", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstancesResponse());
//    }
//
//    @Override
//    public CreateDBInstanceResponse createDBInstanceWithOptions(CreateDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateDBInstance", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateDBInstanceResponse());
//    }
//
//    @Override
//    public DescribeRoleZoneInfoResponse describeRoleZoneInfoWithOptions(DescribeRoleZoneInfoRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeRoleZoneInfo", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRoleZoneInfoResponse());
//    }
//
//    @Override
//    public DescribeRegionsResponse describeRegionsWithOptions(DescribeRegionsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeRegions", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRegionsResponse());
//    }
//
//    @Override
//    public DescribeAvailableResourceResponse describeAvailableResourceWithOptions(DescribeAvailableResourceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAvailableResource", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAvailableResourceResponse());
//    }
//
//    @Override
//    public DescribeKernelReleaseNotesResponse describeKernelReleaseNotesWithOptions(DescribeKernelReleaseNotesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeKernelReleaseNotes", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeKernelReleaseNotesResponse());
//    }
//
//    @Override
//    public DescribeAvailableEngineVersionResponse describeAvailableEngineVersionWithOptions(DescribeAvailableEngineVersionRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAvailableEngineVersion", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAvailableEngineVersionResponse());
//    }
//
//    @Override
//    public DescribeAccountsResponse describeAccountsWithOptions(DescribeAccountsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAccounts", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAccountsResponse());
//    }
//
//    @Override
//    public DescribeBackupPolicyResponse describeBackupPolicyWithOptions(DescribeBackupPolicyRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBackupPolicy", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBackupPolicyResponse());
//    }
//
//    @Override
//    public DescribeBackupsResponse describeBackupsWithOptions(DescribeBackupsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBackups", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBackupsResponse());
//    }
//
//    @Override
//    public CheckRecoveryConditionResponse checkRecoveryConditionWithOptions(CheckRecoveryConditionRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CheckRecoveryCondition", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new CheckRecoveryConditionResponse());
//    }
//
//    @Override
//    public DescribeSecurityIpsResponse describeSecurityIpsWithOptions(DescribeSecurityIpsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityIps", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityIpsResponse());
//    }
//
//    @Override
//    public DescribeSecurityGroupConfigurationResponse describeSecurityGroupConfigurationWithOptions(DescribeSecurityGroupConfigurationRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityGroupConfiguration", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityGroupConfigurationResponse());
//    }
//
//    @Override
//    public DescribeDBInstanceTDEInfoResponse describeDBInstanceTDEInfoWithOptions(DescribeDBInstanceTDEInfoRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstanceTDEInfo", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstanceTDEInfoResponse());
//    }
//
//    @Override
//    public DescribeDBInstanceSSLResponse describeDBInstanceSSLWithOptions(DescribeDBInstanceSSLRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstanceSSL", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstanceSSLResponse());
//    }
//
//    @Override
//    public DescribeParameterModificationHistoryResponse describeParameterModificationHistoryWithOptions(DescribeParameterModificationHistoryRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeParameterModificationHistory", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeParameterModificationHistoryResponse());
//    }
//
//    @Override
//    public DescribeParametersResponse describeParametersWithOptions(DescribeParametersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeParameters", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeParametersResponse());
//    }
//
//    @Override
//    public DescribeUserEncryptionKeyListResponse describeUserEncryptionKeyListWithOptions(DescribeUserEncryptionKeyListRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeUserEncryptionKeyList", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeUserEncryptionKeyListResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceSpecResponse modifyDBInstanceSpecWithOptions(ModifyDBInstanceSpecRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceSpec", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceSpecResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceMaintainTimeResponse modifyDBInstanceMaintainTimeWithOptions(ModifyDBInstanceMaintainTimeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceMaintainTime", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceMaintainTimeResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceDescriptionResponse modifyDBInstanceDescriptionWithOptions(ModifyDBInstanceDescriptionRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceDescription", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceDescriptionResponse());
//    }
//
//    @Override
//    public UpgradeDBInstanceEngineVersionResponse upgradeDBInstanceEngineVersionWithOptions(UpgradeDBInstanceEngineVersionRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("UpgradeDBInstanceEngineVersion", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new UpgradeDBInstanceEngineVersionResponse());
//    }
//
//    @Override
//    public UpgradeDBInstanceKernelVersionResponse upgradeDBInstanceKernelVersionWithOptions(UpgradeDBInstanceKernelVersionRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("UpgradeDBInstanceKernelVersion", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new UpgradeDBInstanceKernelVersionResponse());
//    }
//
//    @Override
//    public MigrateToOtherZoneResponse migrateToOtherZoneWithOptions(MigrateToOtherZoneRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("MigrateToOtherZone", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new MigrateToOtherZoneResponse());
//    }
//
//    @Override
//    public MigrateAvailableZoneResponse migrateAvailableZoneWithOptions(MigrateAvailableZoneRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("MigrateAvailableZone", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new MigrateAvailableZoneResponse());
//    }
//
//    @Override
//    public DeleteDBInstanceResponse deleteDBInstanceWithOptions(DeleteDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteDBInstance", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteDBInstanceResponse());
//    }
//
//    @Override
//    public CreateShardingDBInstanceResponse createShardingDBInstanceWithOptions(CreateShardingDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateShardingDBInstance", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateShardingDBInstanceResponse());
//    }
//
//    @Override
//    public CreateNodeBatchResponse createNodeBatchWithOptions(CreateNodeBatchRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateNodeBatch", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateNodeBatchResponse());
//    }
//
//    @Override
//    public CreateNodeResponse createNodeWithOptions(CreateNodeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateNode", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateNodeResponse());
//    }
//
//    @Override
//    public ModifyNodeSpecBatchResponse modifyNodeSpecBatchWithOptions(ModifyNodeSpecBatchRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyNodeSpecBatch", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyNodeSpecBatchResponse());
//    }
//
//    @Override
//    public ModifyNodeSpecResponse modifyNodeSpecWithOptions(ModifyNodeSpecRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyNodeSpec", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyNodeSpecResponse());
//    }
//
//    @Override
//    public DeleteNodeResponse deleteNodeWithOptions(DeleteNodeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteNode", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteNodeResponse());
//    }
//
//    @Override
//    public RestartDBInstanceResponse restartDBInstanceWithOptions(RestartDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RestartDBInstance", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new RestartDBInstanceResponse());
//    }
//
//    @Override
//    public DescribeReplicaSetRoleResponse describeReplicaSetRoleWithOptions(DescribeReplicaSetRoleRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeReplicaSetRole", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeReplicaSetRoleResponse());
//    }
//
//    @Override
//    public DescribeShardingNetworkAddressResponse describeShardingNetworkAddressWithOptions(DescribeShardingNetworkAddressRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeShardingNetworkAddress", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeShardingNetworkAddressResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceNetworkTypeResponse modifyDBInstanceNetworkTypeWithOptions(ModifyDBInstanceNetworkTypeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceNetworkType", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceNetworkTypeResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceConnectionStringResponse modifyDBInstanceConnectionStringWithOptions(ModifyDBInstanceConnectionStringRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceConnectionString", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceConnectionStringResponse());
//    }
//
//    @Override
//    public ModifyInstanceVpcAuthModeResponse modifyInstanceVpcAuthModeWithOptions(ModifyInstanceVpcAuthModeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceVpcAuthMode", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceVpcAuthModeResponse());
//    }
//
//    @Override
//    public AllocatePublicNetworkAddressResponse allocatePublicNetworkAddressWithOptions(AllocatePublicNetworkAddressRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AllocatePublicNetworkAddress", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new AllocatePublicNetworkAddressResponse());
//    }
//
//    @Override
//    public ReleasePublicNetworkAddressResponse releasePublicNetworkAddressWithOptions(ReleasePublicNetworkAddressRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ReleasePublicNetworkAddress", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ReleasePublicNetworkAddressResponse());
//    }
//
//    @Override
//    public AllocateNodePrivateNetworkAddressResponse allocateNodePrivateNetworkAddressWithOptions(AllocateNodePrivateNetworkAddressRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AllocateNodePrivateNetworkAddress", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new AllocateNodePrivateNetworkAddressResponse());
//    }
//
//    @Override
//    public ReleaseNodePrivateNetworkAddressResponse releaseNodePrivateNetworkAddressWithOptions(ReleaseNodePrivateNetworkAddressRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ReleaseNodePrivateNetworkAddress", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ReleaseNodePrivateNetworkAddressResponse());
//    }
//
//    @Override
//    public ModifySecurityIpsResponse modifySecurityIpsWithOptions(ModifySecurityIpsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifySecurityIps", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySecurityIpsResponse());
//    }
//
//    @Override
//    public ModifySecurityGroupConfigurationResponse modifySecurityGroupConfigurationWithOptions(ModifySecurityGroupConfigurationRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifySecurityGroupConfiguration", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySecurityGroupConfigurationResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceTDEResponse modifyDBInstanceTDEWithOptions(ModifyDBInstanceTDERequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceTDE", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceTDEResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceSSLResponse modifyDBInstanceSSLWithOptions(ModifyDBInstanceSSLRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceSSL", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceSSLResponse());
//    }
//
//    @Override
//    public ModifyParametersResponse modifyParametersWithOptions(ModifyParametersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyParameters", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyParametersResponse());
//    }
//
//    @Override
//    public SwitchDBInstanceHAResponse switchDBInstanceHAWithOptions(SwitchDBInstanceHARequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SwitchDBInstanceHA", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new SwitchDBInstanceHAResponse());
//    }
//
//    @Override
//    public ModifyAccountDescriptionResponse modifyAccountDescriptionWithOptions(ModifyAccountDescriptionRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyAccountDescription", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyAccountDescriptionResponse());
//    }
//
//    @Override
//    public ResetAccountPasswordResponse resetAccountPasswordWithOptions(ResetAccountPasswordRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ResetAccountPassword", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ResetAccountPasswordResponse());
//    }
//
//    @Override
//    public CreateBackupResponse createBackupWithOptions(CreateBackupRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateBackup", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateBackupResponse());
//    }
//
//    @Override
//    public ModifyBackupPolicyResponse modifyBackupPolicyWithOptions(ModifyBackupPolicyRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyBackupPolicy", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyBackupPolicyResponse());
//    }
//
//    @Override
//    public RestoreDBInstanceResponse restoreDBInstanceWithOptions(RestoreDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RestoreDBInstance", "2015-12-01", "HTTPS", "POST", "AK", "json", req, runtime), new RestoreDBInstanceResponse());
//    }
//
//}
