package com.futong.gemini.plugin.cloud.ali.v316.service;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.v316.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
@Slf4j
public class CloudService {

    public static ThreadLocal<JSONObject> requestData = new ThreadLocal<>();

    public static <Q, R, C> FTAction<BaseCloudRequest> toFTAction(FTExecute<C, Q, R> exec) {
        return (BaseCloudRequest body) -> doAction(body, exec);
    }

    public static <Q, R, C> BaseDataResponse<R> doAction(BaseCloudRequest request, FTExecute<C, Q, R> exec) {
        try {
//            //加载请求参数
//            loadRequestData(request);
            return new BaseDataResponse<>(CloudClient.client.execute(request.getBody(), exec));
        } catch (Exception e) {
            String message = request.getAction().cname() + "失败!" + e.getMessage();
            log.error(message, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, message);
        }
    }

    public static void loadRequestData(JSONObject request) {
        if (request.containsKey("body")) {
            requestData.set(request.getJSONObject("body"));
        }
    }
    public static boolean defaultRegion(BaseCloudRequest request) {
        request.getBody().getCloud().put("regionId","cn-hangzhou-zjjk-d01");//默认北京
        return true;
    }

    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageNumber")) {
            request.getBody().getCloud().put("pageNumber", 1);//默认1
        }
        if (!request.getBody().getCloud().containsKey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 100);//默认100
        }
        return true;
    }

    public static boolean defaultPage50(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageNumber")) {
            request.getBody().getCloud().put("pageNumber", 1);//默认1
        }
        if (!request.getBody().getCloud().containsKey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 50);//默认100
        }
        return true;
    }

    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("startTime")) {
            request.getBody().getCloud().put("startTime", DateUtil.offsetDay(new Date(), -1).getTime());//一天前
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", new Date().getTime());//当前时间
        }
        return true;
    }


}
