package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import lombok.Data;
/**
 * 创建组织请求参数
 *
 * <AUTHOR>
 * @since 2024/10/16 17:03
 */
@Data
public class CreateOrganizationRequest extends TeaModel {

    /**
     * 组织名称。
     */
    @NameInMap("name")
    public String name;

    /**
     * 父组织ID。
     * 说明
     * root（根）组织ID为1。
     * 该参数为必填。
     */
    @NameInMap("parentId")
    public String parentId;

    /**
     * 父组织唯一识别码。
     */
    @NameInMap("orgParentUuid")
    public String orgParentUuid;

    /**
     * 组织UUID，该参数由系统自动生成。
     */
    @NameInMap("orgUuid")
    public String orgUuid;

    /**
     * 多云状态字段。默认为null。
     * master：主云。
     * slave：从云。
     */
    @NameInMap("multiCloudStatus")
    public String multiCloudStatus;

    /**
     * 额外信息。
     */
    @NameInMap("extraInfo")
    public String extraInfo;
}
