package com.futong.gemini.plugin.cloud.ali.v316.common.extend;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.aliyun.tea.*;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.log4j.Log4j2;
import org.apache.http.entity.StringEntity;

import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
@Log4j2
public class TongqueClient extends Client {

    public TongqueClient(Config config) throws Exception {
        super(config);
    }

    public JSONObject queryDrds(HashMap<String, Object> request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("query", request)
        ));
        Map<String, ?> map = this.doRPCRequest("HTTP", "GET", "/public/water_level", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body.data");
    }

    public Map<String, ?> doRPCRequest(String protocol, String method, String pathname, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        TeaModel.validateParams(request, "request");
        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", com.aliyun.teautil.Common.defaultNumber(60 * 1000, _readTimeout)),
                new TeaPair("connectTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", com.aliyun.teautil.Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", com.aliyun.teautil.Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", com.aliyun.teautil.Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", com.aliyun.teautil.Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", com.aliyun.teautil.Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", com.aliyun.teautil.Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", com.aliyun.teautil.Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", com.aliyun.teautil.Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", com.aliyun.teautil.Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = com.aliyun.teautil.Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = pathname;
                Map<String, String> headers = this.getRpcHeaders();
                if (com.aliyun.teautil.Common.isUnset(headers)) {
                    request_.headers = TeaConverter.buildMap(
                            new TeaPair("host", _endpoint)
                    );
                } else {
                    request_.headers = TeaConverter.merge(String.class,
                            TeaConverter.buildMap(
                                    new TeaPair("host", _endpoint)
                            ),
                            headers
                    );
                }
                request_.headers.put("content-type", "application/x-www-form-urlencoded");
                if (!com.aliyun.teautil.Common.isUnset(request.body)) {
                    Map<String, Object> m = com.aliyun.teautil.Common.assertAsMap(request.body);
                    Map<String, Object> tmp = com.aliyun.teautil.Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
                    request_.body = Tea.toReadable(com.aliyun.teautil.Common.toFormString(tmp));
                }

                //转换 body 格式计算签名使用
                String body = "";
                if (ObjectUtil.isNotEmpty(request.getBody())) {
                    body = JSONObject.toJSONString(request.getBody());
                    if ("{}".equals(body)) {
                        body = "";
                    }
                }
                request.getQuery().put("ak", this.getAccessKeyId());
                request.getQuery().put("stamp", String.valueOf(System.currentTimeMillis() / 1000));
                //转换 query 参数格式计算签名使用 并用于拼接域名
                String queryStr = formatUrlParam(request.getQuery(), "utf-8", true);
                String signature = getMD5Str(queryStr, this.getAccessKeySecret());
                if (!"".equals(queryStr.toString())) {
                    request_.pathname = pathname + "?" + queryStr + "&signature=" + signature;
                }

                //对 body 转换
                StringEntity data = new StringEntity(body, Charset.forName("UTF-8"));
                request_.body = data.getContent();

                //发起请求
                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, null);
                _lastResponse = response_;

                //解析返回结果
                if (com.aliyun.teautil.Common.is4xx(response_.statusCode) || com.aliyun.teautil.Common.is5xx(response_.statusCode)) {
                    Object _res = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> err = com.aliyun.teautil.Common.assertAsMap(_res);
                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + Client.defaultAny(err.get("Code"), err.get("code")) + ""),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
                            new TeaPair("data", err)
                    ));
                }

                //判断返回类型，转换对应的数据格式
                if (com.aliyun.teautil.Common.equalString(bodyType, "string")) {
                    String str = com.aliyun.teautil.Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "json")) {
                    Object obj = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> res = com.aliyun.teautil.Common.assertAsMap(obj);
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "array")) {
                    Object arr = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!com.aliyun.teautil.Common.isUnset(_lastResponse)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }

    public static String formatUrlParam(Map<String, String> param, String encode, Boolean isLower) {
        String params = "";
        Map<String, String> map = param;
        try {
            List<Map.Entry<String, String>> itmes = new ArrayList<Map.Entry<String, String>>(map.entrySet());
            //对所有传入的参数按照字段名从小到大排序
            //Collections.sort(items); 默认正序
            //可通过实现Comparator接口的compare方法来完成自定义排序
            Collections.sort(itmes, new Comparator<Map.Entry<String, String>>() {
                @Override
                public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                    return (o1.getKey().toString().compareTo(o2.getKey()));
                }
            });
            //构造URL 键值对的形式
            StringBuffer sb = new StringBuffer();
            for (Map.Entry<String, String> item : itmes) {
                if (null != item.getKey() && !"".equals(item.getKey())) {
                    String key = item.getKey();
                    String val = item.getValue();
                    val = URLEncoder.encode(val, encode);
                    if (isLower) {
                        sb.append(key.toLowerCase() + "=" + val);
                    } else {
                        sb.append(key + "=" + val);
                    }
                    sb.append("&");
                }
            }
            params = sb.toString();
            if (!params.isEmpty()) {
                params = params.substring(0, params.length() - 1);
            }
        } catch (Exception e) {
            return "";
        }
        return params;
    }

    /**
     * 对字符串md5加密
     *
     * @param str
     * @return
     * @throws Exception
     */
    public static String getMD5Str(String str, String seq) throws Exception {
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md.update(str.getBytes(StandardCharsets.UTF_8));
            md.update(seq.getBytes(StandardCharsets.UTF_8));
            // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的
            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形
            return new BigInteger(1, md.digest()).toString(16);
        } catch (Exception e) {
            throw new Exception("MD5加密出现错误，" + e.toString());
        }
    }
}
