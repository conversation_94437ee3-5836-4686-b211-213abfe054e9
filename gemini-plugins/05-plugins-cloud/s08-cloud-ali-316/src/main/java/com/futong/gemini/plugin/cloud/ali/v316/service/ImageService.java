package com.futong.gemini.plugin.cloud.ali.v316.service;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.ecs20140526.Client;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.v316.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
public class ImageService {
    public static BaseResponse deleteImages(BaseCloudRequest request) {
        for (JSONObject ci : request.getBody().getCis()) {
            request.getBody().getCloud().put("imageId", ci.get("openId"));
            CloudClient.client.execute(request.getBody(), Client::deleteImage);
        }
        return BaseResponse.SUCCESS;
    }
}
