package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaPair;
import lombok.extern.log4j.Log4j2;

import java.util.Map;
@Log4j2
public class OSSClientAsapi {

    private static Map<String, Object> product = TeaConverter.buildMap(new TeaPair("ProductName", "oss"));

    private String regionId;

    public OSSClientAsapi withRegionId(String regionId) {
        this.regionId = regionId;
        return this;
    }

//    public JSONObject listBuckets(IaasStorageObjectSyncBucketRequest request) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put(ASClient.REGIONID, request.getRegionId());
//        parameters.put("OpenApiAction", "GetService");
//        getCommonParameters(request, parameters);
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        parameters.put("AccountInfo",getAccountInfo(auth.getUsername(),auth.getPassword()));
//        HashMap<String, String> headers = new HashMap<>();
//        return ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//    }
//
//    public <Q extends BaseCloudSyncRequest> JSONObject listObjects(Q request, ListObjectsRequest req) {
//        String params = req.getParameters().get("Params");
//        JSONObject paramsJson = JSONObject.parseObject(params);
//        paramsJson.put("max-keys",1000);
//        if (StrUtil.isNotEmpty(req.getDelimiter())){
//            paramsJson.put("delimiter",req.getDelimiter());
//        }
//        if (StrUtil.isNotEmpty(req.getMarker())){
//            paramsJson.put("marker",req.getMarker());
//        }
//        if (StrUtil.isNotEmpty(req.getPrefix())){
//            paramsJson.put("prefix",req.getPrefix());
//        }
//        Map<String, Object> parameters = new HashMap<>();
//        params =  JSONObject.toJSONString(paramsJson);
//        parameters.put("params", params);
//        getCommonParameters(request, parameters);
//        parameters.put("OpenApiAction", "GetBucket");
//        Map<String,String> headers;
//        if (CollUtil.isNotEmpty(req.getHeaders())){
//            headers = req.getHeaders();
//        }else {
//            headers = new HashMap<>();
//            getCommonHeaders(headers);
//        }
//        log.info("文件请求参数：{}",parameters);
//        JSONObject jsonObject = CloudClient.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        req.setMarker(null);
//        log.info("文件返回结果：{}",JSONObject.toJSONString(jsonObject));
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data.ListBucketResult");
//    }
//
//    public JSONObject getBucketReferer(IaasStorageObjectSyncBucketRequest request, ListObjectsRequest req) {
//        String params = req.getParameters().get("Params");
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("params", params);
//        getCommonParameters(request, parameters);
//        parameters.put("OpenApiAction", "GetBucketReferer");
//
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, req.getHeaders(), parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject getBucketStat(IaasStorageObjectSyncBucketRequest request, ListObjectsRequest req) {
//        String params = req.getParameters().get("Params");
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("params", params);
//        getCommonParameters(request, parameters);
//        parameters.put("OpenApiAction", "GetBucketStat");
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, req.getHeaders(), parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject getBucketAcl(IaasStorageObjectSyncBucketRequest request, ListObjectsRequest req) {
//        String params = req.getParameters().get("Params");
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("params", params);
//        getCommonParameters(request, parameters);
//        parameters.put("OpenApiAction", "GetBucketAcl");
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, req.getHeaders(), parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject getBucketStorageCapacity(IaasStorageObjectSyncBucketRequest request, ListObjectsRequest req) {
//        String params = req.getParameters().get("Params");
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("params", params);
//        getCommonParameters(request, parameters);
//        parameters.put("OpenApiAction", "GetBucketStorageCapacity");
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, req.getHeaders(), parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject createBucket(IaasStorageObjectCreateBucketRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "PutBucket");
//        String parame = JSONObject.toJSONString(data);
//        parameters.put("params", parame);
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject putBucketEncryption(IaasStorageObjectCreateBucketRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "PutBucketEncryption");
//        String parame = JSONObject.toJSONString(data);
//        String Encryption = "<ServerSideEncryptionRule><ApplyServerSideEncryptionByDefault><SSEAlgorithm>" + data.get("content") + "</SSEAlgorithm></ApplyServerSideEncryptionByDefault></ServerSideEncryptionRule>";
//        parameters.put("Content", Encryption);
//        parameters.put("Params", parame);
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject putBucketReferer(IaasStorageObjectUpdateBucketRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "PutBucketReferer");
//        String parame = JSONObject.toJSONString(data);
//        String referer = "<RefererConfiguration>";
//        if (data.containsKey("AllowEmptyReferer")){
//            referer += "<AllowEmptyReferer>"+data.get("AllowEmptyReferer")+"</AllowEmptyReferer>";
//        }
//        if (data.containsKey("Referer")){
//            referer += "<RefererList><Referer>"+data.get("Referer")+"</Referer></RefererList>";
//        }
//        referer += "</RefererConfiguration>";
//        parameters.put("Content", referer);
//        parameters.put("Params", parame);
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject deleteBucket(IaasStorageObjectDeleteBucketRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "DeleteBucket");
//        parameters.put("Params", JSONObject.toJSONString(data));
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject createFolder(IaasStorageObjectCreateFolderRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "PutObject");
//        parameters.put("Params", JSONObject.toJSONString(data));
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject deleteObjects(IaasStorageObjectDeleteFileRequest request, Map<String, Object> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("AppAction", "DeleteObjects");
//        parameters.put("Params", data.get("params"));
//        parameters.put( "AppName", "one-console-app-oss");
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        parameters.put(ASClient.ACTION, "DoApi");
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject putObjectACL(IaasStorageObjectUpdateFileRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "PutObjectACL");
//        parameters.put("Params", JSONObject.toJSONString(data));
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject getObjectACL(IaasStorageObjectSyncFileRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "GetObjectACL");
//        parameters.put("Params", JSONObject.toJSONString(data));
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject generatePresignedUrl(IaasStorageObjectSyncFileRequest request, Map<String, String> data) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "GeneratePresignedUrl");
//        parameters.putAll(data);
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters, HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    public JSONObject getObject(IaasStorageObjectDownloadFileRequest request) {
//        Map<String, Object> parameters = new HashMap<>();
//        parameters.put("OpenApiAction", "GetObject");
//        HashMap<String, String> map = new HashMap<>();
//        map.put("BucketName","nt-test-oss-bai");
//        map.put("ObjectName","cmp2020-nt.tar.gz");
//        parameters.put("Params", JSONObject.toJSONString(map));
//        HashMap<String, String> fileMap = new HashMap<>();
//        fileMap.put("fileKey","cmp2020-nt.tar.gz");
//        parameters.put("fileRequestParams", JSONObject.toJSONString(fileMap));
//        HashMap<String, String> headers = new HashMap<>();
//        getCommonParameters(request, parameters);
//        getCommonHeaders(headers);
//        JSONObject jsonObject = ClientUtils.base.applyJsonAsapi(request, headers, parameters,HttpMethod.GET);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(jsonObject), "$.Data");
//    }
//
//    private static <Q extends BaseCloudRequest> void getCommonParameters(Q request, Map<String, Object> parameters) {
//        parameters.put(ASClient.REGIONID, request.getRegionId());
//        parameters.putAll(product);
//        parameters.put(ASClient.PRODUCT, "OneRouter");
//        parameters.put(ASClient.ACTION, "DoOpenApi");
//        parameters.put(ASClient.VERSION, "2018-12-12");
//    }
//
//
//    private static <Q extends BaseCloudRequest> void getCommonHeaders(Map<String, String> headers) {
//        JSONObject reqData = ActionFactory.requestData.get();
//        headers.put("x-acs-organizationId", reqData.getString("departmentId"));
//        headers.put("x-acs-resourceGroupId", reqData.getString("resourceGroupId"));
//    }
//
//    private static String getAccountInfo(String accessKeyId,String  accessKeySecret) {
//        return encryptBASE64(JSON.toJSONString(transferContainerAccount(accessKeyId,accessKeySecret)));
//    }
//
//    private static Map<String, Object> transferContainerAccount(String accessKeyId,String  accessKeySecret) {
//        //下列用户信息，accessKeyId、accessKeySecret必填，当为sts调用时securityToken必填
//        //其他参数没有填null，
//        Map<String, Object> map = new HashMap<>();
//        map.put("aliyunPk",null);
//        map.put("accountStructure",null);
//        map.put("accessKeyId",accessKeyId);
//        map.put("accessKeySecret",accessKeySecret);
//        map.put("partnerPk","");
//        //本机的ip
//        map.put("sourceIp","");
//        //sts 必填
//        map.put("securityToken","");
//        return map;
//    }
//
//    public static String encryptBASE64(String str) {
//        byte[] b = null;
//        String s = null;
//        try {
//            b = str.getBytes("utf-8");
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//        if (b != null) {
//            s = Base64.encodeBase64String(b);
//        }
//        return s;
//    }
}
