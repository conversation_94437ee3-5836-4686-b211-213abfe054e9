//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.r_kvstore20150101.models.*;
//import com.aliyun.tea.*;
//import com.aliyun.tea.interceptor.InterceptorChain;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionFactory;
//import com.futong.bean.CloudAccessBean;
//
//import java.util.HashMap;
//import java.util.Map;
//public class RedisClient extends com.aliyun.r_kvstore20150101.Client {
//
//    /**
//     * 产品类型
//     */
//    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "r-kvstore")));
//
//    private final static InterceptorChain interceptorChain = InterceptorChain.create();
//
//    public RedisClient(Config config) throws Exception {
//        super(config);
//    }
//
//    @Override
//    public CreateInstanceResponse createInstanceWithOptions(CreateInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("CreateInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime);
//        return TeaModel.toModel(map, new CreateInstanceResponse());
//    }
//
//    @Override
//    public DescribeInstancesResponse describeInstancesWithOptions(DescribeInstancesRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeInstances", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstancesResponse());
//    }
//
//    @Override
//    public DeleteInstanceResponse deleteInstanceWithOptions(DeleteInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteInstanceResponse());
//    }
//
//    @Override
//    public AddShardingNodeResponse addShardingNodeWithOptions(AddShardingNodeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AddShardingNode", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new AddShardingNodeResponse());
//    }
//
//    @Override
//    public CreateGlobalDistributeCacheResponse createGlobalDistributeCacheWithOptions(CreateGlobalDistributeCacheRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateGlobalDistributeCache", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateGlobalDistributeCacheResponse());
//    }
//
//    @Override
//    public DeleteShardingNodeResponse deleteShardingNodeWithOptions(DeleteShardingNodeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteShardingNode", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteShardingNodeResponse());
//    }
//
//    @Override
//    public ModifyInstanceSpecResponse modifyInstanceSpecWithOptions(ModifyInstanceSpecRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceSpec", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceSpecResponse());
//    }
//
//    @Override
//    public DescribeRegionsResponse describeRegionsWithOptions(DescribeRegionsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeRegions", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRegionsResponse());
//    }
//
//    @Override
//    public DescribeZonesResponse describeZonesWithOptions(DescribeZonesRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeZones", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeZonesResponse());
//    }
//
//    @Override
//    public DescribeAvailableResourceResponse describeAvailableResourceWithOptions(DescribeAvailableResourceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAvailableResource", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAvailableResourceResponse());
//    }
//
//    @Override
//    public TransformToPrePaidResponse transformToPrePaidWithOptions(TransformToPrePaidRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("TransformToPrePaid", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new TransformToPrePaidResponse());
//    }
//
//    @Override
//    public MigrateToOtherZoneResponse migrateToOtherZoneWithOptions(MigrateToOtherZoneRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("MigrateToOtherZone", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new MigrateToOtherZoneResponse());
//    }
//
//    @Override
//    public CreateTairInstanceResponse createTairInstanceWithOptions(CreateTairInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateTairInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateTairInstanceResponse());
//    }
//
//    @Override
//    public EnableAdditionalBandwidthResponse enableAdditionalBandwidthWithOptions(EnableAdditionalBandwidthRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("EnableAdditionalBandwidth", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new EnableAdditionalBandwidthResponse());
//    }
//
//    @Override
//    public ModifyInstanceAttributeResponse modifyInstanceAttributeWithOptions(ModifyInstanceAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceAttribute", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceAttributeResponse());
//    }
//
//    @Override
//    public ModifyInstanceMaintainTimeResponse modifyInstanceMaintainTimeWithOptions(ModifyInstanceMaintainTimeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceMaintainTime", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceMaintainTimeResponse());
//    }
//
//    @Override
//    public ModifyInstanceMajorVersionResponse modifyInstanceMajorVersionWithOptions(ModifyInstanceMajorVersionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceMajorVersion", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceMajorVersionResponse());
//    }
//
//    @Override
//    public ModifyInstanceMinorVersionResponse modifyInstanceMinorVersionWithOptions(ModifyInstanceMinorVersionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceMinorVersion", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceMinorVersionResponse());
//    }
//
//    @Override
//    public DescribeInstanceAttributeResponse describeInstanceAttributeWithOptions(DescribeInstanceAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeInstanceAttribute", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstanceAttributeResponse());
//    }
//
//    @Override
//    public DescribeGlobalDistributeCacheResponse describeGlobalDistributeCacheWithOptions(DescribeGlobalDistributeCacheRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeGlobalDistributeCache", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeGlobalDistributeCacheResponse());
//    }
//
//    @Override
//    public DescribeEngineVersionResponse describeEngineVersionWithOptions(DescribeEngineVersionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeEngineVersion", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeEngineVersionResponse());
//    }
//
//    public DescribeRoleZoneInfoResponse describeRoleZoneInfoWithOptions(DescribeRoleZoneInfoRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeRoleZoneInfo", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRoleZoneInfoResponse());
//    }
//
//    @Override
//    public DescribeClusterMemberInfoResponse describeClusterMemberInfoWithOptions(DescribeClusterMemberInfoRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeClusterMemberInfo", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeClusterMemberInfoResponse());
//    }
//
//    @Override
//    public DescribeTasksResponse describeTasksWithOptions(DescribeTasksRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeTasks", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeTasksResponse());
//    }
//
//    @Override
//    public DescribeDBInstanceNetInfoResponse describeDBInstanceNetInfoWithOptions(DescribeDBInstanceNetInfoRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstanceNetInfo", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstanceNetInfoResponse());
//    }
//
//    public DescribeLogicInstanceTopologyResponse describeLogicInstanceTopologyWithOptions(DescribeLogicInstanceTopologyRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeLogicInstanceTopology", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeLogicInstanceTopologyResponse());
//    }
//
//    @Override
//    public RestartInstanceResponse restartInstanceWithOptions(RestartInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RestartInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new RestartInstanceResponse());
//    }
//
//    @Override
//    public FlushInstanceResponse flushInstanceWithOptions(FlushInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("FlushInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new FlushInstanceResponse());
//    }
//
//    @Override
//    public FlushExpireKeysResponse flushExpireKeysWithOptions(FlushExpireKeysRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("FlushExpireKeys", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new FlushExpireKeysResponse());
//    }
//
//
//    @Override
//    public SwitchInstanceHAResponse switchInstanceHAWithOptions(SwitchInstanceHARequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SwitchInstanceHA", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new SwitchInstanceHAResponse());
//    }
//
//    @Override
//    public SyncDtsStatusResponse syncDtsStatusWithOptions(SyncDtsStatusRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SyncDtsStatus", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new SyncDtsStatusResponse());
//    }
//
//    @Override
//    public ModifyInstanceNetExpireTimeResponse modifyInstanceNetExpireTimeWithOptions(ModifyInstanceNetExpireTimeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceNetExpireTime", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceNetExpireTimeResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceConnectionStringResponse modifyDBInstanceConnectionStringWithOptions(ModifyDBInstanceConnectionStringRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceConnectionString", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceConnectionStringResponse());
//    }
//
//    @Override
//    public DescribeIntranetAttributeResponse describeIntranetAttributeWithOptions(DescribeIntranetAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeIntranetAttribute", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeIntranetAttributeResponse());
//    }
//
//    @Override
//    public SwitchNetworkResponse switchNetworkWithOptions(SwitchNetworkRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SwitchNetwork", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new SwitchNetworkResponse());
//    }
//
//    @Override
//    public AllocateInstancePublicConnectionResponse allocateInstancePublicConnectionWithOptions(AllocateInstancePublicConnectionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AllocateInstancePublicConnection", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new AllocateInstancePublicConnectionResponse());
//    }
//
//    @Override
//    public ReleaseInstancePublicConnectionResponse releaseInstancePublicConnectionWithOptions(ReleaseInstancePublicConnectionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ReleaseInstancePublicConnection", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ReleaseInstancePublicConnectionResponse());
//    }
//
//    @Override
//    public AllocateDirectConnectionResponse allocateDirectConnectionWithOptions(AllocateDirectConnectionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AllocateDirectConnection", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new AllocateDirectConnectionResponse());
//    }
//
//    @Override
//    public ReleaseDirectConnectionResponse releaseDirectConnectionWithOptions(ReleaseDirectConnectionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ReleaseDirectConnection", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ReleaseDirectConnectionResponse());
//    }
//
//    @Override
//    public SwitchInstanceProxyResponse switchInstanceProxyWithOptions(SwitchInstanceProxyRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SwitchInstanceProxy", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new SwitchInstanceProxyResponse());
//    }
//
//    @Override
//    public RenewAdditionalBandwidthResponse renewAdditionalBandwidthWithOptions(RenewAdditionalBandwidthRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RenewAdditionalBandwidth", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new RenewAdditionalBandwidthResponse());
//    }
//
//    @Override
//    public RenewInstanceResponse renewInstanceWithOptions(RenewInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RenewInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new RenewInstanceResponse());
//    }
//
//    @Override
//    public TransformInstanceChargeTypeResponse transformInstanceChargeTypeWithOptions(TransformInstanceChargeTypeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("TransformInstanceChargeType", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new TransformInstanceChargeTypeResponse());
//    }
//
//    @Override
//    public DescribeInstanceAutoRenewalAttributeResponse describeInstanceAutoRenewalAttributeWithOptions(DescribeInstanceAutoRenewalAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeInstanceAutoRenewalAttribute", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstanceAutoRenewalAttributeResponse());
//    }
//
//    @Override
//    public ModifyInstanceAutoRenewalAttributeResponse modifyInstanceAutoRenewalAttributeWithOptions(ModifyInstanceAutoRenewalAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceAutoRenewalAttribute", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceAutoRenewalAttributeResponse());
//    }
//
//    @Override
//    public DescribePriceResponse describePriceWithOptions(DescribePriceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribePrice", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribePriceResponse());
//    }
//
//    @Override
//    public DescribeAccountsResponse describeAccountsWithOptions(DescribeAccountsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAccounts", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAccountsResponse());
//    }
//
//    @Override
//    public CreateAccountResponse createAccountWithOptions(CreateAccountRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateAccount", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateAccountResponse());
//    }
//
//    @Override
//    public ModifyAccountDescriptionResponse modifyAccountDescriptionWithOptions(ModifyAccountDescriptionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyAccountDescription", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyAccountDescriptionResponse());
//    }
//
//    @Override
//    public ModifyAccountPasswordResponse modifyAccountPasswordWithOptions(ModifyAccountPasswordRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyAccountPassword", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyAccountPasswordResponse());
//    }
//
//    @Override
//    public GrantAccountPrivilegeResponse grantAccountPrivilegeWithOptions(GrantAccountPrivilegeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("GrantAccountPrivilege", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new GrantAccountPrivilegeResponse());
//    }
//
//    @Override
//    public ResetAccountPasswordResponse resetAccountPasswordWithOptions(ResetAccountPasswordRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ResetAccountPassword", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ResetAccountPasswordResponse());
//    }
//
//    @Override
//    public DeleteAccountResponse deleteAccountWithOptions(DeleteAccountRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteAccount", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteAccountResponse());
//    }
//
//    @Override
//    public DescribeBackupsResponse describeBackupsWithOptions(DescribeBackupsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBackups", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBackupsResponse());
//    }
//
//    @Override
//    public CreateBackupResponse createBackupWithOptions(CreateBackupRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateBackup", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateBackupResponse());
//    }
//
//    @Override
//    public DescribeBackupTasksResponse describeBackupTasksWithOptions(DescribeBackupTasksRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBackupTasks", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBackupTasksResponse());
//    }
//
//    @Override
//    public RestoreInstanceResponse restoreInstanceWithOptions(RestoreInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RestoreInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new RestoreInstanceResponse());
//    }
//
//    @Override
//    public DescribeBackupPolicyResponse describeBackupPolicyWithOptions(DescribeBackupPolicyRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBackupPolicy", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBackupPolicyResponse());
//    }
//
//    @Override
//    public ModifyBackupPolicyResponse modifyBackupPolicyWithOptions(ModifyBackupPolicyRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyBackupPolicy", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyBackupPolicyResponse());
//    }
//
//    @Override
//    public DescribeMonitorItemsResponse describeMonitorItemsWithOptions(DescribeMonitorItemsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeMonitorItems", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeMonitorItemsResponse());
//    }
//
//    @Override
//    public DescribeHistoryMonitorValuesResponse describeHistoryMonitorValuesWithOptions(DescribeHistoryMonitorValuesRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeHistoryMonitorValues", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeHistoryMonitorValuesResponse());
//    }
//
//    @Override
//    public ModifyAuditLogConfigResponse modifyAuditLogConfigWithOptions(ModifyAuditLogConfigRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyAuditLogConfig", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyAuditLogConfigResponse());
//    }
//
//    @Override
//    public DescribeAuditRecordsResponse describeAuditRecordsWithOptions(DescribeAuditRecordsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAuditRecords", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAuditRecordsResponse());
//    }
//
//    @Override
//    public DescribeRunningLogRecordsResponse describeRunningLogRecordsWithOptions(DescribeRunningLogRecordsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeRunningLogRecords", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRunningLogRecordsResponse());
//    }
//
//    @Override
//    public DescribeSlowLogRecordsResponse describeSlowLogRecordsWithOptions(DescribeSlowLogRecordsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeSlowLogRecords", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSlowLogRecordsResponse());
//    }
//
//    @Override
//    public InitializeKvstorePermissionResponse initializeKvstorePermissionWithOptions(InitializeKvstorePermissionRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("InitializeKvstorePermission", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new InitializeKvstorePermissionResponse());
//    }
//
//    @Override
//    public DescribeSecurityIpsResponse describeSecurityIpsWithOptions(DescribeSecurityIpsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityIps", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityIpsResponse());
//    }
//
//    @Override
//    public ModifySecurityIpsResponse modifySecurityIpsWithOptions(ModifySecurityIpsRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifySecurityIps", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySecurityIpsResponse());
//    }
//
//    @Override
//    public DescribeSecurityGroupConfigurationResponse describeSecurityGroupConfigurationWithOptions(DescribeSecurityGroupConfigurationRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityGroupConfiguration", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityGroupConfigurationResponse());
//    }
//
//    @Override
//    public ModifySecurityGroupConfigurationResponse modifySecurityGroupConfigurationWithOptions(ModifySecurityGroupConfigurationRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifySecurityGroupConfiguration", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySecurityGroupConfigurationResponse());
//    }
//
//    @Override
//    public DescribeInstanceSSLResponse describeInstanceSSLWithOptions(DescribeInstanceSSLRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeInstanceSSL", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstanceSSLResponse());
//    }
//
//    @Override
//    public ModifyInstanceSSLResponse modifyInstanceSSLWithOptions(ModifyInstanceSSLRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceSSL", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceSSLResponse());
//    }
//
//    @Override
//    public ModifyInstanceVpcAuthModeResponse modifyInstanceVpcAuthModeWithOptions(ModifyInstanceVpcAuthModeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceVpcAuthMode", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceVpcAuthModeResponse());
//    }
//
//    @Override
//    public ModifyInstanceConfigResponse modifyInstanceConfigWithOptions(ModifyInstanceConfigRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceConfig", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceConfigResponse());
//    }
//
//    @Override
//    public DescribeParameterTemplatesResponse describeParameterTemplatesWithOptions(DescribeParameterTemplatesRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeParameterTemplates", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeParameterTemplatesResponse());
//    }
//
//    @Override
//    public DescribeInstanceConfigResponse describeInstanceConfigWithOptions(DescribeInstanceConfigRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeInstanceConfig", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstanceConfigResponse());
//    }
//
//    @Override
//    public DescribeParametersResponse describeParametersWithOptions(DescribeParametersRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeParameters", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeParametersResponse());
//    }
//
//    @Override
//    public CreateCacheAnalysisTaskResponse createCacheAnalysisTaskWithOptions(CreateCacheAnalysisTaskRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateCacheAnalysisTask", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new CreateCacheAnalysisTaskResponse());
//    }
//
//    @Override
//    public DescribeCacheAnalysisReportResponse describeCacheAnalysisReportWithOptions(DescribeCacheAnalysisReportRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeCacheAnalysisReport", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeCacheAnalysisReportResponse());
//    }
//
//    @Override
//    public DescribeCacheAnalysisReportListResponse describeCacheAnalysisReportListWithOptions(DescribeCacheAnalysisReportListRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeCacheAnalysisReportList", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeCacheAnalysisReportListResponse());
//    }
//
//    @Override
//    public DescribeActiveOperationTasksResponse describeActiveOperationTasksWithOptions(DescribeActiveOperationTasksRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeActiveOperationTasks", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeActiveOperationTasksResponse());
//    }
//
//    @Override
//    public ModifyActiveOperationTasksResponse modifyActiveOperationTasksWithOptions(ModifyActiveOperationTasksRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyActiveOperationTasks", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyActiveOperationTasksResponse());
//    }
//
//    @Override
//    public DescribeDedicatedClusterInstanceListResponse describeDedicatedClusterInstanceListWithOptions(DescribeDedicatedClusterInstanceListRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDedicatedClusterInstanceList", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDedicatedClusterInstanceListResponse());
//    }
//
//    @Override
//    public RemoveSubInstanceResponse removeSubInstanceWithOptions(RemoveSubInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RemoveSubInstance", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new RemoveSubInstanceResponse());
//    }
//
//    @Override
//    public DescribeAuditLogConfigResponse describeAuditLogConfigWithOptions(DescribeAuditLogConfigRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAuditLogConfig", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAuditLogConfigResponse());
//    }
//
//    @Override
//    public DescribeEncryptionKeyListResponse describeEncryptionKeyListWithOptions(DescribeEncryptionKeyListRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeEncryptionKeyList", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeEncryptionKeyListResponse());
//    }
//
//    @Override
//    public DescribeInstanceTDEStatusResponse describeInstanceTDEStatusWithOptions(DescribeInstanceTDEStatusRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeInstanceTDEStatus", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstanceTDEStatusResponse());
//    }
//
//    @Override
//    public ModifyInstanceTDEResponse modifyInstanceTDEWithOptions(ModifyInstanceTDERequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceTDE", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceTDEResponse());
//    }
//
//    @Override
//    public ModifyIntranetAttributeResponse modifyIntranetAttributeWithOptions(ModifyIntranetAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyIntranetAttribute", "2015-01-01", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyIntranetAttributeResponse());
//    }
//
//    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        TeaModel.validateParams(request, "request");
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getBody();
//        if (CollUtil.isEmpty(body)){
//            body = new HashMap();
//            request.setBody(body);
//        }
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_2".equals(auth.getVersion())) {
//            request.query.put("RegionId", super._regionId);
//        }
//        Map<String, Object> runtime_ = TeaConverter.buildMap(
//                new TeaPair("timeouted", "retry"),
//                new TeaPair("readTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.readTimeout, _readTimeout)),
//                new TeaPair("connectTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
//                new TeaPair("httpProxy", com.aliyun.teautil.Common.defaultString(runtime.httpProxy, _httpProxy)),
//                new TeaPair("httpsProxy", com.aliyun.teautil.Common.defaultString(runtime.httpsProxy, _httpsProxy)),
//                new TeaPair("noProxy", com.aliyun.teautil.Common.defaultString(runtime.noProxy, _noProxy)),
//                new TeaPair("socks5Proxy", com.aliyun.teautil.Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
//                new TeaPair("socks5NetWork", com.aliyun.teautil.Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
//                new TeaPair("maxIdleConns", com.aliyun.teautil.Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
//                new TeaPair("retry", TeaConverter.buildMap(
//                        new TeaPair("retryable", runtime.autoretry),
//                        new TeaPair("maxAttempts", com.aliyun.teautil.Common.defaultNumber(runtime.maxAttempts, 3))
//                )),
//                new TeaPair("backoff", TeaConverter.buildMap(
//                        new TeaPair("policy", com.aliyun.teautil.Common.defaultString(runtime.backoffPolicy, "no")),
//                        new TeaPair("period", com.aliyun.teautil.Common.defaultNumber(runtime.backoffPeriod, 1))
//                )),
//                new TeaPair("ignoreSSL", runtime.ignoreSSL)
//        );
//
//        TeaRequest _lastRequest = null;
//        Exception _lastException = null;
//        TeaResponse _lastResponse = null;
//        long _now = System.currentTimeMillis();
//        int _retryTimes = 0;
//        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
//            if (_retryTimes > 0) {
//                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
//                if (backoffTime > 0) {
//                    Tea.sleep(backoffTime);
//                }
//            }
//            _retryTimes = _retryTimes + 1;
//            try {
//                TeaRequest request_ = new TeaRequest();
//                request_.protocol = com.aliyun.teautil.Common.defaultString(_protocol, protocol);
//                request_.method = method;
//                request_.pathname = "/";
//                request_.query = TeaConverter.merge(String.class,
//                        TeaConverter.buildMap(
//                                new TeaPair("Action", action),
//                                new TeaPair("Format", "json"),
//                                new TeaPair("Version", version),
//                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
//                                new TeaPair("SignatureNonce", com.aliyun.teautil.Common.getNonce())
//                        ),
//                        request.query
//                );
//                Map<String, String> headers = this.getRpcHeaders();
//                if (com.aliyun.teautil.Common.isUnset(headers)) {
//                    // endpoint is setted in product client
//                    request_.headers = TeaConverter.buildMap(
//                            new TeaPair("host", _endpoint),
//                            new TeaPair("x-acs-version", version),
//                            new TeaPair("x-acs-action", action),
//                            new TeaPair("user-agent", this.getUserAgent())
//                    );
//                } else {
//                    request_.headers = TeaConverter.merge(String.class,
//                            TeaConverter.buildMap(
//                                    new TeaPair("host", _endpoint),
//                                    new TeaPair("x-acs-version", version),
//                                    new TeaPair("x-acs-action", action),
//                                    new TeaPair("user-agent", this.getUserAgent())
//                            ),
//                            headers
//                    );
//                }
//
//                if (!com.aliyun.teautil.Common.isUnset(request.body)) {
//                    Map<String, Object> m = com.aliyun.teautil.Common.assertAsMap(request.body);
//                    Map<String, Object> tmp = com.aliyun.teautil.Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
//                    request_.body = Tea.toReadable(com.aliyun.teautil.Common.toFormString(tmp));
//                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
//                }
//
//                if (!com.aliyun.teautil.Common.equalString(authType, "Anonymous")) {
//                    String accessKeyId = this.getAccessKeyId();
//                    String accessKeySecret = this.getAccessKeySecret();
//                    String securityToken = this.getSecurityToken();
//                    if (!com.aliyun.teautil.Common.empty(securityToken)) {
//                        request_.query.put("SecurityToken", securityToken);
//                    }
//
//                    request_.query.put("SignatureMethod", "HMAC-SHA1");
//                    request_.query.put("SignatureVersion", "1.0");
//                    request_.query.put("AccessKeyId", accessKeyId);
//                    Map<String, Object> t = null;
//                    if (!com.aliyun.teautil.Common.isUnset(request.body)) {
//                        t = com.aliyun.teautil.Common.assertAsMap(request.body);
//                    }
//
//                    Map<String, String> signedParam = TeaConverter.merge(String.class,
//                            request_.query,
//                            com.aliyun.openapiutil.Client.query(t)
//                    );
//                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
//                }
//
//                _lastRequest = request_;
//                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
//                _lastResponse = response_;
//
//                if (com.aliyun.teautil.Common.is4xx(response_.statusCode) || com.aliyun.teautil.Common.is5xx(response_.statusCode)) {
//                    Object _res = com.aliyun.teautil.Common.readAsJSON(response_.body);
//                    Map<String, Object> err = com.aliyun.teautil.Common.assertAsMap(_res);
//                    Object requestId = com.aliyun.teaopenapi.Client.defaultAny(err.get("RequestId"), err.get("requestId"));
//                    throw new TeaException(TeaConverter.buildMap(
//                            new TeaPair("code", "" + com.aliyun.teaopenapi.Client.defaultAny(err.get("Code"), err.get("code")) + ""),
//                            new TeaPair("message", "code: " + response_.statusCode + ", " + com.aliyun.teaopenapi.Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
//                            new TeaPair("data", err)
//                    ));
//                }
//
//                if (com.aliyun.teautil.Common.equalString(bodyType, "binary")) {
//                    Map<String, Object> resp = TeaConverter.buildMap(
//                            new TeaPair("body", response_.body),
//                            new TeaPair("headers", response_.headers)
//                    );
//                    return resp;
//                } else if (com.aliyun.teautil.Common.equalString(bodyType, "byte")) {
//                    byte[] byt = com.aliyun.teautil.Common.readAsBytes(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", byt),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (com.aliyun.teautil.Common.equalString(bodyType, "string")) {
//                    String str = com.aliyun.teautil.Common.readAsString(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", str),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (com.aliyun.teautil.Common.equalString(bodyType, "json")) {
//                    Object obj = com.aliyun.teautil.Common.readAsJSON(response_.body);
//                    Map<String, Object> res = com.aliyun.teautil.Common.assertAsMap(obj);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", res),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (com.aliyun.teautil.Common.equalString(bodyType, "array")) {
//                    Object arr = com.aliyun.teautil.Common.readAsJSON(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", arr),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else {
//                    return TeaConverter.buildMap(
//                            new TeaPair("headers", response_.headers)
//                    );
//                }
//
//            } catch (Exception e) {
//                if (Tea.isRetryable(e)) {
//                    _lastException = e;
//                    continue;
//                }
//                throw e;
//            } finally {
//                if (!com.aliyun.teautil.Common.isUnset(_lastResponse)
//                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response)
//                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response.body())) {
//                    _lastResponse.response.close();
//                }
//            }
//        }
//        throw new TeaUnretryableException(_lastRequest, _lastException);
//    }
//
//}
