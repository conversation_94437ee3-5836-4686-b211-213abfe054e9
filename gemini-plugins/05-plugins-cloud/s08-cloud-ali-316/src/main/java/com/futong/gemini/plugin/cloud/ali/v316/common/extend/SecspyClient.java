package com.futong.gemini.plugin.cloud.ali.v316.common.extend;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.models.RuntimeOptions;

import java.util.Map;

import static com.aliyun.tea.TeaConverter.buildMap;
public class SecspyClient extends Client {

    private static final TeaPair product = new TeaPair("query", buildMap(new TeaPair("Product", "secspy")));

    /**
     * Init client with Config
     *
     * @param config config contains the necessary information to create a client
     */
    public SecspyClient(Config config) throws Exception {
        super(config);
    }

    public JSONObject describeNtaTrafficTopInfo(Map<String, Object> request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        request.put("Product", "secspy");
        OpenApiRequest req = OpenApiRequest.build(buildMap(
                new TeaPair("query",OpenApiRequest.toMap(request) )
        ));
        Map<String, ?> map = this.doRPCRequest("DescribeNtaTrafficTopInfo", "2019-08-05", "HTTPS", "GET", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
    }

    public JSONObject describeNtaTrafficTrend(Map<String, Object> request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        request.put("product", "secspy");
        OpenApiRequest req = OpenApiRequest.build(buildMap(
                new TeaPair("query",OpenApiRequest.toMap(request))
        ));
        Map<String, ?> map = this.doRPCRequest("DescribeNtaTrafficTrend", "2019-08-05", "HTTPS", "GET", "AK", "json", req, runtime);
        return (JSONObject) JSONPath.read(JSONObject.toJSONString(map), "$.body");
    }
}
