package com.futong.gemini.plugin.cloud.ali.v316.common.extend;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
public class OpenapiUtils {
    private static final int nonceLength = 16;

    public OpenapiUtils() {
    }

    public static String generateNonce() {
        SecureRandom random = new SecureRandom();
        StringBuilder result = new StringBuilder();
        int range = 9;

        for (int i = 0; i < 16; ++i) {
            result.append(random.nextInt(range + 1));
        }

        return result.toString();
    }

    public static String encrypt(String value) throws NoSuchAlgorithmException {
        if (value != null && value.length() != 0) {
            MessageDigest messageDigest = MessageDigest.getInstance("md5");
            byte[] buffer = value.getBytes(StandardCharsets.UTF_8);
            buffer = messageDigest.digest(buffer);
            StringBuilder result = new StringBuilder();
            byte[] var4 = buffer;
            int var5 = buffer.length;

            for (int var6 = 0; var6 < var5; ++var6) {
                byte b = var4[var6];
                result.append(String.format("%02x", b));
            }

            return result.toString();
        } else {
            return "";
        }
    }
}

