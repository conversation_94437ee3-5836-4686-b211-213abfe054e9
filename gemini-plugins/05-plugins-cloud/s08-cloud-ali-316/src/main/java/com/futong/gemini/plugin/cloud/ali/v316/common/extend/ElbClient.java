//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ObjectUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.slb20140515.models.*;
//import com.aliyun.tea.*;
//import com.aliyun.tea.interceptor.InterceptorChain;
//import com.aliyun.teaopenapi.Client;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teaopenapi.models.Params;
//import com.aliyun.teautil.Common;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionFactory;
//import com.futong.bean.CloudAccessBean;
//import com.futong.cloud.ali.pri.common.extend.model.DescribeLoadBalancersExtRequest;
//import com.futong.cloud.ali.pri.common.extend.model.DescribeLoadBalancersExtResponse;
//
//import java.util.HashMap;
//import java.util.Map;
//public class ElbClient extends com.aliyun.slb20140515.Client {
//
//    private final static InterceptorChain interceptorChain = InterceptorChain.create();
//
//    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "slb")));
//
//    /**
//     * Init client with Config
//     *
//     * @param config config contains the necessary information to create a client
//     */
//    public ElbClient(Config config) throws Exception {
//        super(config);
//        super._signatureAlgorithm = "v2";
//    }
//
//    public DescribeLoadBalancersExtResponse describeLoadBalancersExt(DescribeLoadBalancersExtRequest request) throws Exception {
//        Common.validateModel(request);
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, Object> query = OpenApiRequest.buildMap(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("query", com.aliyun.openapiutil.Client.query(query))}));
//        Params params = Params.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("action", "DescribeLoadBalancers"), new TeaPair("version", "2014-05-15"), new TeaPair("protocol", "HTTPS"), new TeaPair("pathname", "/"), new TeaPair("method", "POST"), new TeaPair("authType", "AK"), new TeaPair("style", "RPC"), new TeaPair("reqBodyType", "formData"), new TeaPair("bodyType", "json")}));
//        DescribeLoadBalancersExtResponse data = TeaModel.toModel(this.callApi(params, req, runtime), new DescribeLoadBalancersExtResponse());
//        return data;
//    }
//
//    public DescribeLoadBalancerAttributeResponse describeLoadBalancerAttributeWithOptions(DescribeLoadBalancerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        Map<String, Object> value = OpenApiRequest.buildMap(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("query", com.aliyun.openapiutil.Client.query(value))));
//        Params params = Params.build(TeaConverter.buildMap(
//                new TeaPair("action", "DescribeLoadBalancerAttribute"),
//                new TeaPair("version", "2014-05-15"),
//                new TeaPair("protocol", "HTTPS"),
//                new TeaPair("pathname", "/"),
//                new TeaPair("method", "POST"),
//                new TeaPair("authType", "AK"),
//                new TeaPair("style", "RPC"),
//                new TeaPair("reqBodyType", "formData"),
//                new TeaPair("bodyType", "json")
//        ));
//        return TeaModel.toModel(this.callApi(params, req, runtime), new DescribeLoadBalancerAttributeResponse());
//    }
//
//
//    @Override
//    public Map<String, ?> callApi(Params params, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        request.getQuery().put("Product", "slb");
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getBody();
//        if (CollUtil.isEmpty(body)) {
//            body = new HashMap();
//            request.setBody(body);
//        }
//        if (!body.containsKey("ResourceGroup") && request.getQuery().containsKey("ResourceGroup")) {
//            body.put("ResourceGroup", request.getQuery().get("ResourceGroup"));
//        } else if (!body.containsKey("ResourceGroup") && (ObjectUtil.isNotEmpty(reqData) && reqData.containsKey("resourceGroupId"))) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && request.getQuery().containsKey("Department")){
//            body.put("Department",request.getQuery().get("Department"));
//        }else if (!body.containsKey("Department") && (ObjectUtil.isNotEmpty(reqData) && reqData.containsKey("departmentId"))) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//
//        if ("v3_1_2".equals(auth.getVersion())) {
//            request.query.put("RegionId", super._regionId);
//        }
//
//        if (Common.isUnset(TeaModel.buildMap(params))) {
//            throw new TeaException(TeaConverter.buildMap(
//                    new TeaPair("code", "ParameterMissing"),
//                    new TeaPair("message", "'params' can not be unset")
//            ));
//        }
//
//        String _signatureAlgorithm = "v2";
//        if (Common.isUnset(_signatureAlgorithm) || !Common.equalString(_signatureAlgorithm, "v2")) {
//            return this.doRequest(params, request, runtime);
//        } else if (Common.equalString(params.style, "ROA") && Common.equalString(params.reqBodyType, "json")) {
//            return this.doROARequest(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else if (Common.equalString(params.style, "ROA")) {
//            return this.doROARequestWithForm(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else {
//            return this.doRPCRequest(params.action, params.version, params.protocol, params.method, params.authType, params.bodyType, request, runtime);
//        }
//
//    }
//
//    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        TeaModel.validateParams(request, "request");
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getBody();
//        if (CollUtil.isEmpty(body)) {
//            body = new HashMap();
//            request.setBody(body);
//        }
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        Map<String, Object> runtime_ = TeaConverter.buildMap(
//                new TeaPair("timeouted", "retry"),
//                new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, _readTimeout)),
//                new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
//                new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, _httpProxy)),
//                new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, _httpsProxy)),
//                new TeaPair("noProxy", Common.defaultString(runtime.noProxy, _noProxy)),
//                new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
//                new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
//                new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
//                new TeaPair("retry", TeaConverter.buildMap(
//                        new TeaPair("retryable", runtime.autoretry),
//                        new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))
//                )),
//                new TeaPair("backoff", TeaConverter.buildMap(
//                        new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")),
//                        new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))
//                )),
//                new TeaPair("ignoreSSL", runtime.ignoreSSL)
//        );
//
//        TeaRequest _lastRequest = null;
//        Exception _lastException = null;
//        TeaResponse _lastResponse = null;
//        long _now = System.currentTimeMillis();
//        int _retryTimes = 0;
//        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
//            if (_retryTimes > 0) {
//                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
//                if (backoffTime > 0) {
//                    Tea.sleep(backoffTime);
//                }
//            }
//            _retryTimes = _retryTimes + 1;
//            try {
//                TeaRequest request_ = new TeaRequest();
//                request_.protocol = Common.defaultString(_protocol, protocol);
//                request_.method = method;
//                request_.pathname = "/";
//                request_.query = TeaConverter.merge(String.class,
//                        TeaConverter.buildMap(
//                                new TeaPair("Action", action),
//                                new TeaPair("Format", "json"),
//                                new TeaPair("Version", version),
//                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
//                                new TeaPair("SignatureNonce", Common.getNonce())
//                        ),
//                        request.query
//                );
//                Map<String, String> headers = this.getRpcHeaders();
//                if (Common.isUnset(headers)) {
//                    // endpoint is setted in product client
//                    request_.headers = TeaConverter.buildMap(
//                            new TeaPair("host", _endpoint),
//                            new TeaPair("x-acs-version", version),
//                            new TeaPair("x-acs-action", action),
//                            new TeaPair("user-agent", this.getUserAgent())
//                    );
//                } else {
//                    request_.headers = TeaConverter.merge(String.class,
//                            TeaConverter.buildMap(
//                                    new TeaPair("host", _endpoint),
//                                    new TeaPair("x-acs-version", version),
//                                    new TeaPair("x-acs-action", action),
//                                    new TeaPair("user-agent", this.getUserAgent())
//                            ),
//                            headers
//                    );
//                }
//                CloudAccessBean auth = ActionFactory.credentials.get();
//                if ("v3_1_4".equals(auth.getVersion())) {
////                    String hostEndpoint = _endpoint.substring(0, _endpoint.indexOf("/asapi"));
////                    request_.headers.put("host", hostEndpoint);
//                    request_.pathname = "/asapi/v3/";
//                }
//                if (!Common.isUnset(request.body)) {
//                    Map<String, Object> m = Common.assertAsMap(request.body);
//                    Map<String, Object> tmp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
//                    request_.body = Tea.toReadable(Common.toFormString(tmp));
//                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
//                }
//
//                if (!Common.equalString(authType, "Anonymous")) {
//                    String accessKeyId = this.getAccessKeyId();
//                    String accessKeySecret = this.getAccessKeySecret();
//                    String securityToken = this.getSecurityToken();
//                    if (!Common.empty(securityToken)) {
//                        request_.query.put("SecurityToken", securityToken);
//                    }
//
//                    request_.query.put("SignatureMethod", "HMAC-SHA1");
//                    request_.query.put("SignatureVersion", "1.0");
//                    request_.query.put("AccessKeyId", accessKeyId);
//                    Map<String, Object> t = null;
//                    if (!Common.isUnset(request.body)) {
//                        t = Common.assertAsMap(request.body);
//                    }
//
//                    Map<String, String> signedParam = TeaConverter.merge(String.class,
//                            request_.query,
//                            com.aliyun.openapiutil.Client.query(t)
//                    );
//                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
//                }
//
//                _lastRequest = request_;
//                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
//                _lastResponse = response_;
//
//                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
//                    Object _res = Common.readAsJSON(response_.body);
//                    Map<String, Object> err = Common.assertAsMap(_res);
//                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
//                    throw new TeaException(TeaConverter.buildMap(
//                            new TeaPair("code", "" + Client.defaultAny(err.get("Code"), err.get("code")) + ""),
//                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
//                            new TeaPair("data", err)
//                    ));
//                }
//
//                if (Common.equalString(bodyType, "binary")) {
//                    Map<String, Object> resp = TeaConverter.buildMap(
//                            new TeaPair("body", response_.body),
//                            new TeaPair("headers", response_.headers)
//                    );
//                    return resp;
//                } else if (Common.equalString(bodyType, "byte")) {
//                    byte[] byt = Common.readAsBytes(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", byt),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "string")) {
//                    String str = Common.readAsString(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", str),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "json")) {
//                    Object obj = Common.readAsJSON(response_.body);
//                    Map<String, Object> res = Common.assertAsMap(obj);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", res),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "array")) {
//                    Object arr = Common.readAsJSON(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", arr),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else {
//                    return TeaConverter.buildMap(
//                            new TeaPair("headers", response_.headers)
//                    );
//                }
//
//            } catch (Exception e) {
//                if (Tea.isRetryable(e)) {
//                    _lastException = e;
//                    continue;
//                }
//                throw e;
//            } finally {
//                if (!Common.isUnset(_lastResponse)
//                        && !Common.isUnset(_lastResponse.response)
//                        && !Common.isUnset(_lastResponse.response.body())) {
//                    _lastResponse.response.close();
//                }
//            }
//        }
//        throw new TeaUnretryableException(_lastRequest, _lastException);
//    }
//
//    @Override
//    public CreateLoadBalancerResponse createLoadBalancerWithOptions(CreateLoadBalancerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateLoadBalancer", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateLoadBalancerResponse());
//    }
//
//    @Override
//    public SetLoadBalancerNameResponse setLoadBalancerNameWithOptions(SetLoadBalancerNameRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetLoadBalancerName", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetLoadBalancerNameResponse());
//    }
//
//    @Override
//    public SetLoadBalancerStatusResponse setLoadBalancerStatusWithOptions(SetLoadBalancerStatusRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetLoadBalancerStatus", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetLoadBalancerStatusResponse());
//    }
//
//    @Override
//    public DeleteLoadBalancerResponse deleteLoadBalancerWithOptions(DeleteLoadBalancerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteLoadBalancer", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteLoadBalancerResponse());
//    }
//
//    @Override
//    public DescribeLoadBalancerHTTPListenerAttributeResponse describeLoadBalancerHTTPListenerAttributeWithOptions(DescribeLoadBalancerHTTPListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeLoadBalancerHTTPListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeLoadBalancerHTTPListenerAttributeResponse());
//    }
//
//    @Override
//    public DescribeHealthStatusResponse describeHealthStatusWithOptions(DescribeHealthStatusRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeHealthStatus", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeHealthStatusResponse());
//    }
//
//    @Override
//    public CreateLoadBalancerHTTPListenerResponse createLoadBalancerHTTPListenerWithOptions(CreateLoadBalancerHTTPListenerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateLoadBalancerHTTPListener", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateLoadBalancerHTTPListenerResponse());
//    }
//
//    @Override
//    public SetLoadBalancerHTTPListenerAttributeResponse setLoadBalancerHTTPListenerAttributeWithOptions(SetLoadBalancerHTTPListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetLoadBalancerHTTPListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetLoadBalancerHTTPListenerAttributeResponse());
//    }
//
//    @Override
//    public DeleteLoadBalancerListenerResponse deleteLoadBalancerListenerWithOptions(DeleteLoadBalancerListenerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteLoadBalancerListener", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteLoadBalancerListenerResponse());
//    }
//
//    @Override
//    public StartLoadBalancerListenerResponse startLoadBalancerListenerWithOptions(StartLoadBalancerListenerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("StartLoadBalancerListener", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new StartLoadBalancerListenerResponse());
//    }
//
//    @Override
//    public StopLoadBalancerListenerResponse stopLoadBalancerListenerWithOptions(StopLoadBalancerListenerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("StopLoadBalancerListener", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new StopLoadBalancerListenerResponse());
//    }
//
//    @Override
//    public DescribeLoadBalancerHTTPSListenerAttributeResponse describeLoadBalancerHTTPSListenerAttributeWithOptions(DescribeLoadBalancerHTTPSListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeLoadBalancerHTTPSListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeLoadBalancerHTTPSListenerAttributeResponse());
//    }
//
//    @Override
//    public CreateLoadBalancerHTTPSListenerResponse createLoadBalancerHTTPSListenerWithOptions(CreateLoadBalancerHTTPSListenerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateLoadBalancerHTTPSListener", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateLoadBalancerHTTPSListenerResponse());
//    }
//
//    @Override
//    public SetLoadBalancerHTTPSListenerAttributeResponse setLoadBalancerHTTPSListenerAttributeWithOptions(SetLoadBalancerHTTPSListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetLoadBalancerHTTPSListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetLoadBalancerHTTPSListenerAttributeResponse());
//    }
//
//    @Override
//    public DescribeLoadBalancerTCPListenerAttributeResponse describeLoadBalancerTCPListenerAttributeWithOptions(DescribeLoadBalancerTCPListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeLoadBalancerTCPListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeLoadBalancerTCPListenerAttributeResponse());
//    }
//
//    @Override
//    public CreateLoadBalancerTCPListenerResponse createLoadBalancerTCPListenerWithOptions(CreateLoadBalancerTCPListenerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateLoadBalancerTCPListener", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateLoadBalancerTCPListenerResponse());
//    }
//
//    @Override
//    public SetLoadBalancerTCPListenerAttributeResponse setLoadBalancerTCPListenerAttributeWithOptions(SetLoadBalancerTCPListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetLoadBalancerTCPListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetLoadBalancerTCPListenerAttributeResponse());
//    }
//
//    @Override
//    public DescribeLoadBalancerUDPListenerAttributeResponse describeLoadBalancerUDPListenerAttributeWithOptions(DescribeLoadBalancerUDPListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeLoadBalancerUDPListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeLoadBalancerUDPListenerAttributeResponse());
//    }
//
//    @Override
//    public CreateLoadBalancerUDPListenerResponse createLoadBalancerUDPListenerWithOptions(CreateLoadBalancerUDPListenerRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateLoadBalancerUDPListener", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateLoadBalancerUDPListenerResponse());
//    }
//
//    @Override
//    public SetLoadBalancerUDPListenerAttributeResponse setLoadBalancerUDPListenerAttributeWithOptions(SetLoadBalancerUDPListenerAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetLoadBalancerUDPListenerAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetLoadBalancerUDPListenerAttributeResponse());
//    }
//
//    @Override
//    public DescribeAccessControlListsResponse describeAccessControlListsWithOptions(DescribeAccessControlListsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAccessControlLists", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAccessControlListsResponse());
//    }
//
//    @Override
//    public DescribeAccessControlListAttributeResponse describeAccessControlListAttributeWithOptions(DescribeAccessControlListAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAccessControlListAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAccessControlListAttributeResponse());
//    }
//
//    @Override
//    public CreateAccessControlListResponse createAccessControlListWithOptions(CreateAccessControlListRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateAccessControlList", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateAccessControlListResponse());
//    }
//
//    @Override
//    public SetAccessControlListAttributeResponse setAccessControlListAttributeWithOptions(SetAccessControlListAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetAccessControlListAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetAccessControlListAttributeResponse());
//    }
//
//    @Override
//    public DeleteAccessControlListResponse deleteAccessControlListWithOptions(DeleteAccessControlListRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteAccessControlList", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteAccessControlListResponse());
//    }
//
//    @Override
//    public AddAccessControlListEntryResponse addAccessControlListEntryWithOptions(AddAccessControlListEntryRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AddAccessControlListEntry", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new AddAccessControlListEntryResponse());
//    }
//
//
//    @Override
//    public RemoveAccessControlListEntryResponse removeAccessControlListEntryWithOptions(RemoveAccessControlListEntryRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RemoveAccessControlListEntry", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new RemoveAccessControlListEntryResponse());
//    }
//
//    @Override
//    public AddBackendServersResponse addBackendServersWithOptions(AddBackendServersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AddBackendServers", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new AddBackendServersResponse());
//    }
//
//    @Override
//    public SetBackendServersResponse setBackendServersWithOptions(SetBackendServersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetBackendServers", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetBackendServersResponse());
//    }
//
//    @Override
//    public RemoveBackendServersResponse removeBackendServersWithOptions(RemoveBackendServersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RemoveBackendServers", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new RemoveBackendServersResponse());
//    }
//
//    @Override
//    public DescribeVServerGroupsResponse describeVServerGroupsWithOptions(DescribeVServerGroupsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeVServerGroups", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeVServerGroupsResponse());
//    }
//
//    @Override
//    public CreateVServerGroupResponse createVServerGroupWithOptions(CreateVServerGroupRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateVServerGroup", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateVServerGroupResponse());
//    }
//
//    @Override
//    public SetVServerGroupAttributeResponse setVServerGroupAttributeWithOptions(SetVServerGroupAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetVServerGroupAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetVServerGroupAttributeResponse());
//    }
//
//    @Override
//    public ModifyVServerGroupBackendServersResponse modifyVServerGroupBackendServersWithOptions(ModifyVServerGroupBackendServersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyVServerGroupBackendServers", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyVServerGroupBackendServersResponse());
//    }
//
//    @Override
//    public DeleteVServerGroupResponse deleteVServerGroupWithOptions(DeleteVServerGroupRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteVServerGroup", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteVServerGroupResponse());
//    }
//
//    @Override
//    public DescribeVServerGroupAttributeResponse describeVServerGroupAttributeWithOptions(DescribeVServerGroupAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeVServerGroupAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeVServerGroupAttributeResponse());
//    }
//
//    @Override
//    public DescribeMasterSlaveServerGroupsResponse describeMasterSlaveServerGroupsWithOptions(DescribeMasterSlaveServerGroupsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeMasterSlaveServerGroups", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeMasterSlaveServerGroupsResponse());
//    }
//
//    @Override
//    public CreateMasterSlaveServerGroupResponse createMasterSlaveServerGroupWithOptions(CreateMasterSlaveServerGroupRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateMasterSlaveServerGroup", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateMasterSlaveServerGroupResponse());
//    }
//
//    @Override
//    public DeleteMasterSlaveServerGroupResponse deleteMasterSlaveServerGroupWithOptions(DeleteMasterSlaveServerGroupRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteMasterSlaveServerGroup", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteMasterSlaveServerGroupResponse());
//    }
//
//    @Override
//    public DescribeMasterSlaveServerGroupAttributeResponse describeMasterSlaveServerGroupAttributeWithOptions(DescribeMasterSlaveServerGroupAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeMasterSlaveServerGroupAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeMasterSlaveServerGroupAttributeResponse());
//    }
//
//    @Override
//    public AddVServerGroupBackendServersResponse addVServerGroupBackendServersWithOptions(AddVServerGroupBackendServersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AddVServerGroupBackendServers", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new AddVServerGroupBackendServersResponse());
//    }
//
//    @Override
//    public RemoveVServerGroupBackendServersResponse removeVServerGroupBackendServersWithOptions(RemoveVServerGroupBackendServersRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RemoveVServerGroupBackendServers", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new RemoveVServerGroupBackendServersResponse());
//    }
//
//    @Override
//    public DescribeRulesResponse describeRulesWithOptions(DescribeRulesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeRules", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeRulesResponse());
//    }
//
//    @Override
//    public CreateRulesResponse createRulesWithOptions(CreateRulesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("CreateRules", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateRulesResponse());
//    }
//
//    @Override
//    public SetRuleResponse setRuleWithOptions(SetRuleRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetRule", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetRuleResponse());
//    }
//
//    @Override
//    public DeleteRulesResponse deleteRulesWithOptions(DeleteRulesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteRules", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteRulesResponse());
//    }
//
//    @Override
//    public DescribeCACertificatesResponse describeCACertificatesWithOptions(DescribeCACertificatesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeCACertificates", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeCACertificatesResponse());
//    }
//
//    @Override
//    public DescribeServerCertificatesResponse describeServerCertificatesWithOptions(DescribeServerCertificatesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeServerCertificates", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeServerCertificatesResponse());
//    }
//
//    @Override
//    public UploadCACertificateResponse uploadCACertificateWithOptions(UploadCACertificateRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("UploadCACertificate", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new UploadCACertificateResponse());
//    }
//
//    @Override
//    public UploadServerCertificateResponse uploadServerCertificateWithOptions(UploadServerCertificateRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("UploadServerCertificate", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new UploadServerCertificateResponse());
//    }
//
//    @Override
//    public SetCACertificateNameResponse setCACertificateNameWithOptions(SetCACertificateNameRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetCACertificateName", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetCACertificateNameResponse());
//    }
//
//    @Override
//    public SetServerCertificateNameResponse setServerCertificateNameWithOptions(SetServerCertificateNameRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetServerCertificateName", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetServerCertificateNameResponse());
//    }
//
//    @Override
//    public DeleteCACertificateResponse deleteCACertificateWithOptions(DeleteCACertificateRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteCACertificate", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteCACertificateResponse());
//    }
//
//    @Override
//    public DeleteServerCertificateResponse deleteServerCertificateWithOptions(DeleteServerCertificateRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteServerCertificate", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteServerCertificateResponse());
//    }
//
//    @Override
//    public DescribeListenerAccessControlAttributeResponse describeListenerAccessControlAttributeWithOptions(DescribeListenerAccessControlAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeListenerAccessControlAttribute", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeListenerAccessControlAttributeResponse());
//    }
//
//    @Override
//    public AddListenerWhiteListItemResponse addListenerWhiteListItemWithOptions(AddListenerWhiteListItemRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("AddListenerWhiteListItem", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new AddListenerWhiteListItemResponse());
//    }
//
//    @Override
//    public RemoveListenerWhiteListItemResponse removeListenerWhiteListItemWithOptions(RemoveListenerWhiteListItemRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RemoveListenerWhiteListItem", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new RemoveListenerWhiteListItemResponse());
//    }
//
//    @Override
//    public SetListenerAccessControlStatusResponse setListenerAccessControlStatusWithOptions(SetListenerAccessControlStatusRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("SetListenerAccessControlStatus", "2014-05-15", "HTTPS", "POST", "AK", "json", req, runtime), new SetListenerAccessControlStatusResponse());
//    }
//
//}
