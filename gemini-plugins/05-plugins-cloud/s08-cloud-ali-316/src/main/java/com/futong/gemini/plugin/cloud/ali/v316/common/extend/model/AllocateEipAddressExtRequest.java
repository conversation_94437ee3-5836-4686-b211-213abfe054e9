package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.vpc20160428.models.AllocateEipAddressRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
@EqualsAndHashCode(callSuper = true)
@Data
public class AllocateEipAddressExtRequest extends AllocateEipAddressRequest implements IResourceGroup{

    @NameInMap("ResourceGroup")
    public String resourceGroup;

    @NameInMap("Department")
    public String department;

}
