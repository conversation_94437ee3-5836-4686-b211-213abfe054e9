//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.JSONPath;
//import com.aliyun.rds20140815.Client;
//import com.aliyun.rds20140815.models.*;
//import com.aliyun.tea.*;
//import com.aliyun.tea.interceptor.InterceptorChain;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teautil.Common;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionFactory;
//import com.futong.bean.CloudAccessBean;
//import com.futong.cloud.ali.pri.common.extend.model.DescribeDBInstancesPriResponse;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.HashMap;
//import java.util.Map;
//@Slf4j
//public class RdsClient extends Client {
//
//    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "rds")));
//
//    private final static InterceptorChain interceptorChain = InterceptorChain.create();
//
//    public RdsClient(Config config) throws Exception {
//        super(config);
//    }
//
//    @Override
//    public DescribeDBInstancesResponse describeDBInstancesWithOptions(DescribeDBInstancesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstances", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstancesResponse());
//    }
//
//
//    public DescribeDBInstancesPriResponse describeDBInstance(DescribeDBInstancesRequest request) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeDBInstances", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime);
//        return TeaModel.toModel(map, new DescribeDBInstancesPriResponse());
//    }
//
//    @Override
//    public DescribeDBInstanceAttributeResponse describeDBInstanceAttributeWithOptions(DescribeDBInstanceAttributeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstanceAttribute", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstanceAttributeResponse());
//    }
//
//    @Override
//    public CreateDBInstanceResponse createDBInstanceWithOptions(CreateDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)),
//                product));
//        return TeaModel.toModel(this.doRPCRequest("CreateDBInstance", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateDBInstanceResponse());
//    }
//
//    @Override
//    public ModifyDBInstanceSpecResponse modifyDBInstanceSpecWithOptions(ModifyDBInstanceSpecRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)),
//                product));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceSpec", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceSpecResponse());
//    }
//
//
//    @Override
//    public DeleteDBInstanceResponse deleteDBInstanceWithOptions(DeleteDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DeleteDBInstance", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteDBInstanceResponse());
//    }
//
//    @Override
//    public RestartDBInstanceResponse restartDBInstanceWithOptions(RestartDBInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("RestartDBInstance", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new RestartDBInstanceResponse());
//    }
//
//    @Override
//    public ListClassesResponse listClassesWithOptions(ListClassesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("ListClasses", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new ListClassesResponse());
//    }
//
//    @Override
//    public DescribeDBInstanceIPArrayListResponse describeDBInstanceIPArrayListWithOptions(DescribeDBInstanceIPArrayListRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstanceIPArrayList", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstanceIPArrayListResponse());
//    }
//
//    @Override
//    public ModifySecurityIpsResponse modifySecurityIpsWithOptions(ModifySecurityIpsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("ModifySecurityIps", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySecurityIpsResponse());
//    }
//
//    @Override
//    public DescribeAccountsResponse describeAccountsWithOptions(DescribeAccountsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAccounts", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAccountsResponse());
//    }
//
//    @Override
//    public CreateAccountResponse createAccountWithOptions(CreateAccountRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("CreateAccount", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateAccountResponse());
//    }
//
//    @Override
//    public DeleteAccountResponse deleteAccountWithOptions(DeleteAccountRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DeleteAccount", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteAccountResponse());
//    }
//
//    @Override
//    public ResetAccountPasswordResponse resetAccountPasswordWithOptions(ResetAccountPasswordRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("ResetAccountPassword", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new ResetAccountPasswordResponse());
//    }
//
//    @Override
//    public ResetAccountResponse resetAccountWithOptions(ResetAccountRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("ResetAccount", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new ResetAccountResponse());
//    }
//
//    @Override
//    public GrantAccountPrivilegeResponse grantAccountPrivilegeWithOptions(GrantAccountPrivilegeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("GrantAccountPrivilege", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new GrantAccountPrivilegeResponse());
//    }
//
//    @Override
//    public RevokeAccountPrivilegeResponse revokeAccountPrivilegeWithOptions(RevokeAccountPrivilegeRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("RevokeAccountPrivilege", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new RevokeAccountPrivilegeResponse());
//    }
//
//    @Override
//    public DescribeDatabasesResponse describeDatabasesWithOptions(DescribeDatabasesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDatabases", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDatabasesResponse());
//    }
//
//    @Override
//    public DescribeCharacterSetNameResponse describeCharacterSetNameWithOptions(DescribeCharacterSetNameRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeCharacterSetName", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeCharacterSetNameResponse());
//    }
//
//    @Override
//    public CreateDatabaseResponse createDatabaseWithOptions(CreateDatabaseRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("CreateDatabase", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new CreateDatabaseResponse());
//    }
//
//    @Override
//    public DeleteDatabaseResponse deleteDatabaseWithOptions(DeleteDatabaseRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DeleteDatabase", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteDatabaseResponse());
//    }
//
//    @Override
//    public DescribeDBInstanceNetInfoResponse describeDBInstanceNetInfoWithOptions(DescribeDBInstanceNetInfoRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstanceNetInfo", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstanceNetInfoResponse());
//    }
//
//    @Override
//    public DescribeBackupsResponse describeBackupsWithOptions(DescribeBackupsRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBackups", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBackupsResponse());
//    }
//
//    @Override
//    public DescribeBinlogFilesResponse describeBinlogFilesWithOptions(DescribeBinlogFilesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBinlogFiles", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBinlogFilesResponse());
//    }
//
//    @Override
//    public DescribeBackupPolicyResponse describeBackupPolicyWithOptions(DescribeBackupPolicyRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeBackupPolicy", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeBackupPolicyResponse());
//    }
//
//    @Override
//    public DescribeDBInstancePerformanceResponse describeDBInstancePerformanceWithOptions(DescribeDBInstancePerformanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeDBInstancePerformance", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDBInstancePerformanceResponse());
//    }
//
//
//    @Override
//    public DescribeAvailableZonesResponse describeAvailableZonesWithOptions(DescribeAvailableZonesRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("DescribeAvailableZones", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAvailableZonesResponse());
//    }
//
//    public JSONArray describeCpuTypes(Map<String, Object> hashmap) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", hashmap),
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeCpuTypes", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime);
//        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.Items.Item");
//    }
//
//    public JSONArray queryRegions(Map<String, Object> hashmap) throws Exception {
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", hashmap),
//                product
//        ));
//        Map<String, ?> map = this.doRPCRequest("DescribeRegions", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime);
//        return (JSONArray) JSONPath.read(JSONObject.toJSONString(map), "$.body.Regions.RDSRegion");
//    }
//
//    @Override
//    public LockAccountResponse lockAccountWithOptions(LockAccountRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("LockAccount", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new LockAccountResponse());
//    }
//
//    @Override
//    public UnlockAccountResponse unlockAccountWithOptions(UnlockAccountRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair("body", Common.toMap(request)), product));
//        return TeaModel.toModel(this.doRPCRequest("UnlockAccount", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new UnlockAccountResponse());
//    }
//
//    public ModifyDBInstanceDescriptionResponse modifyDBInstanceDescriptionWithOptions(ModifyDBInstanceDescriptionRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("ModifyDBInstanceDescription", "2014-08-15", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDBInstanceDescriptionResponse());
//    }
//
//    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        TeaModel.validateParams(request, "request");
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getBody();
//        if (CollUtil.isEmpty(body)){
//            body = new HashMap();
//            request.setBody(body);
//        }
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_4".equals(auth.getVersion()) && ("DescribeDBInstanceAttribute".equals(action))) {
//            request.query.put("regionId", super._regionId);
//        } if ("v3_1_5".equals(auth.getVersion()) && ("DescribeDBInstanceAttribute".equals(action))) {
//            request.query.put("regionId", super._regionId);
//        }else if ("v3_1_2".equals(auth.getVersion())) {
//            request.query.put("RegionId", super._regionId);
//        }
//        Map<String, Object> runtime_ = TeaConverter.buildMap(
//                new TeaPair("timeouted", "retry"),
//                new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, _readTimeout)),
//                new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
//                new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, _httpProxy)),
//                new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, _httpsProxy)),
//                new TeaPair("noProxy", Common.defaultString(runtime.noProxy, _noProxy)),
//                new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
//                new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
//                new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
//                new TeaPair("retry", TeaConverter.buildMap(
//                        new TeaPair("retryable", runtime.autoretry),
//                        new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))
//                )),
//                new TeaPair("backoff", TeaConverter.buildMap(
//                        new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")),
//                        new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))
//                )),
//                new TeaPair("ignoreSSL", runtime.ignoreSSL)
//        );
//
//        TeaRequest _lastRequest = null;
//        Exception _lastException = null;
//        TeaResponse _lastResponse = null;
//        long _now = System.currentTimeMillis();
//        int _retryTimes = 0;
//        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
//            if (_retryTimes > 0) {
//                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
//                if (backoffTime > 0) {
//                    Tea.sleep(backoffTime);
//                }
//            }
//            _retryTimes = _retryTimes + 1;
//            try {
//                TeaRequest request_ = new TeaRequest();
//                request_.protocol = Common.defaultString(_protocol, protocol);
//                request_.method = method;
//                request_.pathname = "/";
//                request_.query = TeaConverter.merge(String.class,
//                        TeaConverter.buildMap(
//                                new TeaPair("Action", action),
//                                new TeaPair("Format", "json"),
//                                new TeaPair("Version", version),
//                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
//                                new TeaPair("SignatureNonce", Common.getNonce())
//                        ),
//                        request.query
//                );
//                Map<String, String> headers = this.getRpcHeaders();
//                if (Common.isUnset(headers)) {
//                    // endpoint is setted in product client
//                    request_.headers = TeaConverter.buildMap(
//                            new TeaPair("host", _endpoint),
//                            new TeaPair("x-acs-version", version),
//                            new TeaPair("x-acs-action", action),
//                            new TeaPair("user-agent", this.getUserAgent())
//                    );
//                } else {
//                    request_.headers = TeaConverter.merge(String.class,
//                            TeaConverter.buildMap(
//                                    new TeaPair("host", _endpoint),
//                                    new TeaPair("x-acs-version", version),
//                                    new TeaPair("x-acs-action", action),
//                                    new TeaPair("user-agent", this.getUserAgent())
//                            ),
//                            headers
//                    );
//                }
//                if ("v3_1_4".equals(auth.getVersion())) {
////                    String hostEndpoint = _endpoint.substring(0, _endpoint.indexOf("/asapi"));
////                    request_.headers.put("host", hostEndpoint);
//                    request_.pathname = "/asapi/v3/";
//                }
//                if (!Common.isUnset(request.body)) {
//                    Map<String, Object> m = Common.assertAsMap(request.body);
//                    Map<String, Object> tmp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
//                    request_.body = Tea.toReadable(Common.toFormString(tmp));
//                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
//                }
//
//                if (!Common.equalString(authType, "Anonymous")) {
//                    String accessKeyId = this.getAccessKeyId();
//                    String accessKeySecret = this.getAccessKeySecret();
//                    String securityToken = this.getSecurityToken();
//                    if (!Common.empty(securityToken)) {
//                        request_.query.put("SecurityToken", securityToken);
//                    }
//
//                    request_.query.put("SignatureMethod", "HMAC-SHA1");
//                    request_.query.put("SignatureVersion", "1.0");
//                    request_.query.put("AccessKeyId", accessKeyId);
//                    Map<String, Object> t = null;
//                    if (!Common.isUnset(request.body)) {
//                        t = Common.assertAsMap(request.body);
//                    }
//
//                    Map<String, String> signedParam = TeaConverter.merge(String.class,
//                            request_.query,
//                            com.aliyun.openapiutil.Client.query(t)
//                    );
//                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
//                }
//
//                _lastRequest = request_;
//                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
//                _lastResponse = response_;
//
//                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
//                    Object _res = Common.readAsJSON(response_.body);
//                    Map<String, Object> err = Common.assertAsMap(_res);
//                    Object requestId = com.aliyun.teaopenapi.Client.defaultAny(err.get("RequestId"), err.get("requestId"));
//                    throw new TeaException(TeaConverter.buildMap(
//                            new TeaPair("code", "" + com.aliyun.teaopenapi.Client.defaultAny(err.get("Code"), err.get("code")) + ""),
//                            new TeaPair("message", "code: " + response_.statusCode + ", " + com.aliyun.teaopenapi.Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
//                            new TeaPair("data", err)
//                    ));
//                }
//
//                if (Common.equalString(bodyType, "binary")) {
//                    Map<String, Object> resp = TeaConverter.buildMap(
//                            new TeaPair("body", response_.body),
//                            new TeaPair("headers", response_.headers)
//                    );
//                    return resp;
//                } else if (Common.equalString(bodyType, "byte")) {
//                    byte[] byt = Common.readAsBytes(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", byt),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "string")) {
//                    String str = Common.readAsString(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", str),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "json")) {
//                    Object obj = Common.readAsJSON(response_.body);
//                    Map<String, Object> res = Common.assertAsMap(obj);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", res),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "array")) {
//                    Object arr = Common.readAsJSON(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", arr),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else {
//                    return TeaConverter.buildMap(
//                            new TeaPair("headers", response_.headers)
//                    );
//                }
//
//            } catch (Exception e) {
//                if (Tea.isRetryable(e)) {
//                    _lastException = e;
//                    continue;
//                }
//                throw e;
//            } finally {
//                if (!Common.isUnset(_lastResponse)
//                        && !Common.isUnset(_lastResponse.response)
//                        && !Common.isUnset(_lastResponse.response.body())) {
//                    _lastResponse.response.close();
//                }
//            }
//        }
//        throw new TeaUnretryableException(_lastRequest, _lastException);
//    }
//
//}
