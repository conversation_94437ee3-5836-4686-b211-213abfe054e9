//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ObjectUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.drds20190123.Client;
//import com.aliyun.drds20190123.models.DescribeDrdsInstancesRequest;
//import com.aliyun.tea.TeaConverter;
//import com.aliyun.tea.TeaException;
//import com.aliyun.tea.TeaModel;
//import com.aliyun.tea.TeaPair;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teaopenapi.models.Params;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionFactory;
//import com.futong.bean.CloudAccessBean;
//import com.futong.cloud.ali.pri.common.extend.model.DescribeDrdsInstancesPriResponse;
//
//import java.util.HashMap;
//public class DrdsClient extends Client {
//
//
//    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "Drds")));
//
//    public DrdsClient(Config config) throws Exception {
//        super(config);
//    }
//
//    public DescribeDrdsInstancesPriResponse describeDrdsInstancesExt(DescribeDrdsInstancesRequest request) throws Exception {
//        com.aliyun.teautil.Common.validateModel(request);
//        RuntimeOptions runtime = new RuntimeOptions();
//        java.util.Map<String, Object> query = new HashMap<>();
//        if (!com.aliyun.teautil.Common.isUnset(request.description)) {
//            query.put("Description", request.description);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.expired)) {
//            query.put("Expired", request.expired);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.mix)) {
//            query.put("Mix", request.mix);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.pageNumber)) {
//            query.put("PageNumber", request.pageNumber);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.pageSize)) {
//            query.put("PageSize", request.pageSize);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.productVersion)) {
//            query.put("ProductVersion", request.productVersion);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.regionId)) {
//            query.put("RegionId", request.regionId);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.resourceGroupId)) {
//            query.put("ResourceGroupId", request.resourceGroupId);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.tag)) {
//            query.put("Tag", request.tag);
//        }
//
//        if (!com.aliyun.teautil.Common.isUnset(request.type)) {
//            query.put("Type", request.type);
//        }
//
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("query", com.aliyun.openapiutil.Client.query(query))
//        ));
//        Params params = Params.build(TeaConverter.buildMap(
//                new TeaPair("action", "DescribeDrdsInstances"),
//                new TeaPair("version", "2019-01-23"),
//                new TeaPair("protocol", "HTTPS"),
//                new TeaPair("pathname", "/"),
//                new TeaPair("method", "POST"),
//                new TeaPair("authType", "AK"),
//                new TeaPair("style", "RPC"),
//                new TeaPair("reqBodyType", "formData"),
//                new TeaPair("bodyType", "json")
//        ));
//        return TeaModel.toModel(this.callApi(params, req, runtime), new DescribeDrdsInstancesPriResponse());
//    }
//
//    @Override
//    public java.util.Map<String, ?> callApi(Params params, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        request.getQuery().put("Product", "Drds");
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getBody();
//        if (CollUtil.isEmpty(body)) {
//            body = new HashMap();
//            request.setBody(body);
//        }
//        if (!body.containsKey("ResourceGroup") && request.getQuery().containsKey("ResourceGroup")) {
//            body.put("ResourceGroup", request.getQuery().get("ResourceGroup"));
//        } else if (!body.containsKey("ResourceGroup") && (ObjectUtil.isNotEmpty(reqData) && reqData.containsKey("resourceGroupId"))) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && request.getQuery().containsKey("Department")) {
//            body.put("Department", request.getQuery().get("Department"));
//        } else if (!body.containsKey("Department") && (ObjectUtil.isNotEmpty(reqData) && reqData.containsKey("departmentId"))) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        if ("v3_1_2".equals(auth.getVersion())) {
//            request.query.put("RegionId", super._regionId);
//        }
//        if (com.aliyun.teautil.Common.isUnset(TeaModel.buildMap(params))) {
//            throw new TeaException(TeaConverter.buildMap(
//                    new TeaPair("code", "ParameterMissing"),
//                    new TeaPair("message", "'params' can not be unset")
//            ));
//        }
//
//        String _signatureAlgorithm = "v2";
//        if (com.aliyun.teautil.Common.isUnset(_signatureAlgorithm) || !com.aliyun.teautil.Common.equalString(_signatureAlgorithm, "v2")) {
//            return this.doRequest(params, request, runtime);
//        } else if (com.aliyun.teautil.Common.equalString(params.style, "ROA") && com.aliyun.teautil.Common.equalString(params.reqBodyType, "json")) {
//            return this.doROARequest(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else if (com.aliyun.teautil.Common.equalString(params.style, "ROA")) {
//            return this.doROARequestWithForm(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else {
//            return this.doRPCRequest(params.action, params.version, params.protocol, params.method, params.authType, params.bodyType, request, runtime);
//        }
//
//    }
//}