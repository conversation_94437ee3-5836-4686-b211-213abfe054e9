package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeSnapshotsPriResponse  extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeSnapshotsPriResponseBody body;

    @Data
    public static class DescribeSnapshotsPriResponseBody extends TeaModel{
        @NameInMap("NextToken")
        public String nextToken;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("Snapshots")
        public DescribeSnapshotsResponseBodyData snapshots;

        @NameInMap("TotalCount")
        public Integer totalCount;


    }
    @Data
    public static class DescribeSnapshotsResponseBodyData extends TeaModel{
        @NameInMap("Snapshot")
        public java.util.List<DescribeSnapshotsResponseBodySnapshotsSnapshot> snapshot;
    }

    @Data
    public static class DescribeSnapshotsResponseBodySnapshotsSnapshot extends TeaModel {
        @NameInMap("Category")
        public String category;

        @NameInMap("CreationTime")
        public String creationTime;

        @NameInMap("Description")
        public String description;

        @NameInMap("Encrypted")
        public Boolean encrypted;

        @NameInMap("InstantAccess")
        public Boolean instantAccess;

        @NameInMap("InstantAccessRetentionDays")
        public Integer instantAccessRetentionDays;

        @NameInMap("KMSKeyId")
        public String KMSKeyId;

        @NameInMap("LastModifiedTime")
        public String lastModifiedTime;

        @NameInMap("ProductCode")
        public String productCode;

        @NameInMap("Progress")
        public String progress;

        @NameInMap("RemainTime")
        public Integer remainTime;

        @NameInMap("ResourceGroupId")
        public String resourceGroupId;

        @NameInMap("RetentionDays")
        public Integer retentionDays;

        @NameInMap("SnapshotId")
        public String snapshotId;

        @NameInMap("SnapshotName")
        public String snapshotName;

        @NameInMap("SnapshotSN")
        public String snapshotSN;

        @NameInMap("SnapshotType")
        public String snapshotType;

        @NameInMap("SourceDiskId")
        public String sourceDiskId;

        @NameInMap("SourceDiskSize")
        public String sourceDiskSize;

        @NameInMap("SourceDiskType")
        public String sourceDiskType;

        @NameInMap("SourceRegionId")
        public String sourceRegionId;

        @NameInMap("SourceSnapshotId")
        public String sourceSnapshotId;

        @NameInMap("SourceStorageType")
        public String sourceStorageType;

        @NameInMap("Status")
        public String status;
        @NameInMap("Usage")
        public String usage;

        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
