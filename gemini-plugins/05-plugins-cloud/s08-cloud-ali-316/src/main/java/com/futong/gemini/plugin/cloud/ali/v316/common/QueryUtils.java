package com.futong.gemini.plugin.cloud.ali.v316.common;
import lombok.extern.log4j.Log4j2;
@Log4j2
public class QueryUtils {
//
//    /**
//     * 获取组织及资源集数据
//     *
//     * @return
//     */
//    public static ListOrganizationResponse getOrganizationAndResourceGroup() {
//        //获取必须参数
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        //封装必须参数
//        ListOrganizationRequest query = new ListOrganizationRequest();
//        query.setEntity(new CloudPlatAccountOrganization()
//                .withCloudType(auth.getCloudType())
//                .withClientId(auth.getCmpId())
//        );
//        //封装固定参数
//        query.setSelect(new BaseServiceSelectRequest.QuerySelect()
//                .withRelation(true)
//                .withTable(CloudPlatResourceGroup.class)
//        );
//        //获取组织和资源集信息
//        ListOrganizationResponse response = CloudService.listSelect(query);
//        return response;
//    }
//
//    /**
//     * 获取资源集数据
//     *
//     * @return
//     */
//    public static ListResoureGroupResponse getResourceGroup() {
//        //获取必须参数
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        //封装必须参数
//        ListResoureGroupRequest query = new ListResoureGroupRequest();
//        query.setEntity(new CloudPlatResourceGroup()
//                .withCloudType(auth.getCloudType())
//                .withClientId(auth.getCmpId())
//        );
//        //封装固定参数
//        //获取组织和资源集信息
//        ListResoureGroupResponse response = CloudService.listSelect(query);
//        return response;
//    }
//
//    /**
//     * @param q        自定义请求类
//     * @param func     云上函数
//     * @param convert  自定义请求转换云上请求体函数
//     * @param toRes    云上结果集获取函数
//     * @param toResult 云上结果集请求转换数据库入库数据函数
//     * @param <Q>
//     * @param <Q1>
//     * @param <R1>
//     * @param <C>
//     * @param <R2>
//     * @param <R3>
//     * @param <R4>     入库结果集
//     * @return
//     */
//    public static <Q extends BaseCloudRequest, Q1, R1, C, R2, R3 extends List<R4>, R4> List<R4> syncByResourceGroup(Q q, FExcute<C, Q1, R1> func, FConvert<Q, Q1> convert, FConvert<R1, R2> toRes, FExcute<R2, Q, R3> toResult) {
//        List<R4> result = new ArrayList<>();
//        //判断是否是同步指定资源
//        if (StrUtil.isNotEmpty(q.getResourceGroupId()) || StrUtil.isNotEmpty(q.getDepartmentId()) || CollUtil.isNotEmpty(q.getResourceIds())) {
//            getData(q, func, convert, toRes, toResult, result);
//            return result;
//        }
//        //全量同步
//        ListResoureGroupResponse response = getResourceGroup();
//
//        //遍历组织信息
//        response.getData().forEach(t -> {
//            //执行云上操作
//            q.setDepartmentId(t.getOrganizationId());
//            q.setResourceGroupId(t.getResourceId());
//            getData(q, func, convert, toRes, toResult, result);
//        });
//        return result;
//    }
//
//    public static <Q extends BaseCloudSyncRequest, Q1, R1, C, R2, R3, R4, P> List<R3> syncByResourceGroup(Q q, P p, FExcute<C, Q1, R1> func, FConvert<Q, Q1> convert, FConvert<R1, R2> toRes, FMultiparameterExcute<R2, Q, P, R3> toResult) {
//        List<R3> result = new ArrayList<>();
//        //判断是否是同步指定资源
//        if (StrUtil.isNotEmpty(q.getResourceGroupId()) || StrUtil.isNotEmpty(q.getDepartmentId()) || CollUtil.isNotEmpty(q.getResourceIds())) {
//            getData(q, p, func, convert, toRes, toResult, result);
//            return result;
//        }
//        //全量同步
//        ListOrganizationResponse response = getOrganizationAndResourceGroup();
//        //遍历组织信息
//        response.getData().forEach(t -> {
//            //判断当前组织是否包含资源集信息，如果不包含则认为是无效组织
//            if (ObjectUtil.isNotNull(t.getRelation()) && t.getRelation().containsKey("CloudPlatResourceGroup") && CollUtil.isNotEmpty(t.getRelation().getJSONArray("CloudPlatResourceGroup"))) {
//                //解析当前组织下的资源集信息
//                List<CloudPlatResourceGroup> resourceGroupList = JSONArray.parseArray(t.getRelation().getJSONArray("CloudPlatResourceGroup").toJSONString(), CloudPlatResourceGroup.class);
//                //遍历组织下的资源集数据
//                resourceGroupList.forEach(r -> {
//                    //执行云上操作
//                    q.setDepartmentId(t.getResourceId());
//                    q.setResourceGroupId(r.getResourceId());
//                    getData(q, p, func, convert, toRes, toResult, result);
//                });
//            }
//        });
//        return result;
//    }
//
//    public static <Q extends BaseCloudSyncRequest, Q1, R1, C, R2 extends List<R3>, R3> List<R3> syncByResourceGroup(Q q, FExcute<C, Q1, R1> func, FConvert<Q, Q1> convert, FConvert<R1, R2> toRes) {
//        List<R3> result = new ArrayList<>();
//        //判断是否是同步指定资源
//        if (StrUtil.isNotEmpty(q.getResourceGroupId()) || StrUtil.isNotEmpty(q.getDepartmentId()) || CollUtil.isNotEmpty(q.getResourceIds())) {
//            getData(q, func, convert, toRes, result);
//            return result;
//        }
//        //全量同步
//        ListOrganizationResponse response = getOrganizationAndResourceGroup();
//        //遍历组织信息
//        response.getData().forEach(t -> {
//            //判断当前组织是否包含资源集信息，如果不包含则认为是无效组织
//            if (ObjectUtil.isNotNull(t.getRelation()) && t.getRelation().containsKey("CloudPlatResourceGroup") && CollUtil.isNotEmpty(t.getRelation().getJSONArray("CloudPlatResourceGroup"))) {
//                //解析当前组织下的资源集信息
//                List<CloudPlatResourceGroup> resourceGroupList = JSONArray.parseArray(t.getRelation().getJSONArray("CloudPlatResourceGroup").toJSONString(), CloudPlatResourceGroup.class);
//                //遍历组织下的资源集数据
//                resourceGroupList.forEach(r -> {
//                    //执行云上操作
//                    q.setDepartmentId(t.getResourceId());
//                    q.setResourceGroupId(r.getResourceId());
//                    getData(q, func, convert, toRes, result);
//                });
//            }
//        });
//        return result;
//    }
//
//    private static <Q extends BaseCloudSyncRequest, Q1, R1, C, R2 extends List<R3>, R3> List<R3> getData(Q q, FExcute<C, Q1, R1> func, FConvert<Q, Q1> convert, FConvert<R1, R2> toRes, List<R3> result) {
//        R1 r1 = q.bc.execute(q, func, convert);
//        //转换为入库数据
//        try {
//            //获取出云上结果集
//            R2 r2 = toRes.convert(r1);
//            //遍历结果集转换数据格式
//            result.addAll(r2);
//            return result;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    private static <Q extends BaseCloudSyncRequest, Q1, R1, C, R2, R3 extends List<R4>, R4> List<R4> getData(Q q, FExcute<C, Q1, R1> func, FConvert<Q, Q1> convert, FConvert<R1, R2> toRes, FExcute<R2, Q, R3> toResult, List<R4> result) {
//        R1 r1 = q.bc.execute(q, func, convert);
//        //转换为入库数据
//        try {
//            //获取出云上结果集
//            R2 r2 = toRes.convert(r1);
//            //将云上结果集转换为实体类集合
//            if (ObjectUtil.isNull(r2)) {
//                return null;
//            }
//            R3 r3 = toResult.apply(r2, q);
//            //遍历结果集转换数据格式
//            result.addAll(r3);
//            return result;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    private static <Q extends BaseCloudSyncRequest, Q1, R1, C, R2, R3, R4, P> List<R3> getData(Q q, P p, FExcute<C, Q1, R1> func, FConvert<Q, Q1> convert, FConvert<R1, R2> toRes, FMultiparameterExcute<R2, Q, P, R3> toResult, List<R3> result) {
//        R1 r1 = q.bc.execute(q, func, convert);
//        //转换为入库数据
//        try {
//            //获取出云上结果集
//            R2 r2 = toRes.convert(r1);
//            if (null == r2) {
//                return null;
//            }
//            //将云上结果集转换为实体类集合
//            R3 r3 = toResult.apply(r2, q, p);
//            //遍历结果集转换数据格式
//            result.add(r3);
//            return result;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    /**
//     * @param q1               自定义请求体
//     * @param q                云上请求体或继承云上请求体的请求类
//     * @param setDepartment    云上请求体或继承云上请求体的请求类赋值组织的函数
//     * @param setResourceGroup 云上请求体或继承云上请求体的请求类赋值资源集的函数
//     * @param limit            每页条数
//     * @param exec             执行云上请求的函数
//     * @param getList          获取云上返回的结果集
//     * @param setOffset        云上请求体或继承云上请求体的请求类赋当前页的函数
//     * @param setLimit         云上请求体或继承云上请求体的请求类赋每页条数的函数
//     * @param toResult         云上结果转换为入库数据的函数
//     * @param <R>
//     * @param <O>
//     * @param <Q>
//     * @param <N>
//     * @param <Q1>
//     * @param <R3>
//     * @return
//     */
//    public static <R, O, Q, N, Q1 extends BaseCloudSyncRequest, R3> List<R3> pageList(Q1 q1, Q q, Consumer<String> setDepartment, Consumer<String> setResourceGroup, N limit, LimitUtils.BQFunction<Q, R> exec, Function<R, Collection<O>> getList, Consumer<N> setOffset, Consumer<N> setLimit, BiFunction<O, Q1, R3> toResult) {
//        List<R3> result = new ArrayList<>();
//        //判断是否是同步指定资源
//        if (StrUtil.isNotEmpty(q1.getResourceGroupId()) || StrUtil.isNotEmpty(q1.getDepartmentId()) || CollUtil.isNotEmpty(q1.getResourceIds())) {
//            getPageList(q1, q, setDepartment, setResourceGroup, limit, exec, getList, setOffset, setLimit, toResult, result, q1.getResourceGroupId(), q1.getDepartmentId());
//            return result;
//        }
//        ListResoureGroupResponse response = getResourceGroup();
//        //遍历组织信息
//        response.getData().forEach(p -> {
//                    String resourceGroupId = p.getResourceId();
//                    String departmentId = p.getOrganizationId();
//                    q1.setDepartmentId(departmentId);
//                    q1.setResourceGroupId(resourceGroupId);
//                    //执行云上操作
//                    getPageList(q1, q, setDepartment, setResourceGroup, limit, exec, getList, setOffset, setLimit, toResult, result, resourceGroupId, departmentId);
//                });
//
//        return result;
//    }
//
//    public static <R, O, Q, N, Q1 extends BaseCloudSyncRequest, R3> List<R3> pageListResourceGroup(Q1 q1, Q q, Consumer<String> setDepartment, Consumer<String> setResourceGroup, N limit, LimitUtils.BQFunction<Q, R> exec, Function<R, Collection<O>> getList, Consumer<N> setOffset, Consumer<N> setLimit, BiFunction<O, Q1, R3> toResult) {
//        List<R3> result = new ArrayList<>();
//        //判断是否是同步指定资源
//        if (StrUtil.isNotEmpty(q1.getResourceGroupId()) || StrUtil.isNotEmpty(q1.getDepartmentId()) || CollUtil.isNotEmpty(q1.getResourceIds())) {
//            getPageList(q1, q, setDepartment, setResourceGroup, limit, exec, getList, setOffset, setLimit, toResult, result, q1.getResourceGroupId(), q1.getDepartmentId());
//            return result;
//        }
//        ListResoureGroupResponse response = getResourceGroup();
//        //遍历组织信息
//        response.getData().forEach(p -> {
//            //遍历组织下的资源集数
//            String resourceGroupId = p.getResourceId();
//            String departmentId = p.getOrganizationId();
//            q1.setDepartmentId(departmentId);
//            q1.setResourceGroupId(resourceGroupId);
//            //执行云上操作
//            getPageList(q1, q, setDepartment, setResourceGroup, limit, exec, getList, setOffset, setLimit, toResult, result, resourceGroupId, departmentId);
//
//        });
//        return result;
//    }
//
//    /**
//     * @param q1               自定义请求体
//     * @param q                云上请求体或继承云上请求体的请求类
//     * @param setDepartment    云上请求体或继承云上请求体的请求类赋值组织的函数
//     * @param setResourceGroup 云上请求体或继承云上请求体的请求类赋值资源集的函数
//     * @param limit            每页条数
//     * @param exec             执行云上请求的函数
//     * @param getList          获取云上返回的结果集
//     * @param setOffset        云上请求体或继承云上请求体的请求类赋当前页的函数
//     * @param setLimit         云上请求体或继承云上请求体的请求类赋每页条数的函数
//     * @param <R>
//     * @param <O>
//     * @param <Q>
//     * @param <N>
//     * @param <Q1>
//     * @param <R3>
//     * @param <P>
//     * @param <G>
//     * @return
//     */
//    public static <R, O, Q, N, Q1 extends BaseCloudSyncRequest, R3, P, G> List<O> pageList(Q1 q1, Q q, Consumer<String> setDepartment, Consumer<String> setResourceGroup, N limit, LimitUtils.BQFunction<Q, R> exec, Function<R, Collection<O>> getList, Consumer<N> setOffset, Consumer<N> setLimit) {
//        List<O> result = new ArrayList<>();
//        //判断是否是同步指定资源
//        if (StrUtil.isNotEmpty(q1.getResourceGroupId()) || StrUtil.isNotEmpty(q1.getDepartmentId()) || CollUtil.isNotEmpty(q1.getResourceIds())) {
//            getPageList(q1, q, setDepartment, setResourceGroup, limit, exec, getList, setOffset, setLimit, result, q1.getResourceGroupId(), q1.getDepartmentId());
//            return result;
//        }
//        ListOrganizationResponse response = getOrganizationAndResourceGroup();
//        //遍历组织信息
//        response.getData().forEach(p -> {
//            //判断当前组织是否包含资源集信息，如果不包含则认为是无效组织
//            if (ObjectUtil.isNotNull(p.getRelation()) && p.getRelation().containsKey("CloudPlatResourceGroup") && CollUtil.isNotEmpty(p.getRelation().getJSONArray("CloudPlatResourceGroup"))) {
//                //解析当前组织下的资源集信息
//                List<CloudPlatResourceGroup> resourceGroupList = JSONArray.parseArray(p.getRelation().getJSONArray("CloudPlatResourceGroup").toJSONString(), CloudPlatResourceGroup.class);
//                //遍历组织下的资源集数据
//                resourceGroupList.forEach(r -> {
//                    String resourceGroupId = r.getResourceId();
//                    String departmentId = p.getResourceId();
//                    q1.setDepartmentId(departmentId);
//                    q1.setResourceGroupId(resourceGroupId);
//                    //执行云上操作
//                    getPageList(q1, q, setDepartment, setResourceGroup, limit, exec, getList, setOffset, setLimit, result, resourceGroupId, departmentId);
//                });
//            }
//        });
//        return result;
//    }
//
//    private static <R, O, Q, N, Q1 extends BaseCloudSyncRequest, R3> void getPageList(Q1 q1, Q q, Consumer<String> setDepartment, Consumer<String> setResourceGroup, N limit, LimitUtils.BQFunction<Q, R> exec, Function<R, Collection<O>> getList, Consumer<N> setOffset, Consumer<N> setLimit, BiFunction<O, Q1, R3> toResult, List<R3> result, String resourceGroupId, String departmentId) {
//        setDepartment.accept(departmentId);
//        setResourceGroup.accept(resourceGroupId);
//        List<O> list = LimitUtils.query(q, limit, exec, getList, setOffset, setLimit, t -> t, LimitUtils::toPage);
//        for (O o : list) {
//            R3 r3 = toResult.apply(o, q1);
//            result.add(r3);
//        }
//    }
//
//    private static <R, O, Q, N, Q1 extends BaseCloudSyncRequest, R3> void getPageList(Q1 q1, Q q, Consumer<String> setDepartment, Consumer<String> setResourceGroup, N limit, LimitUtils.BQFunction<Q, R> exec, Function<R, Collection<O>> getList, Consumer<N> setOffset, Consumer<N> setLimit, List<O> result, String resourceGroupId, String departmentId) {
//        setDepartment.accept(departmentId);
//        setResourceGroup.accept(resourceGroupId);
//        List<O> list = LimitUtils.query(q, limit, exec, getList, setOffset, setLimit, t -> t, LimitUtils::toPage);
//        result.addAll(list);
//    }
//
}
