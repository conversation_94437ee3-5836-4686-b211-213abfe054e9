package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import lombok.Data;
/**
 * 创建用户
 *
 * <AUTHOR>
 * @since 2024/10/16 17:03
 */
@Data
public class BindResGroupToTenantRequest extends TeaModel {

    /**
     * 资源集ID。
     */
    @NameInMap("resource_group_id")
    public Integer resourceGroupId;

    /**
     * 待绑定的⽤户ID列表，多个用户ID间，以英文半角逗号（,）隔开
     */
    @NameInMap("ascm_user_ids")
    public String ascmUserIds;

    /**
     * 角色ID。
     */
    @NameInMap("ascm_role_id")
    public Integer ascmRoleId;

}
