package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import com.aliyun.vpc20160428.models.DescribeEipAddressesResponseBody;
import lombok.Data;

import java.util.List;
/**
 * description
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2023年06月16日
 */
@Data
public class DescribeEipAddressesPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public java.util.Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeEipAddressesPriResponseBody body;

    @Data
    public static class DescribeEipAddressesPriResponseBody extends TeaModel{
        @NameInMap("EipAddresses")
        public DescribeEipAddressesResponseBodyEipAddresses eipAddresses;
        @NameInMap("PageNumber")
        public Integer pageNumber;
        @NameInMap("PageSize")
        public Integer pageSize;
        @NameInMap("RequestId")
        public String requestId;
        @NameInMap("TotalCount")
        public Integer totalCount;


    }
    @Data
    public static class DescribeEipAddressesResponseBodyEipAddresses extends TeaModel{
        @NameInMap("EipAddress")
        public List<DescribeEipAddressesResponseBodyEipAddressesEipAddresss> eipAddress;
    }

    @Data
    public static class DescribeEipAddressesResponseBodyEipAddressesEipAddresss extends DescribeEipAddressesResponseBody.DescribeEipAddressesResponseBodyEipAddressesEipAddress {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
