//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.JSONPath;
//import com.aliyun.cs20151215.models.*;
//import com.aliyun.tea.*;
//import com.aliyun.tea.interceptor.InterceptorChain;
//import com.aliyun.teaopenapi.Client;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teaopenapi.models.Params;
//import com.aliyun.teautil.Common;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionException;
//import com.futong.action.ActionFactory;
//import com.futong.action.BaseResponse;
//import com.futong.bean.CloudAccessBean;
//
//import java.util.HashMap;
//import java.util.Map;
//public class K8sClient extends com.aliyun.cs20151215.Client {
//
//    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "cs")));
//
//    public K8sClient(Config config) throws Exception {
//        super(config);
//    }
//
//    public DescribeClustersV1Response describeClustersV1WithOptions(DescribeClustersV1Request request, Map<String, String> headers, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        Map<String, Object> query = OpenApiRequest.buildMap(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("headers", headers), new TeaPair("query", com.aliyun.openapiutil.Client.query(query))}));
//        Params params = Params.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("action", "DescribeClustersV1"), new TeaPair("version", "2015-12-15"), new TeaPair("protocol", "HTTPS"), new TeaPair("pathname", "/api/v1/clusters"), new TeaPair("method", "GET"), new TeaPair("authType", "AK"), new TeaPair("style", "ROA"), new TeaPair("reqBodyType", "json"), new TeaPair("bodyType", "json")}));
//        return (DescribeClustersV1Response) TeaModel.toModel(this.callApi(params, req, runtime), new DescribeClustersV1Response());
//    }
//
//
//    public DescribeKubernetesVersionMetadataResponse describeVersionMetadata(Map<String, Object> request) throws Exception {
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", request),
//                product
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, ?> map = this.doRPCRequest("DescribeKubernetesVersionMetadata", "2015-12-15", "HTTPS", "POST", "AK", "array", req, runtime);
//        return TeaModel.toModel(map, new DescribeKubernetesVersionMetadataResponse());
//    }
//
//    //检验 clusterId 相关的参数信息
//    public String checkClusterId(Map<String, Object> request) throws Exception {
//        if (CollUtil.isEmpty(request)) {
//            throw new ActionException(BaseResponse.AUTH_412, "参数不得为空");
//        } else if (!request.containsKey("ClusterId")) {
//            throw new ActionException(BaseResponse.AUTH_412, "ClusterId参数不得为空");
//        }
//        String clusterId = com.aliyun.openapiutil.Client.getEncodeParam((String) request.get("ClusterId"));
//        request.put("Product", "CS");
//        return clusterId;
//    }
//
//    public JSONObject describeClusterUserKubeconfigExtend(Map<String, Object> request) throws Exception {
//        String clusterId = checkClusterId(request);
//        request.put("pathname", "/k8s/" + clusterId + "/user_config");
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("query", request)
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, ?> result = this.doRPCRequest("DescribeClusterUserKubeconfig", "2015-12-15", "HTTPS", "POST", "AK", "json", req, runtime);
//        return (JSONObject) JSONPath.read(JSONObject.toJSONString(result), "$.body");
//    }
//
//    public JSONArray describeClusterResourcesExtend(Map<String, Object> request) throws Exception {
//        String clusterId = checkClusterId(request);
//        request.put("pathname", "/clusters/" + clusterId + "/resources");
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("query", request)
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, ?> result = this.doRPCRequest("DescribeClusterResources", "2015-12-15", "HTTPS", "POST", "AK", "array", req, runtime);
//        return (JSONArray) JSONPath.read(JSONObject.toJSONString(result), "$.body");
//    }
//
//    public JSONArray describeClusterLogsExtend(Map<String, Object> request) throws Exception {
//        String clusterId = checkClusterId(request);
//        request.put("pathname", "/clusters/" + clusterId + "/logs");
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("query", request)
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, ?> result = this.doRPCRequest("DescribeClusterLogs", "2015-12-15", "HTTPS", "POST", "AK", "array", req, runtime);
//        return (JSONArray) JSONPath.read(JSONObject.toJSONString(result), "$.body");
//    }
//
//    public DescribeClusterNodePoolsResponse describeClusterNodePoolsExtend(Map<String, Object> request) throws Exception {
//        String clusterId = checkClusterId(request);
//        request.put("pathname", "/clusters/" + clusterId + "/nodepools");
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("query", request)
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, ?> result = this.doRPCRequest("DescribeClusterNodePools", "2015-12-15", "HTTPS", "POST", "AK", "json", req, runtime);
//        return TeaModel.toModel(result, new DescribeClusterNodePoolsResponse());
//    }
//
//    public DescribeClusterNodesResponse describeClusterNodesExtend(Map<String, Object> request) throws Exception {
//        String clusterId = checkClusterId(request);
//        request.put("pathname", "/clusters/" + clusterId + "/nodes");
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("query", request)
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, ?> result = this.doRPCRequest("DescribeClusterNodes", "2015-12-15", "HTTPS", "POST", "AK", "array", req, runtime);
//        return TeaModel.toModel(result, new DescribeClusterNodesResponse());
//    }
//
//    public JSONArray describeClusterNamespaces(Map<String, Object> request) throws Exception {
//        String clusterId = checkClusterId(request);
//        request.put("pathname", "/k8s/" + clusterId + "/namespaces ");
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("query", request)
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        Map<String, ?> result = this.doRPCRequest("DescribeClusterNamespaces", "2015-12-15", "HTTPS", "POST", "AK", "array", req, runtime);
//        return (JSONArray) JSONPath.read(JSONObject.toJSONString(result), "$.body");
//    }
//
//    public CreateClusterResponse createClusterWithOptions(CreateClusterRequest request, Map<String, String> headers, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        Map<String, Object> body = new HashMap();
//        if (!Common.isUnset(request.addons)) {
//            body.put("addons", request.addons);
//        }
//
//        if (!Common.isUnset(request.apiAudiences)) {
//            body.put("api_audiences", request.apiAudiences);
//        }
//
//        if (!Common.isUnset(request.chargeType)) {
//            body.put("charge_type", request.chargeType);
//        }
//
//        if (!Common.isUnset(request.cisEnabled)) {
//            body.put("cis_enabled", request.cisEnabled);
//        }
//
//        if (!Common.isUnset(request.cloudMonitorFlags)) {
//            body.put("cloud_monitor_flags", request.cloudMonitorFlags);
//        }
//
//        if (!Common.isUnset(request.clusterDomain)) {
//            body.put("cluster_domain", request.clusterDomain);
//        }
//
//        if (!Common.isUnset(request.clusterSpec)) {
//            body.put("cluster_spec", request.clusterSpec);
//        }
//
//        if (!Common.isUnset(request.clusterType)) {
//            body.put("cluster_type", request.clusterType);
//        }
//
//        if (!Common.isUnset(request.containerCidr)) {
//            body.put("container_cidr", request.containerCidr);
//        }
//
//        if (!Common.isUnset(request.controlplaneLogComponents)) {
//            body.put("controlplane_log_components", request.controlplaneLogComponents);
//        }
//
//        if (!Common.isUnset(request.controlplaneLogProject)) {
//            body.put("controlplane_log_project", request.controlplaneLogProject);
//        }
//
//        if (!Common.isUnset(request.controlplaneLogTtl)) {
//            body.put("controlplane_log_ttl", request.controlplaneLogTtl);
//        }
//
//        if (!Common.isUnset(request.cpuPolicy)) {
//            body.put("cpu_policy", request.cpuPolicy);
//        }
//
//        if (!Common.isUnset(request.customSan)) {
//            body.put("custom_san", request.customSan);
//        }
//
//        if (!Common.isUnset(request.deletionProtection)) {
//            body.put("deletion_protection", request.deletionProtection);
//        }
//
//        if (!Common.isUnset(request.disableRollback)) {
//            body.put("disable_rollback", request.disableRollback);
//        }
//
//        if (!Common.isUnset(request.enableRrsa)) {
//            body.put("enable_rrsa", request.enableRrsa);
//        }
//
//        if (!Common.isUnset(request.encryptionProviderKey)) {
//            body.put("encryption_provider_key", request.encryptionProviderKey);
//        }
//
//        if (!Common.isUnset(request.endpointPublicAccess)) {
//            body.put("endpoint_public_access", request.endpointPublicAccess);
//        }
//
//        if (!Common.isUnset(request.formatDisk)) {
//            body.put("format_disk", request.formatDisk);
//        }
//
//        if (!Common.isUnset(request.imageId)) {
//            body.put("image_id", request.imageId);
//        }
//
//        if (!Common.isUnset(request.imageType)) {
//            body.put("image_type", request.imageType);
//        }
//
//        if (!Common.isUnset(request.instances)) {
//            body.put("instances", request.instances);
//        }
//
//        if (!Common.isUnset(request.isEnterpriseSecurityGroup)) {
//            body.put("is_enterprise_security_group", request.isEnterpriseSecurityGroup);
//        }
//
//        if (!Common.isUnset(request.keepInstanceName)) {
//            body.put("keep_instance_name", request.keepInstanceName);
//        }
//
//        if (!Common.isUnset(request.keyPair)) {
//            body.put("key_pair", request.keyPair);
//        }
//
//        if (!Common.isUnset(request.kubernetesVersion)) {
//            body.put("kubernetes_version", request.kubernetesVersion);
//        }
//
//        if (!Common.isUnset(request.loadBalancerSpec)) {
//            body.put("load_balancer_spec", request.loadBalancerSpec);
//        }
//
//        if (!Common.isUnset(request.loggingType)) {
//            body.put("logging_type", request.loggingType);
//        }
//
//        if (!Common.isUnset(request.loginPassword)) {
//            body.put("login_password", request.loginPassword);
//        }
//
//        if (!Common.isUnset(request.masterAutoRenew)) {
//            body.put("master_auto_renew", request.masterAutoRenew);
//        }
//
//        if (!Common.isUnset(request.masterAutoRenewPeriod)) {
//            body.put("master_auto_renew_period", request.masterAutoRenewPeriod);
//        }
//
//        if (!Common.isUnset(request.masterCount)) {
//            body.put("master_count", request.masterCount);
//        }
//
//        if (!Common.isUnset(request.masterInstanceChargeType)) {
//            body.put("master_instance_charge_type", request.masterInstanceChargeType);
//        }
//
//        if (!Common.isUnset(request.masterInstanceTypes)) {
//            body.put("master_instance_types", request.masterInstanceTypes);
//        }
//
//        if (!Common.isUnset(request.masterPeriod)) {
//            body.put("master_period", request.masterPeriod);
//        }
//
//        if (!Common.isUnset(request.masterPeriodUnit)) {
//            body.put("master_period_unit", request.masterPeriodUnit);
//        }
//
//        if (!Common.isUnset(request.masterSystemDiskCategory)) {
//            body.put("master_system_disk_category", request.masterSystemDiskCategory);
//        }
//
//        if (!Common.isUnset(request.masterSystemDiskPerformanceLevel)) {
//            body.put("master_system_disk_performance_level", request.masterSystemDiskPerformanceLevel);
//        }
//
//        if (!Common.isUnset(request.masterSystemDiskSize)) {
//            body.put("master_system_disk_size", request.masterSystemDiskSize);
//        }
//
//        if (!Common.isUnset(request.masterSystemDiskSnapshotPolicyId)) {
//            body.put("master_system_disk_snapshot_policy_id", request.masterSystemDiskSnapshotPolicyId);
//        }
//
//        if (!Common.isUnset(request.masterVswitchIds)) {
//            body.put("master_vswitch_ids", request.masterVswitchIds);
//        }
//
//        if (!Common.isUnset(request.name)) {
//            body.put("name", request.name);
//        }
//
//        if (!Common.isUnset(request.natGateway)) {
//            body.put("nat_gateway", request.natGateway);
//        }
//
//        if (!Common.isUnset(request.nodeCidrMask)) {
//            body.put("node_cidr_mask", request.nodeCidrMask);
//        }
//
//        if (!Common.isUnset(request.nodeNameMode)) {
//            body.put("node_name_mode", request.nodeNameMode);
//        }
//
//        if (!Common.isUnset(request.nodePortRange)) {
//            body.put("node_port_range", request.nodePortRange);
//        }
//
//        if (!Common.isUnset(request.numOfNodes)) {
//            body.put("num_of_nodes", request.numOfNodes);
//        }
//
//        if (!Common.isUnset(request.osType)) {
//            body.put("os_type", request.osType);
//        }
//
//        if (!Common.isUnset(request.period)) {
//            body.put("period", request.period);
//        }
//
//        if (!Common.isUnset(request.periodUnit)) {
//            body.put("period_unit", request.periodUnit);
//        }
//
//        if (!Common.isUnset(request.platform)) {
//            body.put("platform", request.platform);
//        }
//
//        if (!Common.isUnset(request.podVswitchIds)) {
//            body.put("pod_vswitch_ids", request.podVswitchIds);
//        }
//
//        if (!Common.isUnset(request.profile)) {
//            body.put("profile", request.profile);
//        }
//
//        if (!Common.isUnset(request.proxyMode)) {
//            body.put("proxy_mode", request.proxyMode);
//        }
//
//        if (!Common.isUnset(request.rdsInstances)) {
//            body.put("rds_instances", request.rdsInstances);
//        }
//
//        if (!Common.isUnset(request.regionId)) {
//            body.put("region_id", request.regionId);
//        }
//
//        if (!Common.isUnset(request.resourceGroupId)) {
//            body.put("resource_group_id", request.resourceGroupId);
//        }
//
//        if (!Common.isUnset(TeaModel.buildMap(request.runtime))) {
//            body.put("runtime", request.runtime);
//        }
//
//        if (!Common.isUnset(request.securityGroupId)) {
//            body.put("security_group_id", request.securityGroupId);
//        }
//
//        if (!Common.isUnset(request.serviceAccountIssuer)) {
//            body.put("service_account_issuer", request.serviceAccountIssuer);
//        }
//
//        if (!Common.isUnset(request.serviceCidr)) {
//            body.put("service_cidr", request.serviceCidr);
//        }
//
//        if (!Common.isUnset(request.serviceDiscoveryTypes)) {
//            body.put("service_discovery_types", request.serviceDiscoveryTypes);
//        }
//
//        if (!Common.isUnset(request.snatEntry)) {
//            body.put("snat_entry", request.snatEntry);
//        }
//
//        if (!Common.isUnset(request.socEnabled)) {
//            body.put("soc_enabled", request.socEnabled);
//        }
//
//        if (!Common.isUnset(request.sshFlags)) {
//            body.put("ssh_flags", request.sshFlags);
//        }
//
//        if (!Common.isUnset(request.tags)) {
//            body.put("tags", request.tags);
//        }
//
//        if (!Common.isUnset(request.taints)) {
//            body.put("taints", request.taints);
//        }
//
//        if (!Common.isUnset(request.timeoutMins)) {
//            body.put("timeout_mins", request.timeoutMins);
//        }
//
//        if (!Common.isUnset(request.timezone)) {
//            body.put("timezone", request.timezone);
//        }
//
//        if (!Common.isUnset(request.userCa)) {
//            body.put("user_ca", request.userCa);
//        }
//
//        if (!Common.isUnset(request.userData)) {
//            body.put("user_data", request.userData);
//        }
//
//        if (!Common.isUnset(request.vpcid)) {
//            body.put("vpcid", request.vpcid);
//        }
//
//        if (!Common.isUnset(request.vswitchIds)) {
//            body.put("vswitch_ids", request.vswitchIds);
//        }
//
//        if (!Common.isUnset(request.workerAutoRenew)) {
//            body.put("worker_auto_renew", request.workerAutoRenew);
//        }
//
//        if (!Common.isUnset(request.workerAutoRenewPeriod)) {
//            body.put("worker_auto_renew_period", request.workerAutoRenewPeriod);
//        }
//
//        if (!Common.isUnset(request.workerDataDisks)) {
//            body.put("worker_data_disks", request.workerDataDisks);
//        }
//
//        if (!Common.isUnset(request.workerInstanceChargeType)) {
//            body.put("worker_instance_charge_type", request.workerInstanceChargeType);
//        }
//
//        if (!Common.isUnset(request.workerInstanceTypes)) {
//            body.put("worker_instance_types", request.workerInstanceTypes);
//        }
//
//        if (!Common.isUnset(request.workerPeriod)) {
//            body.put("worker_period", request.workerPeriod);
//        }
//
//        if (!Common.isUnset(request.workerPeriodUnit)) {
//            body.put("worker_period_unit", request.workerPeriodUnit);
//        }
//
//        if (!Common.isUnset(request.workerSystemDiskCategory)) {
//            body.put("worker_system_disk_category", request.workerSystemDiskCategory);
//        }
//
//        if (!Common.isUnset(request.workerSystemDiskPerformanceLevel)) {
//            body.put("worker_system_disk_performance_level", request.workerSystemDiskPerformanceLevel);
//        }
//
//        if (!Common.isUnset(request.workerSystemDiskSize)) {
//            body.put("worker_system_disk_size", request.workerSystemDiskSize);
//        }
//
//        if (!Common.isUnset(request.workerSystemDiskSnapshotPolicyId)) {
//            body.put("worker_system_disk_snapshot_policy_id", request.workerSystemDiskSnapshotPolicyId);
//        }
//
//        if (!Common.isUnset(request.workerVswitchIds)) {
//            body.put("worker_vswitch_ids", request.workerVswitchIds);
//        }
//
//        if (!Common.isUnset(request.zoneId)) {
//            body.put("zone_id", request.zoneId);
//        }
//
//        JSONObject reqData = ActionFactory.requestData.get();
//        if (reqData.containsKey("departmentId")) {
//            headers.put("x-acs-organizationid", reqData.getString("departmentId"));
//        }
//        if (reqData.containsKey("resourceGroupId")) {
//            headers.put("x-acs-resourcegroupid", reqData.getString("resourceGroupId"));
//        }
//
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("headers", headers), new TeaPair("body", com.aliyun.openapiutil.Client.parseToMap(body))}));
//        Params params = Params.build(TeaConverter.buildMap(new TeaPair[]{new TeaPair("action", "CreateCluster"), new TeaPair("version", "2015-12-15"), new TeaPair("protocol", "HTTPS"), new TeaPair("pathname", "/clusters"), new TeaPair("method", "POST"), new TeaPair("authType", "AK"), new TeaPair("style", "ROA"), new TeaPair("reqBodyType", "json"), new TeaPair("bodyType", "json")}));
//        return (CreateClusterResponse) TeaModel.toModel(this.callApi(params, req, runtime), new CreateClusterResponse());
//    }
//
//    private final static InterceptorChain interceptorChain = InterceptorChain.create();
//
//    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        TeaModel.validateParams(request, "request");
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getQuery();
//        if (CollUtil.isEmpty(body)) {
//            body = new HashMap();
//            request.setQuery(body);
//        }
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_2".equals(auth.getVersion())) {
//            request.query.put("RegionId", super._regionId);
//        }
//        Map<String, Object> runtime_ = TeaConverter.buildMap(
//                new TeaPair("timeouted", "retry"),
//                new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, _readTimeout)),
//                new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
//                new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, _httpProxy)),
//                new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, _httpsProxy)),
//                new TeaPair("noProxy", Common.defaultString(runtime.noProxy, _noProxy)),
//                new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
//                new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
//                new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
//                new TeaPair("retry", TeaConverter.buildMap(
//                        new TeaPair("retryable", runtime.autoretry),
//                        new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))
//                )),
//                new TeaPair("backoff", TeaConverter.buildMap(
//                        new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")),
//                        new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))
//                )),
//                new TeaPair("ignoreSSL", runtime.ignoreSSL)
//        );
//
//        TeaRequest _lastRequest = null;
//        Exception _lastException = null;
//        TeaResponse _lastResponse = null;
//        long _now = System.currentTimeMillis();
//        int _retryTimes = 0;
//        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
//            if (_retryTimes > 0) {
//                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
//                if (backoffTime > 0) {
//                    Tea.sleep(backoffTime);
//                }
//            }
//            _retryTimes = _retryTimes + 1;
//            try {
//                TeaRequest request_ = new TeaRequest();
//                request_.protocol = Common.defaultString(_protocol, protocol);
//                request_.method = method;
//                request_.pathname = "/";
//                request_.query = TeaConverter.merge(String.class,
//                        TeaConverter.buildMap(
//                                new TeaPair("Action", action),
//                                new TeaPair("Format", "json"),
//                                new TeaPair("Version", version),
//                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
//                                new TeaPair("SignatureNonce", Common.getNonce())
//                        ),
//                        request.query
//                );
//                Map<String, String> headers = this.getRpcHeaders();
//                if (Common.isUnset(headers)) {
//                    // endpoint is setted in product client
//                    request_.headers = TeaConverter.buildMap(
//                            new TeaPair("host", _endpoint),
//                            new TeaPair("x-acs-version", version),
//                            new TeaPair("x-acs-action", action),
//                            new TeaPair("user-agent", this.getUserAgent())
//                    );
//                } else {
//                    request_.headers = TeaConverter.merge(String.class,
//                            TeaConverter.buildMap(
//                                    new TeaPair("host", _endpoint),
//                                    new TeaPair("x-acs-version", version),
//                                    new TeaPair("x-acs-action", action),
//                                    new TeaPair("user-agent", this.getUserAgent())
//                            ),
//                            headers
//                    );
//                }
//
//                if (!Common.isUnset(request.body)) {
//                    Map<String, Object> m = Common.assertAsMap(request.body);
//                    Map<String, Object> tmp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
//                    request_.body = Tea.toReadable(Common.toFormString(tmp));
//                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
//                }
//
//                if (!Common.equalString(authType, "Anonymous")) {
//                    String accessKeyId = this.getAccessKeyId();
//                    String accessKeySecret = this.getAccessKeySecret();
//                    String securityToken = this.getSecurityToken();
//                    if (!Common.empty(securityToken)) {
//                        request_.query.put("SecurityToken", securityToken);
//                    }
//
//                    request_.query.put("SignatureMethod", "HMAC-SHA1");
//                    request_.query.put("SignatureVersion", "1.0");
//                    request_.query.put("AccessKeyId", accessKeyId);
//                    Map<String, Object> t = null;
//                    if (!Common.isUnset(request.body)) {
//                        t = Common.assertAsMap(request.body);
//                    }
//
//                    Map<String, String> signedParam = TeaConverter.merge(String.class,
//                            request_.query,
//                            com.aliyun.openapiutil.Client.query(t)
//                    );
//                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
//                }
//
//                _lastRequest = request_;
//                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
//                _lastResponse = response_;
//
//                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
//                    Object _res = Common.readAsJSON(response_.body);
//                    Map<String, Object> err = Common.assertAsMap(_res);
//                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
//                    throw new TeaException(TeaConverter.buildMap(
//                            new TeaPair("code", "" + Client.defaultAny(err.get("Code"), err.get("code")) + ""),
//                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
//                            new TeaPair("data", err)
//                    ));
//                }
//
//                if (Common.equalString(bodyType, "binary")) {
//                    Map<String, Object> resp = TeaConverter.buildMap(
//                            new TeaPair("body", response_.body),
//                            new TeaPair("headers", response_.headers)
//                    );
//                    return resp;
//                } else if (Common.equalString(bodyType, "byte")) {
//                    byte[] byt = Common.readAsBytes(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", byt),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "string")) {
//                    String str = Common.readAsString(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", str),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "json")) {
//                    Object obj = Common.readAsJSON(response_.body);
//                    Map<String, Object> res = Common.assertAsMap(obj);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", res),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else if (Common.equalString(bodyType, "array")) {
//                    Object arr = Common.readAsJSON(response_.body);
//                    return TeaConverter.buildMap(
//                            new TeaPair("body", arr),
//                            new TeaPair("headers", response_.headers)
//                    );
//                } else {
//                    return TeaConverter.buildMap(
//                            new TeaPair("headers", response_.headers)
//                    );
//                }
//
//            } catch (Exception e) {
//                if (Tea.isRetryable(e)) {
//                    _lastException = e;
//                    continue;
//                }
//                throw e;
//            } finally {
//                if (!Common.isUnset(_lastResponse)
//                        && !Common.isUnset(_lastResponse.response)
//                        && !Common.isUnset(_lastResponse.response.body())) {
//                    _lastResponse.response.close();
//                }
//            }
//        }
//        throw new TeaUnretryableException(_lastRequest, _lastException);
//    }
//
//    public Map<String, ?> callApi(Params params, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getQuery();
//        if (CollUtil.isEmpty(body)) {
//            body = new HashMap();
//            request.setQuery(body);
//        }
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//
//        if (Common.isUnset(TeaModel.buildMap(params))) {
//            throw new TeaException(TeaConverter.buildMap(
//                    new TeaPair("code", "ParameterMissing"),
//                    new TeaPair("message", "'params' can not be unset")
//            ));
//        }
//
//        if (Common.isUnset(_signatureAlgorithm) || !Common.equalString(_signatureAlgorithm, "v2")) {
//            return this.doRequest(params, request, runtime);
//        } else if (Common.equalString(params.style, "ROA") && Common.equalString(params.reqBodyType, "json")) {
//            return this.doROARequest(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else if (Common.equalString(params.style, "ROA")) {
//            return this.doROARequestWithForm(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else {
//            return this.doRPCRequest(params.action, params.version, params.protocol, params.method, params.authType, params.bodyType, request, runtime);
//        }
//
//    }
//
//
//}
