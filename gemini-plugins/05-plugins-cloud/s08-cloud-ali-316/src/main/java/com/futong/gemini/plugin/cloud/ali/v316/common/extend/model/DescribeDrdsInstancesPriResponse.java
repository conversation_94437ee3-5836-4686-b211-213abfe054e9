package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.drds20190123.models.DescribeDrdsInstancesResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class DescribeDrdsInstancesPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeDBInstancesBody body;

    @Data
    public static class DescribeDBInstancesBody extends TeaModel {
        @NameInMap("Instances")
        public DBInstance instances;

        @NameInMap("redirect")
        public String redirect;

        @NameInMap("eagleEyeTraceId")
        public String eagleEyeTraceId;

        @NameInMap("asapiSuccess")
        public Integer asapiSuccess;

        @NameInMap("code")
        public String code;

        @NameInMap("cost")
        public String cost;

        @NameInMap("success")
        public Integer success;

        @NameInMap("pureListData")
        public String pureListData;

        @NameInMap("message")
        public String message;

        @NameInMap("PageSize")
        public String pageSize;

        @NameInMap("PageNumber")
        public String pageNumber;

        @NameInMap("TotalCount")
        public String totalCount;

        @NameInMap("innerTotalCount")
        public String innerTotalCount;
    }

    @Data
    public static class DBInstance extends TeaModel {
        @NameInMap("Instance")
        public List<DescribeDBInstancesResponseBodyDBInstance> DBInstance;
    }

    @Data
    public static class DescribeDBInstancesResponseBodyDBInstance extends DescribeDrdsInstancesResponseBody.DescribeDrdsInstancesResponseBodyInstancesInstance {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}