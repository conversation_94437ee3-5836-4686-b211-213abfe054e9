package com.futong.gemini.plugin.cloud.ali.v316.common.extend;

import com.aliyun.actiontrail20200706.models.LookupEventsRequest;
import com.aliyun.actiontrail20200706.models.LookupEventsResponse;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
public class ActiontrailClient extends com.aliyun.actiontrail20200706.Client {

    /**
     * 产品类型
     */
    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "actiontrail")));

    public ActiontrailClient(Config config) throws Exception {
        super(config);
    }

    @Override
    public LookupEventsResponse lookupEventsWithOptions(LookupEventsRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("LookupEvents", "2020-07-06", "HTTPS", "POST", "AK", "json", req, runtime), new LookupEventsResponse());
    }
}
