package com.futong.gemini.plugin.cloud.ali.v316.common.extend;

import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.ListKeysRequest;
import com.aliyun.kms20160120.models.ListKeysResponse;
import com.aliyun.tea.TeaConverter;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.TeaPair;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
/**
 * @program: futong-cm
 * @description:
 * @author: lipengpeng
 * @create: 2022/5/12 18:17
 */
public class KmsClient extends Client {
    /**
     * 产品类型
     */
    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "Kms")));
    public KmsClient(Config config) throws Exception {
        super(config);
    }
    @Override
    public ListKeysResponse listKeysWithOptions(ListKeysRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(
                TeaConverter.buildMap(new TeaPair[]{new TeaPair("body", Common.toMap(request)),product}));
        return TeaModel.toModel(this.doRPCRequest("ListKeys", "2016-01-20", "HTTPS", "POST", "AK", "json", req, runtime), new ListKeysResponse());
    }
}
