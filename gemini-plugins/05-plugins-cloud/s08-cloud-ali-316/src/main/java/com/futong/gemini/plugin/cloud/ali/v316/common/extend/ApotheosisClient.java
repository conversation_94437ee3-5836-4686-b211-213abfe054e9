package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.*;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import com.futong.gemini.plugin.cloud.ali.v316.common.extend.model.GetPhysicalDeviceDataResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.http.entity.StringEntity;

import java.nio.charset.Charset;
import java.util.Map;
/***
 * 阿里云运维侧-封神组件
 */
@Log4j2
public class ApotheosisClient extends Client {

    public ApotheosisClient(Config config) throws Exception {
        super(config);
    }

    public GetPhysicalDeviceDataResponse searchPhysicalDeviceData(Map<String,Object> request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        request.put("product","NC");
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", request)
        ));
        Map<String, ?> map = this.doRPCRequest( "HTTP", "POST", "/api/v1/search/data_new", "json", req, runtime);
        return TeaModel.toModel(map,new GetPhysicalDeviceDataResponse());
    }

    public Map<String, ?> doRPCRequest(String protocol, String method, String pathname, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        TeaModel.validateParams(request, "request");
        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", com.aliyun.teautil.Common.defaultNumber(60 * 1000, _readTimeout)),
                new TeaPair("connectTimeout", com.aliyun.teautil.Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", com.aliyun.teautil.Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", com.aliyun.teautil.Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", com.aliyun.teautil.Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", com.aliyun.teautil.Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", com.aliyun.teautil.Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", com.aliyun.teautil.Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", com.aliyun.teautil.Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", com.aliyun.teautil.Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", com.aliyun.teautil.Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = com.aliyun.teautil.Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = pathname;
                Map<String, String> headers = this.getRpcHeaders();
                if (com.aliyun.teautil.Common.isUnset(headers)) {
                    request_.headers = TeaConverter.buildMap(
                            new TeaPair("host", _endpoint)
                    );
                } else {
                    request_.headers = TeaConverter.merge(String.class,
                            TeaConverter.buildMap(
                                    new TeaPair("host", _endpoint)
                            ),
                            headers
                    );
                }

                if (!com.aliyun.teautil.Common.isUnset(request.body)) {
                    Map<String, Object> m = com.aliyun.teautil.Common.assertAsMap(request.body);
                    Map<String, Object> tmp = com.aliyun.teautil.Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
                    request_.body = Tea.toReadable(com.aliyun.teautil.Common.toFormString(tmp));
                }

                //转换 body 格式计算签名使用
                String body = "";
                if (ObjectUtil.isNotEmpty(request.getBody())) {
                    body = JSONObject.toJSONString(request.getBody());
                    if ("{}".equals(body)) {
                        body = "";
                    }
                }

                //转换 query 参数格式计算签名使用 并用于拼接域名
                StringBuilder queryStr = new StringBuilder();
                if (CollUtil.isNotEmpty(request.getQuery())) {
                    for (Map.Entry<String, String> entry : request.getQuery().entrySet()) {
                        queryStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                    }
                    queryStr = new StringBuilder(queryStr.substring(0, queryStr.length() - 1));
                }

                request_.headers.put("Content-Type", "application/json;charset=UTF-8");

                //对 body 转换
                StringEntity data = new StringEntity(body, Charset.forName("UTF-8"));
                request_.body = data.getContent();

                if (!"".equals(queryStr.toString())) {
                    request_.pathname = pathname + "?" + queryStr;
                }

                //发起请求
                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, null);
                _lastResponse = response_;

                //解析返回结果
                if (com.aliyun.teautil.Common.is4xx(response_.statusCode) || com.aliyun.teautil.Common.is5xx(response_.statusCode)) {
                    Object _res = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> err = com.aliyun.teautil.Common.assertAsMap(_res);
                    Object requestId = Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + Client.defaultAny(err.get("statusCode"), err.get("statusCode")) + ""),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + Client.defaultAny(err.get("description"), err.get("description")) + " request id: " + requestId + ""),
                            new TeaPair("description", Client.defaultAny(err.get("description"), err.get("description"))),
                            new TeaPair("data", err)
                    ));
                }
                //判断返回类型，转换对应的数据格式
                if (com.aliyun.teautil.Common.equalString(bodyType, "string")) {
                    String str = com.aliyun.teautil.Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "json")) {
                    Object obj = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    Map<String, Object> res = com.aliyun.teautil.Common.assertAsMap(obj);
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (com.aliyun.teautil.Common.equalString(bodyType, "array")) {
                    Object arr = com.aliyun.teautil.Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!com.aliyun.teautil.Common.isUnset(_lastResponse)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response)
                        && !com.aliyun.teautil.Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }
}
