package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import lombok.Data;
@Data
public class DescribeLoadBalancersResponseBodyExt extends TeaModel {
    @NameInMap("LoadBalancers")
    public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancers loadBalancers;

    @NameInMap("PageNumber")
    public Integer pageNumber;

    @NameInMap("PageSize")
    public Integer pageSize;

    @NameInMap("RequestId")
    public String requestId;

    @NameInMap("TotalCount")
    public Integer totalCount;

    public static DescribeLoadBalancersResponseBodyExt build(java.util.Map<String, ?> map) {
        DescribeLoadBalancersResponseBodyExt self = new DescribeLoadBalancersResponseBodyExt();
        return TeaModel.build(map, self);
    }

    public DescribeLoadBalancersResponseBodyExt setLoadBalancers(DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancers loadBalancers) {
        this.loadBalancers = loadBalancers;
        return this;
    }

    public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancers getLoadBalancers() {
        return this.loadBalancers;
    }

    public DescribeLoadBalancersResponseBodyExt setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
        return this;
    }

    public Integer getPageNumber() {
        return this.pageNumber;
    }

    public DescribeLoadBalancersResponseBodyExt setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public DescribeLoadBalancersResponseBodyExt setRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public String getRequestId() {
        return this.requestId;
    }

    public DescribeLoadBalancersResponseBodyExt setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public Integer getTotalCount() {
        return this.totalCount;
    }

    public static class DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer extends TeaModel {
        @NameInMap("Address")
        public String address;

        @NameInMap("AddressIPVersion")
        public String addressIPVersion;

        @NameInMap("AddressType")
        public String addressType;

        @NameInMap("Bandwidth")
        public Integer bandwidth;

        @NameInMap("CreateTime")
        public String createTime;

        @NameInMap("CreateTimeStamp")
        public Long createTimeStamp;

        @NameInMap("DeleteProtection")
        public String deleteProtection;

        @NameInMap("InstanceChargeType")
        public String instanceChargeType;

        @NameInMap("InternetChargeType")
        public String internetChargeType;

        @NameInMap("InternetChargeTypeAlias")
        public String internetChargeTypeAlias;

        @NameInMap("LoadBalancerId")
        public String loadBalancerId;

        @NameInMap("LoadBalancerName")
        public String loadBalancerName;

        @NameInMap("LoadBalancerSpec")
        public String loadBalancerSpec;

        @NameInMap("LoadBalancerStatus")
        public String loadBalancerStatus;

        @NameInMap("MasterZoneId")
        public String masterZoneId;

        @NameInMap("ModificationProtectionReason")
        public String modificationProtectionReason;

        @NameInMap("ModificationProtectionStatus")
        public String modificationProtectionStatus;

        @NameInMap("NetworkType")
        public String networkType;

        @NameInMap("PayType")
        public String payType;

        @NameInMap("RegionId")
        public String regionId;

        @NameInMap("RegionIdAlias")
        public String regionIdAlias;

        @NameInMap("ResourceGroupId")
        public String resourceGroupId;

        @NameInMap("SlaveZoneId")
        public String slaveZoneId;

        @NameInMap("VSwitchId")
        public String vSwitchId;

        @NameInMap("VpcId")
        public String vpcId;

        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;

        public static DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer build(java.util.Map<String, ?> map) {
            DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer self = new DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer();
            return TeaModel.build(map, self);
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setAddress(String address) {
            this.address = address;
            return this;
        }

        public String getAddress() {
            return this.address;
        }

        public String getvSwitchId() {
            return vSwitchId;
        }

        public void setvSwitchId(String vSwitchId) {
            this.vSwitchId = vSwitchId;
        }

        public String getResourceGroup() {
            return resourceGroup;
        }

        public void setResourceGroup(String resourceGroup) {
            this.resourceGroup = resourceGroup;
        }

        public String getDepartment() {
            return department;
        }

        public void setDepartment(String department) {
            this.department = department;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setAddressIPVersion(String addressIPVersion) {
            this.addressIPVersion = addressIPVersion;
            return this;
        }

        public String getAddressIPVersion() {
            return this.addressIPVersion;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setAddressType(String addressType) {
            this.addressType = addressType;
            return this;
        }

        public String getAddressType() {
            return this.addressType;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setBandwidth(Integer bandwidth) {
            this.bandwidth = bandwidth;
            return this;
        }

        public Integer getBandwidth() {
            return this.bandwidth;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setCreateTime(String createTime) {
            this.createTime = createTime;
            return this;
        }

        public String getCreateTime() {
            return this.createTime;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setCreateTimeStamp(Long createTimeStamp) {
            this.createTimeStamp = createTimeStamp;
            return this;
        }

        public Long getCreateTimeStamp() {
            return this.createTimeStamp;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setDeleteProtection(String deleteProtection) {
            this.deleteProtection = deleteProtection;
            return this;
        }

        public String getDeleteProtection() {
            return this.deleteProtection;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setInstanceChargeType(String instanceChargeType) {
            this.instanceChargeType = instanceChargeType;
            return this;
        }

        public String getInstanceChargeType() {
            return this.instanceChargeType;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setInternetChargeType(String internetChargeType) {
            this.internetChargeType = internetChargeType;
            return this;
        }

        public String getInternetChargeType() {
            return this.internetChargeType;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setInternetChargeTypeAlias(String internetChargeTypeAlias) {
            this.internetChargeTypeAlias = internetChargeTypeAlias;
            return this;
        }

        public String getInternetChargeTypeAlias() {
            return this.internetChargeTypeAlias;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setLoadBalancerId(String loadBalancerId) {
            this.loadBalancerId = loadBalancerId;
            return this;
        }

        public String getLoadBalancerId() {
            return this.loadBalancerId;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setLoadBalancerName(String loadBalancerName) {
            this.loadBalancerName = loadBalancerName;
            return this;
        }

        public String getLoadBalancerName() {
            return this.loadBalancerName;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setLoadBalancerSpec(String loadBalancerSpec) {
            this.loadBalancerSpec = loadBalancerSpec;
            return this;
        }

        public String getLoadBalancerSpec() {
            return this.loadBalancerSpec;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setLoadBalancerStatus(String loadBalancerStatus) {
            this.loadBalancerStatus = loadBalancerStatus;
            return this;
        }

        public String getLoadBalancerStatus() {
            return this.loadBalancerStatus;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setMasterZoneId(String masterZoneId) {
            this.masterZoneId = masterZoneId;
            return this;
        }

        public String getMasterZoneId() {
            return this.masterZoneId;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setModificationProtectionReason(String modificationProtectionReason) {
            this.modificationProtectionReason = modificationProtectionReason;
            return this;
        }

        public String getModificationProtectionReason() {
            return this.modificationProtectionReason;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setModificationProtectionStatus(String modificationProtectionStatus) {
            this.modificationProtectionStatus = modificationProtectionStatus;
            return this;
        }

        public String getModificationProtectionStatus() {
            return this.modificationProtectionStatus;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setNetworkType(String networkType) {
            this.networkType = networkType;
            return this;
        }

        public String getNetworkType() {
            return this.networkType;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setPayType(String payType) {
            this.payType = payType;
            return this;
        }

        public String getPayType() {
            return this.payType;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setRegionId(String regionId) {
            this.regionId = regionId;
            return this;
        }

        public String getRegionId() {
            return this.regionId;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setRegionIdAlias(String regionIdAlias) {
            this.regionIdAlias = regionIdAlias;
            return this;
        }

        public String getRegionIdAlias() {
            return this.regionIdAlias;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setResourceGroupId(String resourceGroupId) {
            this.resourceGroupId = resourceGroupId;
            return this;
        }

        public String getResourceGroupId() {
            return this.resourceGroupId;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setSlaveZoneId(String slaveZoneId) {
            this.slaveZoneId = slaveZoneId;
            return this;
        }

        public String getSlaveZoneId() {
            return this.slaveZoneId;
        }


        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setVSwitchId(String vSwitchId) {
            this.vSwitchId = vSwitchId;
            return this;
        }

        public String getVSwitchId() {
            return this.vSwitchId;
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer setVpcId(String vpcId) {
            this.vpcId = vpcId;
            return this;
        }

        public String getVpcId() {
            return this.vpcId;
        }

    }

    public static class DescribeLoadBalancersResponseBodyLoadBalancers extends TeaModel {
        @NameInMap("LoadBalancer")
        public java.util.List<DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer> loadBalancer;

        public static DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancers build(java.util.Map<String, ?> map) {
            DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancers self = new DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancers();
            return TeaModel.build(map, self);
        }

        public DescribeLoadBalancersResponseBodyExt.DescribeLoadBalancersResponseBodyLoadBalancers setLoadBalancer(java.util.List<DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer> loadBalancer) {
            this.loadBalancer = loadBalancer;
            return this;
        }

        public java.util.List<DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer> getLoadBalancer() {
            return this.loadBalancer;
        }

    }

}
