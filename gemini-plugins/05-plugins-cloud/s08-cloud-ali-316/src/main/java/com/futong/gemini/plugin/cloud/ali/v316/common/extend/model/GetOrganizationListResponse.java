package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;

import java.util.Map;
public class GetOrganizationListResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public GetOrganizationListResponseBody body;
    public static GetOrganizationListResponse build(Map<String, ?> map) {
        GetOrganizationListResponse self = new GetOrganizationListResponse();
        return TeaModel.build(map, self);
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public GetOrganizationListResponse setHeaders(Map<String, String> headers) {
        this.headers = headers;
        return this;
    }

    public GetOrganizationListResponseBody getBody() {
        return body;
    }

    public GetOrganizationListResponse setBody(GetOrganizationListResponseBody body) {
        this.body = body;
        return this;
    }
}
