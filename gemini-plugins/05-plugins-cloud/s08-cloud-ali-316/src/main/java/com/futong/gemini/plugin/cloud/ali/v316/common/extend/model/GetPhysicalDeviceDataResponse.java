package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class GetPhysicalDeviceDataResponse extends TeaModel {

    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("body")
    @Validation(required = true)
    public GetPhysicalDeviceDataBody body;


    @Data
    public static class GetPhysicalDeviceDataBody extends TeaModel {
        @NameInMap("code")
        public String code;

        @NameInMap("data")
        public List<GetPhysicalDeviceData> data;
    }

    @Data
    public static class GetPhysicalDeviceData extends TeaModel {

        @NameInMap("nc_project")
        public String ncProject;

        @NameInMap("nc_cpu")
        public String ncCpu;

        @NameInMap("nc_load")
        public String ncLoad;

        @NameInMap("nc_cluster")
        public String ncCluster;

        @NameInMap("nc_id")
        public String ncId;

        @NameInMap("nc_tcp")
        public String ncTcp;

        @NameInMap("nc_mem")
        public String ncMem;

        @NameInMap("nc_ip")
        public String ncIp;

        @NameInMap("ip")
        public String ip;

        @NameInMap("rate")
        public Integer rate;

        @NameInMap("disk")
        public String disk;

        public Integer diskCount = 1;

        @NameInMap("monitor_time")
        public String monitorTime;
    }

}
