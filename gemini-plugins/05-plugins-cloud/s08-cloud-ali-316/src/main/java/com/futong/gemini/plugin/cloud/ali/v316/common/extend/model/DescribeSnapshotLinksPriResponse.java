package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.ecs20140526.models.DescribeSnapshotLinksResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class DescribeSnapshotLinksPriResponse extends TeaModel {
    @NameInMap("headers")
    public Map<String, String> headers;

    @NameInMap("statusCode")
    public Integer statusCode;

    @NameInMap("body")
    public DescribeSnapshotLinksBody body;

    @Data
    public static class DescribeSnapshotLinksBody extends TeaModel {
        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("PageSize")
        public Integer pageSize;

        @NameInMap("TotalCount")
        public Integer totalCount;

        @NameInMap("SnapshotLinks")
        public DescribeSnapshotLinksDataResponseBody snapshotLinks;
    }

    @Data
    public static class DescribeSnapshotLinksDataResponseBody extends TeaModel {
        @NameInMap("SnapshotLink")
        public List<SnapshotLink> snapshotLink;
    }

    @Data
    public static class SnapshotLink extends DescribeSnapshotLinksResponseBody.DescribeSnapshotLinksResponseBodySnapshotLinksSnapshotLink {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}
