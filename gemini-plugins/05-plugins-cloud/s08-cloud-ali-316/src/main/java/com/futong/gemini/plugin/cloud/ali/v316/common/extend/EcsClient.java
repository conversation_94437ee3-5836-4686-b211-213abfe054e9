package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
import cn.hutool.core.collection.CollUtil;
import com.aliyun.ecs20140526.Client;
import com.aliyun.ecs20140526.models.*;
import com.aliyun.tea.*;
import com.aliyun.tea.interceptor.InterceptorChain;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.futong.gemini.plugin.cloud.ali.v316.common.extend.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
@Slf4j
public class EcsClient extends Client {

    /**
     * 产品类型
     */
    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "Ecs")));

    private final static InterceptorChain interceptorChain = InterceptorChain.create();

    public EcsClient(Config config) throws Exception {
        super(config);
    }

    /**
     * 查询规格
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeInstanceTypesResponse describeInstanceTypesWithOptions(DescribeInstanceTypesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        String method = "POST";
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_2".equals(auth.getVersion())) {
//            method = "GET";
//        }
        return TeaModel.toModel(this.doRPCRequest("DescribeInstanceTypes", "2014-05-26", "HTTPS", method, "AK", "json", req, runtime), new DescribeInstanceTypesResponse());
    }

    /**
     * 查询区域
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeRegionsResponse describeRegionsWithOptions(DescribeRegionsRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        String method = "POST";
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_2".equals(auth.getVersion())) {
//            method = "GET";
//        }
        return TeaModel.toModel(this.doRPCRequest("DescribeRegions", "2014-05-26", "HTTPS", method, "AK", "json", req, runtime), new DescribeRegionsResponse());
    }

    /**
     * 查询可用区
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeZonesResponse describeZonesWithOptions(DescribeZonesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeZones", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeZonesResponse());
    }

    /**
     * 查询镜像
     *
     * @param request
     * @return
     * @throws Exception
     */
    public DescribeImagesPriResponse describeImage(DescribeImagesRequest request) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeImages", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeImagesPriResponse());
    }

    /**
     * 创建镜像
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateImageResponse createImageWithOptions(CreateImageRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateImage", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateImageResponse());
    }

    /**
     * 修改镜像
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyImageAttributeResponse modifyImageAttributeWithOptions(ModifyImageAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyImageAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyImageAttributeResponse());
    }

    /**
     * 删除镜像
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteImageResponse deleteImageWithOptions(DeleteImageRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteImage", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteImageResponse());
    }

    /**
     * 复制镜像
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CopyImageResponse copyImageWithOptions(CopyImageRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CopyImage", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CopyImageResponse());
    }

    /**
     * 导出镜像
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ExportImageResponse exportImageWithOptions(ExportImageRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ExportImage", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ExportImageResponse());
    }

    /**
     * 共享镜像
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyImageSharePermissionResponse modifyImageSharePermissionWithOptions(ModifyImageSharePermissionRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyImageSharePermission", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyImageSharePermissionResponse());
    }

    /**
     * 查询实例
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public DescribeInstancesPriResponse describeInstance(DescribeInstancesRequest request) throws Exception {
        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
        RuntimeOptions runtime = new RuntimeOptions();
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeInstances", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstancesPriResponse());
    }

//    /**
//     * 重启实例
//     *
//     * @param request
//     * @param runtime
//     * @return
//     * @throws Exception
//     */
//    @Override
//    public RebootInstanceResponse rebootInstanceWithOptions(RebootInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("RebootInstance", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new RebootInstanceResponse());
//    }

    /**
     * 重启实例
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public RebootInstancesResponse rebootInstancesPri(RebootInstancesExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("RebootInstances", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new RebootInstancesResponse());
    }

    /**
     * 关闭实例
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public StopInstancesResponse stopInstancesPri(StopInstancesExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("StopInstances", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new StopInstancesResponse());
    }

//    /**
//     * 启动实例
//     *
//     * @param request
//     * @param runtime
//     * @return
//     * @throws Exception
//     */
//    @Override
//    public StartInstanceResponse startInstanceWithOptions(StartInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("StartInstance", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new StartInstanceResponse());
//    }

    /**
     * 启动实例
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public StartInstancesResponse startInstancesPri(StartInstancesExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("StartInstances", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new StartInstancesResponse());
    }

    /**
     * 修改实例
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyInstanceAttributeResponse modifyInstanceAttributeWithOptions(ModifyInstanceAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceAttributeResponse());
    }

//    /**
//     * 删除实例
//     *
//     * @param request
//     * @param runtime
//     * @return
//     * @throws Exception
//     */
//    @Override
//    public DeleteInstanceResponse deleteInstanceWithOptions(DeleteInstanceRequest request, RuntimeOptions runtime) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        return TeaModel.toModel(this.doRPCRequest("DeleteInstance", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteInstanceResponse());
//    }

    /**
     * 删除实例
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */

    public DeleteInstancesResponse deleteInstancesPri(DeleteInstancesExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteInstances", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteInstancesResponse());
    }

    /**
     * 创建实例
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */

    public RunInstancesResponse runInstancesPri(RunInstancesExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("RunInstances", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new RunInstancesResponse());
    }

    /**
     * VNC 远程调用
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeInstanceVncUrlResponse describeInstanceVncUrlWithOptions(DescribeInstanceVncUrlRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeInstanceVncUrl", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeInstanceVncUrlResponse());
    }

    /**
     * 修改 VNC 密码
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyInstanceVncPasswdResponse modifyInstanceVncPasswdWithOptions(ModifyInstanceVncPasswdRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceVncPasswd", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceVncPasswdResponse());
    }

    /**
     * 查询比密钥对
     *
     * @param request
     * @return
     * @throws Exception
     */
    public DescribeKeyPairsPriResponse describeKeyPair(DescribeKeyPairsExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        Map<String, ?> map = this.doRPCRequest("DescribeKeyPairs", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime);
        return TeaModel.toModel(map, new DescribeKeyPairsPriResponse());

    }

    /**
     * 删除密钥对
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteKeyPairsResponse deleteKeyPairsWithOptions(DeleteKeyPairsRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteKeyPairs", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteKeyPairsResponse());
    }

    /**
     * 绑定密钥对
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public AttachKeyPairResponse attachKeyPairWithOptions(AttachKeyPairRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("AttachKeyPair", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new AttachKeyPairResponse());
    }

    /**
     * 解绑密钥对
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DetachKeyPairResponse detachKeyPairWithOptions(DetachKeyPairRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DetachKeyPair", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DetachKeyPairResponse());
    }

    /**
     * 创建密钥对
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateKeyPairResponse createKeyPairWithOptions(CreateKeyPairRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateKeyPair", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateKeyPairResponse());
    }

    /**
     * 查询安全组
     *
     * @param request
     * @return
     * @throws Exception
     */
    public DescribeSecurityGroupsPriResponse describeSecurityGroup(DescribeSecurityGroupsRequest request) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityGroups", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityGroupsPriResponse());
    }

    /**
     * 查询安全组
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public DescribeSecurityGroupsResponse describeSecurityGroupsWithOptions(DescribeSecurityGroupsRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityGroups", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityGroupsResponse());
    }

    /**
     * 查询安全组规则
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeSecurityGroupAttributeResponse describeSecurityGroupAttributeWithOptions(DescribeSecurityGroupAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityGroupAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityGroupAttributeResponse());
    }
    /**
     * 查询安全组规则
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public DescribeSecurityGroupAttributeResponse describeSecurityGroupAttribute(DescribeSecurityGroupAttributeExtRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSecurityGroupAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSecurityGroupAttributeResponse());
    }

    /**
     * 创建安全组
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateSecurityGroupResponse createSecurityGroupWithOptions(CreateSecurityGroupRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateSecurityGroup", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateSecurityGroupResponse());
    }

    /**
     * 删除安全组
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteSecurityGroupResponse deleteSecurityGroupWithOptions(DeleteSecurityGroupRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteSecurityGroup", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteSecurityGroupResponse());
    }

    /**
     * 修改安全组
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifySecurityGroupAttributeResponse modifySecurityGroupAttributeWithOptions(ModifySecurityGroupAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifySecurityGroupAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySecurityGroupAttributeResponse());
    }

    /**
     * 绑定安全组
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public JoinSecurityGroupResponse joinSecurityGroupWithOptions(JoinSecurityGroupRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("JoinSecurityGroup", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new JoinSecurityGroupResponse());
    }

    /**
     * 解绑安全组
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public LeaveSecurityGroupResponse leaveSecurityGroupWithOptions(LeaveSecurityGroupRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("LeaveSecurityGroup", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new LeaveSecurityGroupResponse());
    }

    /**
     * 创建安全组 入方向的规则
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public AuthorizeSecurityGroupResponse authorizeSecurityGroupWithOptions(AuthorizeSecurityGroupRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("AuthorizeSecurityGroup", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new AuthorizeSecurityGroupResponse());
    }

    /**
     * 创建安全组 出方向的规则
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public AuthorizeSecurityGroupEgressResponse authorizeSecurityGroupEgressWithOptions(AuthorizeSecurityGroupEgressRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("AuthorizeSecurityGroupEgress", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new AuthorizeSecurityGroupEgressResponse());
    }

    /**
     * 删除安全组 入方向的规则
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public RevokeSecurityGroupResponse revokeSecurityGroupWithOptions(RevokeSecurityGroupRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("RevokeSecurityGroup", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new RevokeSecurityGroupResponse());
    }

    /**
     * 删除安全组 出方向的规则
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public RevokeSecurityGroupEgressResponse revokeSecurityGroupEgressWithOptions(RevokeSecurityGroupEgressRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("RevokeSecurityGroupEgress", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new RevokeSecurityGroupEgressResponse());
    }

    /**
     * 查询 VPC
     *
     * @param request
     * @return
     * @throws Exception
     */
    public DescribeVpcsPriResponse describeVpcList(DescribeVpcsRequest request) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
//        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", request),
//                product
//        ));
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));

        return TeaModel.toModel(this.doRPCRequest("DescribeVpcs", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeVpcsPriResponse());
    }

    /**
     * 查询 VPC
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public DescribeVpcsResponse describeVpcsWithOptions(DescribeVpcsRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeVpcs", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeVpcsResponse());
    }

    /**
     * 创建 VPC
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateVpcResponse createVpcWithOptions(CreateVpcRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateVpc", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateVpcResponse());
    }

    /**
     * 删除 VPC
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteVpcResponse deleteVpcWithOptions(DeleteVpcRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteVpc", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteVpcResponse());
    }

    /**
     * 修改 VPC
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyVpcAttributeResponse modifyVpcAttributeWithOptions(ModifyVpcAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyVpcAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyVpcAttributeResponse());
    }

    /**
     * 查询子网
     *
     * @param request
     * @return
     * @throws Exception
     */
    public DescribeVSwitchesPriResponse describeVSwitcheList(DescribeVSwitchesRequest request) throws Exception {
//        Common.validateModel(request);
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", Common.toMap(request)),
//                product
//        ));
        RuntimeOptions runtime = new RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", request),
//                product
//        ));
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeVSwitches", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeVSwitchesPriResponse());
    }

    /**
     * 查询子网
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public DescribeVSwitchesResponse describeVSwitchesWithOptions(DescribeVSwitchesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeVSwitches", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeVSwitchesResponse());
    }

    /**
     * 创建子网
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateVSwitchResponse createVSwitchWithOptions(CreateVSwitchRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateVSwitch", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateVSwitchResponse());
    }

    /**
     * 删除子网
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteVSwitchResponse deleteVSwitchWithOptions(DeleteVSwitchRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteVSwitch", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteVSwitchResponse());
    }

    /**
     * 修改子网
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyVSwitchAttributeResponse modifyVSwitchAttributeWithOptions(ModifyVSwitchAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyVSwitchAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyVSwitchAttributeResponse());
    }

    /**
     * 查询磁盘
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public DescribeDisksPriResponse describeDiskList(DescribeDisksRequest request) throws Exception {
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));


//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", request),
//                product
//        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeDisks", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeDisksPriResponse());
    }

    /**
     * 创建磁盘
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateDiskResponse createDiskWithOptions(CreateDiskRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateDisk", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateDiskResponse());
    }

    /**
     * 删除磁盘
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteDiskResponse deleteDiskWithOptions(DeleteDiskRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteDisk", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteDiskResponse());
    }

    /**
     * 修改磁盘
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyDiskAttributeResponse modifyDiskAttributeWithOptions(ModifyDiskAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyDiskAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyDiskAttributeResponse());
    }

    /**
     * 扩容磁盘
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ResizeDiskResponse resizeDiskWithOptions(ResizeDiskRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ResizeDisk", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ResizeDiskResponse());
    }

    /**
     * 挂载磁盘
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public AttachDiskResponse attachDiskWithOptions(AttachDiskRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("AttachDisk", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new AttachDiskResponse());
    }

    /**
     * 卸载磁盘
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DetachDiskResponse detachDiskWithOptions(DetachDiskRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DetachDisk", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DetachDiskResponse());
    }

    /**
     * 查询自动快照策略
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DescribeAutoSnapshotPolicyExResponse describeAutoSnapshotPolicyExWithOptions(DescribeAutoSnapshotPolicyExRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeAutoSnapshotPolicyEx", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeAutoSnapshotPolicyExResponse());
    }

    /**
     * 创建自动快照策略
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateAutoSnapshotPolicyResponse createAutoSnapshotPolicyWithOptions(CreateAutoSnapshotPolicyRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateAutoSnapshotPolicy", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateAutoSnapshotPolicyResponse());
    }

    /**
     * 删除自动快照策略
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteAutoSnapshotPolicyResponse deleteAutoSnapshotPolicyWithOptions(DeleteAutoSnapshotPolicyRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteAutoSnapshotPolicy", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteAutoSnapshotPolicyResponse());
    }

    /**
     * 修改自动快照策略
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifyAutoSnapshotPolicyExResponse modifyAutoSnapshotPolicyExWithOptions(ModifyAutoSnapshotPolicyExRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyAutoSnapshotPolicyEx", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyAutoSnapshotPolicyExResponse());
    }

    /**
     * 绑定自动快照策略
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ApplyAutoSnapshotPolicyResponse applyAutoSnapshotPolicyWithOptions(ApplyAutoSnapshotPolicyRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ApplyAutoSnapshotPolicy", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ApplyAutoSnapshotPolicyResponse());
    }

    /**
     * 解绑自动快照策略
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CancelAutoSnapshotPolicyResponse cancelAutoSnapshotPolicyWithOptions(CancelAutoSnapshotPolicyRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CancelAutoSnapshotPolicy", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CancelAutoSnapshotPolicyResponse());
    }

    /**
     * 查询快照
     *
     * @param request
     * @param
     * @return
     * @throws Exception
     */
    public DescribeSnapshotsPriResponse describeSnapshot(DescribeSnapshotsRequest request) throws Exception {
//        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
//        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
//                new TeaPair("body", request),
//                product
//        ));
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSnapshots", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSnapshotsPriResponse());
    }

    public DescribeSnapshotLinksPriResponse describeSnapshotLink(DescribeSnapshotLinksRequest request) throws Exception {
        Common.validateModel(request);
        RuntimeOptions runtime = new RuntimeOptions();
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DescribeSnapshotLinks", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DescribeSnapshotLinksPriResponse());
    }

    /**
     * 创建快照
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public CreateSnapshotResponse createSnapshotWithOptions(CreateSnapshotRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("CreateSnapshot", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new CreateSnapshotResponse());
    }

    /**
     * 删除快照
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public DeleteSnapshotResponse deleteSnapshotWithOptions(DeleteSnapshotRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("DeleteSnapshot", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteSnapshotResponse());
    }

    /**
     * 修改快照
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ModifySnapshotAttributeResponse modifySnapshotAttributeWithOptions(ModifySnapshotAttributeRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifySnapshotAttribute", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifySnapshotAttributeResponse());
    }

    /**
     * 回滚快照
     *
     * @param request
     * @param runtime
     * @return
     * @throws Exception
     */
    @Override
    public ResetDisksResponse resetDisksWithOptions(ResetDisksRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ResetDisks", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ResetDisksResponse());
    }

    @Override
    public ResetDiskResponse resetDiskWithOptions(ResetDiskRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ResetDisk", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ResetDiskResponse());
    }

    @Override
    public StopInstanceResponse stopInstanceWithOptions(StopInstanceRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("StopInstance", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new StopInstanceResponse());
    }

    @Override
    public RenewReservedInstancesResponse renewReservedInstancesWithOptions(RenewReservedInstancesRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", Common.toMap(request)),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("RenewReservedInstances", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new RenewReservedInstancesResponse());
    }
    @Override
    public ModifyInstanceSpecResponse modifyInstanceSpecWithOptions(ModifyInstanceSpecRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        Map<String, Object> query = new HashMap<>();
        if (!Common.isUnset(request.allowMigrateAcrossZone)) {
            query.put("AllowMigrateAcrossZone", request.allowMigrateAcrossZone);
        }

        if (!Common.isUnset(request.async)) {
            query.put("Async", request.async);
        }

        if (!Common.isUnset(request.clientToken)) {
            query.put("ClientToken", request.clientToken);
        }

        if (!Common.isUnset(request.instanceId)) {
            query.put("InstanceId", request.instanceId);
        }

        if (!Common.isUnset(request.instanceType)) {
            query.put("InstanceType", request.instanceType);
        }

        if (!Common.isUnset(request.internetMaxBandwidthIn)) {
            query.put("InternetMaxBandwidthIn", request.internetMaxBandwidthIn);
        }

        if (!Common.isUnset(request.internetMaxBandwidthOut)) {
            query.put("InternetMaxBandwidthOut", request.internetMaxBandwidthOut);
        }

        if (!Common.isUnset(request.ownerAccount)) {
            query.put("OwnerAccount", request.ownerAccount);
        }

        if (!Common.isUnset(request.ownerId)) {
            query.put("OwnerId", request.ownerId);
        }

        if (!Common.isUnset(request.resourceOwnerAccount)) {
            query.put("ResourceOwnerAccount", request.resourceOwnerAccount);
        }

        if (!Common.isUnset(request.resourceOwnerId)) {
            query.put("ResourceOwnerId", request.resourceOwnerId);
        }

        if (!Common.isUnset(TeaModel.buildMap(request.systemDisk))) {
            query.put("SystemDisk", request.systemDisk);
        }

        if (!Common.isUnset(TeaModel.buildMap(request.temporary))) {
            query.put("Temporary", request.temporary);
        }
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", query),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ModifyInstanceSpec", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ModifyInstanceSpecResponse());
    }

    public ReplaceSystemDiskResponse replaceSystemDiskWithOptions(ReplaceSystemDiskRequest request, RuntimeOptions runtime) throws Exception {
        Common.validateModel(request);
        Map<String, Object> query = new HashMap<>();
        if (!Common.isUnset(request.architecture)) {
            query.put("Architecture", request.architecture);
        }

        if (!Common.isUnset(request.arn)) {
            query.put("Arn", request.arn);
        }

        if (!Common.isUnset(request.clientToken)) {
            query.put("ClientToken", request.clientToken);
        }

        if (!Common.isUnset(request.diskId)) {
            query.put("DiskId", request.diskId);
        }

        if (!Common.isUnset(request.encryptAlgorithm)) {
            query.put("EncryptAlgorithm", request.encryptAlgorithm);
        }

        if (!Common.isUnset(request.encrypted)) {
            query.put("Encrypted", request.encrypted);
        }

        if (!Common.isUnset(request.imageId)) {
            query.put("ImageId", request.imageId);
        }

        if (!Common.isUnset(request.instanceId)) {
            query.put("InstanceId", request.instanceId);
        }

        if (!Common.isUnset(request.KMSKeyId)) {
            query.put("KMSKeyId", request.KMSKeyId);
        }

        if (!Common.isUnset(request.keyPairName)) {
            query.put("KeyPairName", request.keyPairName);
        }

        if (!Common.isUnset(request.ownerAccount)) {
            query.put("OwnerAccount", request.ownerAccount);
        }

        if (!Common.isUnset(request.ownerId)) {
            query.put("OwnerId", request.ownerId);
        }

        if (!Common.isUnset(request.password)) {
            query.put("Password", request.password);
        }

        if (!Common.isUnset(request.passwordInherit)) {
            query.put("PasswordInherit", request.passwordInherit);
        }

        if (!Common.isUnset(request.platform)) {
            query.put("Platform", request.platform);
        }

        if (!Common.isUnset(request.resourceOwnerAccount)) {
            query.put("ResourceOwnerAccount", request.resourceOwnerAccount);
        }

        if (!Common.isUnset(request.resourceOwnerId)) {
            query.put("ResourceOwnerId", request.resourceOwnerId);
        }

        if (!Common.isUnset(request.securityEnhancementStrategy)) {
            query.put("SecurityEnhancementStrategy", request.securityEnhancementStrategy);
        }

        if (!Common.isUnset(request.useAdditionalService)) {
            query.put("UseAdditionalService", request.useAdditionalService);
        }

        if (!Common.isUnset(request.systemDisk)) {
            query.put("SystemDisk", request.systemDisk);
        }
        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
                new TeaPair("body", query),
                product
        ));
        return TeaModel.toModel(this.doRPCRequest("ReplaceSystemDisk", "2014-05-26", "HTTPS", "POST", "AK", "json", req, runtime), new ReplaceSystemDiskResponse());
    }
    @Override
    public Map<String, ?> doRPCRequest(String action, String version, String protocol, String method, String authType, String bodyType, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
        TeaModel.validateParams(request, "request");
//        JSONObject reqData = ActionFactory.requestData.get();
//        JSONObject reqData = BaseClient.bodys.get();
        HashMap body = (HashMap) request.getBody();
        if (CollUtil.isEmpty(body)) {
            body = new HashMap();
            request.setBody(body);
        }
        if(CollUtil.isEmpty(request.getHeaders())){
            request.setHeaders(new HashMap());
        }
        if(body.containsKey("ResourceGroup")){
            request.getHeaders().put("x-acs-resourcegroupid",String.valueOf(body.get("ResourceGroup")));
        }
        if(body.containsKey("Department")){
            request.getHeaders().put("x-acs-organizationid", String.valueOf(body.get("Department")));
        }
//        if (!body.containsKey("ResourceGroup") && ObjectUtil.isNotNull(reqData) && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//            request.getHeaders().put("x-acs-resourcegroupid", reqData.getString("resourceGroupId"));
//        }else{
//            request.getHeaders().put("x-acs-resourcegroupid", String.valueOf(body.get("ResourceGroup")));
//        }
//        if (!body.containsKey("Department") && ObjectUtil.isNotNull(reqData) && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//            request.getHeaders().put("x-acs-organizationid", reqData.getString("departmentId"));
//        }else{
//            request.getHeaders().put("x-acs-organizationid", String.valueOf(body.get("Department")));
//        }
//        CloudAccessBean auth = ActionFactory.credentials.get();
////        if ("v3_1_4".equals(auth.getVersion())) {
////            request.query.put("regionId", super._regionId);
////        } else if ("v3_1_2".equals(auth.getVersion())) {
////            request.query.put("RegionId", super._regionId);
////        }

        Map<String, Object> runtime_ = TeaConverter.buildMap(
                new TeaPair("timeouted", "retry"),
                new TeaPair("readTimeout", Common.defaultNumber(runtime.readTimeout, _readTimeout)),
                new TeaPair("connectTimeout", Common.defaultNumber(runtime.connectTimeout, _connectTimeout)),
                new TeaPair("httpProxy", Common.defaultString(runtime.httpProxy, _httpProxy)),
                new TeaPair("httpsProxy", Common.defaultString(runtime.httpsProxy, _httpsProxy)),
                new TeaPair("noProxy", Common.defaultString(runtime.noProxy, _noProxy)),
                new TeaPair("socks5Proxy", Common.defaultString(runtime.socks5Proxy, _socks5Proxy)),
                new TeaPair("socks5NetWork", Common.defaultString(runtime.socks5NetWork, _socks5NetWork)),
                new TeaPair("maxIdleConns", Common.defaultNumber(runtime.maxIdleConns, _maxIdleConns)),
                new TeaPair("retry", TeaConverter.buildMap(
                        new TeaPair("retryable", runtime.autoretry),
                        new TeaPair("maxAttempts", Common.defaultNumber(runtime.maxAttempts, 3))
                )),
                new TeaPair("backoff", TeaConverter.buildMap(
                        new TeaPair("policy", Common.defaultString(runtime.backoffPolicy, "no")),
                        new TeaPair("period", Common.defaultNumber(runtime.backoffPeriod, 1))
                )),
                new TeaPair("ignoreSSL", runtime.ignoreSSL)
        );

        TeaRequest _lastRequest = null;
        Exception _lastException = null;
        TeaResponse _lastResponse = null;
        long _now = System.currentTimeMillis();
        int _retryTimes = 0;
        while (Tea.allowRetry((Map<String, Object>) runtime_.get("retry"), _retryTimes, _now)) {
            if (_retryTimes > 0) {
                int backoffTime = Tea.getBackoffTime(runtime_.get("backoff"), _retryTimes);
                if (backoffTime > 0) {
                    Tea.sleep(backoffTime);
                }
            }
            _retryTimes = _retryTimes + 1;
            try {
                TeaRequest request_ = new TeaRequest();
                request_.protocol = Common.defaultString(_protocol, protocol);
                request_.method = method;
                request_.pathname = "/";
                request_.query = TeaConverter.merge(String.class,
                        TeaConverter.buildMap(
                                new TeaPair("Action", action),
                                new TeaPair("Format", "json"),
                                new TeaPair("Version", version),
                                new TeaPair("Timestamp", com.aliyun.openapiutil.Client.getTimestamp()),
                                new TeaPair("SignatureNonce", Common.getNonce())
                        ),
                        request.query
                );
                Map<String, String> headers = this.getRpcHeaders();
                if (Common.isUnset(headers)) {
                    // endpoint is setted in product client
                    request_.headers = TeaConverter.buildMap(
                            new TeaPair("host", _endpoint),
                            new TeaPair("x-acs-version", version),
                            new TeaPair("x-acs-action", action),
                            new TeaPair("user-agent", this.getUserAgent())
                    );
                } else {
                    request_.headers = TeaConverter.merge(String.class,
                            TeaConverter.buildMap(
                                    new TeaPair("host", _endpoint),
                                    new TeaPair("x-acs-version", version),
                                    new TeaPair("x-acs-action", action),
                                    new TeaPair("user-agent", this.getUserAgent())
                            ),
                            headers
                    );
                }
                if (CollUtil.isNotEmpty(request.headers)){
                    request_.headers.putAll(request.getHeaders());
                }
//                if ("v3_1_4".equals(auth.getVersion())) {
                    String hostEndpoint = _endpoint.substring(0, _endpoint.indexOf("/asapi"));
                    request_.headers.put("host", hostEndpoint);
                    request_.pathname = "/asapi/v3/";
//                }

                if (!Common.isUnset(request.body)) {
                    Map<String, Object> m = Common.assertAsMap(request.body);
                    Map<String, Object> tmp = Common.anyifyMapValue(com.aliyun.openapiutil.Client.query(m));
                    request_.body = Tea.toReadable(Common.toFormString(tmp));
                    request_.headers.put("content-type", "application/x-www-form-urlencoded");
                }

                if (!Common.equalString(authType, "Anonymous")) {
                    String accessKeyId = this.getAccessKeyId();
                    String accessKeySecret = this.getAccessKeySecret();
                    String securityToken = this.getSecurityToken();
                    if (!Common.empty(securityToken)) {
                        request_.query.put("SecurityToken", securityToken);
                    }

                    request_.query.put("SignatureMethod", "HMAC-SHA1");
                    request_.query.put("SignatureVersion", "1.0");
                    request_.query.put("AccessKeyId", accessKeyId);
                    Map<String, Object> t = null;
                    if (!Common.isUnset(request.body)) {
                        t = Common.assertAsMap(request.body);
                    }

                    Map<String, String> signedParam = TeaConverter.merge(String.class,
                            request_.query,
                            com.aliyun.openapiutil.Client.query(t)
                    );
                    request_.query.put("Signature", com.aliyun.openapiutil.Client.getRPCSignature(signedParam, request_.method, accessKeySecret));
                }

                _lastRequest = request_;
                TeaResponse response_ = Tea.doAction(request_, runtime_, interceptorChain);
                _lastResponse = response_;

                if (Common.is4xx(response_.statusCode) || Common.is5xx(response_.statusCode)) {
                    Object _res = Common.readAsJSON(response_.body);
                    Map<String, Object> err = Common.assertAsMap(_res);
                    Object requestId = com.aliyun.teaopenapi.Client.defaultAny(err.get("RequestId"), err.get("requestId"));
                    throw new TeaException(TeaConverter.buildMap(
                            new TeaPair("code", "" + com.aliyun.teaopenapi.Client.defaultAny(err.get("Code"), err.get("code")) + ""),
                            new TeaPair("message", "code: " + response_.statusCode + ", " + com.aliyun.teaopenapi.Client.defaultAny(err.get("Message"), err.get("message")) + " request id: " + requestId + ""),
                            new TeaPair("data", err)
                    ));
                }

                if (Common.equalString(bodyType, "binary")) {
                    Map<String, Object> resp = TeaConverter.buildMap(
                            new TeaPair("body", response_.body),
                            new TeaPair("headers", response_.headers)
                    );
                    return resp;
                } else if (Common.equalString(bodyType, "byte")) {
                    byte[] byt = Common.readAsBytes(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", byt),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (Common.equalString(bodyType, "string")) {
                    String str = Common.readAsString(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", str),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (Common.equalString(bodyType, "json")) {
                    Object obj = Common.readAsJSON(response_.body);
                    Map<String, Object> res = Common.assertAsMap(obj);
                    return TeaConverter.buildMap(
                            new TeaPair("body", res),
                            new TeaPair("headers", response_.headers)
                    );
                } else if (Common.equalString(bodyType, "array")) {
                    Object arr = Common.readAsJSON(response_.body);
                    return TeaConverter.buildMap(
                            new TeaPair("body", arr),
                            new TeaPair("headers", response_.headers)
                    );
                } else {
                    return TeaConverter.buildMap(
                            new TeaPair("headers", response_.headers)
                    );
                }

            } catch (Exception e) {
                if (Tea.isRetryable(e)) {
                    _lastException = e;
                    continue;
                }
                throw e;
            } finally {
                if (!Common.isUnset(_lastResponse)
                        && !Common.isUnset(_lastResponse.response)
                        && !Common.isUnset(_lastResponse.response.body())) {
                    _lastResponse.response.close();
                }
            }
        }
        throw new TeaUnretryableException(_lastRequest, _lastException);
    }

}