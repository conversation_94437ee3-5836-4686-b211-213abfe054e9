//package com.futong.gemini.plugin.cloud.ali.v316.common.extend;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.tea.TeaConverter;
//import com.aliyun.tea.TeaException;
//import com.aliyun.tea.TeaModel;
//import com.aliyun.tea.TeaPair;
//import com.aliyun.teaopenapi.models.Config;
//import com.aliyun.teaopenapi.models.OpenApiRequest;
//import com.aliyun.teaopenapi.models.Params;
//import com.aliyun.teautil.models.RuntimeOptions;
//import com.futong.action.ActionFactory;
//import com.futong.bean.CloudAccessBean;
//
//import java.util.HashMap;
//import java.util.Map;
//public class KafkaClient extends com.aliyun.alikafka20190916.Client {
//
////    private static final TeaPair product = new TeaPair("query", TeaConverter.buildMap(new TeaPair("Product", "alikafka")));
////
//    public KafkaClient(Config config) throws Exception {
//        super(config);
//        super._signatureAlgorithm = "v2";
//    }
////
////    @Override
////    public GetInstanceListResponse getInstanceListWithOptions(GetInstanceListRequest request, RuntimeOptions runtime) throws Exception {
////        Common.validateModel(request);
////        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
////                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
////                product
////        ));
////        return TeaModel.toModel(this.doRPCRequest("GetInstanceList", "2019-09-16", "HTTPS", "POST", "AK", "json", req, runtime), new GetInstanceListResponse());
////    }
////
////    @Override
////    public DeleteInstanceResponse deleteInstanceWithOptions(DeleteInstanceRequest request, RuntimeOptions runtime) throws Exception {
////        Common.validateModel(request);
////        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
////                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
////                product
////        ));
////        return TeaModel.toModel(this.doRPCRequest("DeleteInstance", "2019-09-16", "HTTPS", "POST", "AK", "json", req, runtime), new DeleteInstanceResponse());
////    }
////
////    @Override
////    public ReleaseInstanceResponse releaseInstanceWithOptions(ReleaseInstanceRequest request, RuntimeOptions runtime) throws Exception {
////        Common.validateModel(request);
////        OpenApiRequest req = OpenApiRequest.build(TeaConverter.buildMap(
////                new TeaPair("body", com.aliyun.teautil.Common.toMap(request)),
////                product
////        ));
////        return TeaModel.toModel(this.doRPCRequest("ReleaseInstance", "2019-09-16", "HTTPS", "POST", "AK", "json", req, runtime), new ReleaseInstanceResponse());
////    }
//    @Override
//    public Map<String, ?> callApi(Params params, OpenApiRequest request, RuntimeOptions runtime) throws Exception {
//        request.getQuery().put("Product", "alikafka");
//        JSONObject reqData = ActionFactory.requestData.get();
//        HashMap body = (HashMap) request.getBody();
//        if (CollUtil.isEmpty(body)){
//            body = new HashMap();
//            request.setBody(body);
//        }
//        if (!body.containsKey("ResourceGroup") && reqData.containsKey("resourceGroupId")) {
//            body.put("ResourceGroup", reqData.getString("resourceGroupId"));
//        }
//        if (!body.containsKey("Department") && reqData.containsKey("departmentId")) {
//            body.put("Department", reqData.getString("departmentId"));
//        }
//        CloudAccessBean auth = ActionFactory.credentials.get();
//        if ("v3_1_2".equals(auth.getVersion())) {
//            request.query.put("RegionId", super._regionId);
//        }
//        if (com.aliyun.teautil.Common.isUnset(TeaModel.buildMap(params))) {
//            throw new TeaException(TeaConverter.buildMap(
//                    new TeaPair("code", "ParameterMissing"),
//                    new TeaPair("message", "'params' can not be unset")
//            ));
//        }
//
//        if (com.aliyun.teautil.Common.isUnset(_signatureAlgorithm) || !com.aliyun.teautil.Common.equalString(_signatureAlgorithm, "v2")) {
//            return this.doRequest(params, request, runtime);
//        } else if (com.aliyun.teautil.Common.equalString(params.style, "ROA") && com.aliyun.teautil.Common.equalString(params.reqBodyType, "json")) {
//            return this.doROARequest(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else if (com.aliyun.teautil.Common.equalString(params.style, "ROA")) {
//            return this.doROARequestWithForm(params.action, params.version, params.protocol, params.method, params.authType, params.pathname, params.bodyType, request, runtime);
//        } else {
//            return this.doRPCRequest(params.action, params.version, params.protocol, params.method, params.authType, params.bodyType, request, runtime);
//        }
//    }
//
//}
