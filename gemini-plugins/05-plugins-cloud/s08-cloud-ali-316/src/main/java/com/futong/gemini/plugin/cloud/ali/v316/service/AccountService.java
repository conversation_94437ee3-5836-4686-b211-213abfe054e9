package com.futong.gemini.plugin.cloud.ali.v316.service;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.ecs20140526.Client;
import com.aliyun.ecs20140526.models.DescribeRegionsResponse;
import com.aliyun.ecs20140526.models.DescribeRegionsResponseBody;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.ali.v316.common.CloudClient;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;
import java.util.stream.Collectors;
@Slf4j
public class AccountService {

    public static Map<Locale, JSONObject> accountForm = new HashMap<>();
    public static String accountDispatch;

    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }

    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            CloudClient.client.execute(request.getBody(), Client::describeRegions);
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

    /**
     * #{内部参数},${页面参数}
     *
     * @param request
     * @return
     */
    public static BaseResponse getFetchAddModel(BaseCloudRequest request) {
        //替换参数云账号ID
        String text = StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId());
        JSONObject result = JSON.parseObject(text);
        DescribeRegionsResponse describeRegionsResponse = CloudClient.client.execute(request.getBody(), Client::describeRegions);
        List<DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion> regions = describeRegionsResponse.body.regions.region;
        if (request.getBody().containsKey("all")) {
            //根据地域生成全量调度任务
            return new BaseDataResponse<>(listAllDispatcher(result, regions));
        } else {
            //获取region信息
            List<HashMap<String, String>> formRegionItems = regions.stream().map(t -> {
                HashMap<String, String> region = new HashMap<>();
                region.put("label", t.getLocalName());
                region.put("value", t.getRegionId());
                return region;
            }).collect(Collectors.toList());
            result.getJSONObject("form").getJSONObject("region").put("items", formRegionItems);
            return new BaseDataResponse<>(result);
        }
    }

    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);
        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        //根据地域生成全量调度任务
        DescribeRegionsResponse describeRegionsResponse = CloudClient.client.execute(request.getBody(), Client::describeRegions);
        List<JSONObject> dispatchers = listAllDispatcher(result, describeRegionsResponse.body.regions.region);
        //调用gourd服务-批量添加调度任务
        return SpringUtil.getBean(GourdProxy.class).createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model, List<DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion> regions) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            } else {
                String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                for (DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion region : regions) {
                    String itemStrRegion = StrUtil.replace(itemStr, "${region.label}", region.getLocalName());
                    itemStrRegion = StrUtil.replace(itemStrRegion, "${region.value}", region.getRegionId());
                    dispatchers.add(JSON.parseObject(itemStrRegion));
                }
            }
        });
        return dispatchers;
    }
}
