package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import lombok.Data;

import java.util.List;
/**
 * 创建登录策略请求参数
 *
 * <AUTHOR>
 * @since 2024/10/16 17:03
 */
@Data
public class CreateLoginPolicyRequest extends TeaModel {

    /**
     * 登录策略名。
     * 长度为2~50个字符。
     * 说明 该参数为必填。
     */
    @NameInMap("name")
    public String name;

    /**
     * 登录策略描述。
     */
    @NameInMap("description")
    public String description;

    /**
     * 登录策略的策略属性，默认为ALLOW。
     * ALLOW：白名单。
     * DENY：黑名单。
     */
    @NameInMap("rule")
    public String rule;

    /**
     * 登录策略的IP段，不传则为空。
     * 说明 该参数需要按照格式传入，格式为 [{"protocol":"<登录策略所使用的协议>","ipRange":"<登录策略IP段>"}]
     * protocol：登录策略所使用协议，仅支持IPv4。
     * ipRange：登录策略IP段。格式如***********/24，当掩码为32位时表示仅允许该IP地址登录，网段地址不能为空且不可重复。
     */
    @NameInMap("ipRanges")
    public List<IpRanges> ipRanges;

    @Data
    public static class IpRanges {
        /**
         * <登录策略所使⽤协议
         */
        @NameInMap("protocol")
        public String protocol;

        /**
         * 登录策略IP段
         */
        @NameInMap("ipRange")
        public String ipRange;
    }

    /**
     * 登录策略的时间段，不传则为空。
     * 说明 该参数需要按照格式传入，格式为 [{"startTime":"<开始时间>","endTime":"<结束时间>"}]
     * startTime：登录策略时间段的开始时间，例如08:00。
     * endTime：登录策略时间段的结束时间，例如18:00。
     */
    @NameInMap("timeRanges")
    public List<TimeRanges> timeRanges;

    @Data
    public static class TimeRanges {
        /**
         * 登录策略时间段的开始时间，例如08:00。
         */
        @NameInMap("startTime")
        public String startTime;

        /**
         * 登录策略时间段的结束时间，例如18:00。
         */
        @NameInMap("endTime")
        public String endTime;
    }

    /**
     * 登录策略是否有效，不传默认为true。
     */
    @NameInMap("enable")
    public Boolean enable;

    /**
     * 登录策略共享范围,不传默认全局共享：
     * organizationVisibility.global：全局共享。
     * organizationVisibility.organization：本组织内共享。
     * organizationVisibility.orgAndSubOrgs：下级组织共享。
     */
    @NameInMap("organizationVisibility")
    public String organizationVisibility;

}
