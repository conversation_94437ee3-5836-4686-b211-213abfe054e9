package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.aliyun.rds20140815.models.DescribeDBInstancesResponseBody;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import com.aliyun.tea.Validation;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class DescribeDBInstancesPriResponse extends TeaModel {
    @NameInMap("headers")
    @Validation(required = true)
    public Map<String, String> headers;

    @NameInMap("statusCode")
    @Validation(required = true)
    public Integer statusCode;

    @NameInMap("body")
    @Validation(required = true)
    public DescribeDBInstancesBody body;

    @Data
    public static class DescribeDBInstancesBody extends TeaModel {
        @NameInMap("Items")
        public DBInstance items;

        @NameInMap("NextToken")
        public String nextToken;

        @NameInMap("PageNumber")
        public Integer pageNumber;

        @NameInMap("PageRecordCount")
        public Integer pageRecordCount;

        @NameInMap("RequestId")
        public String requestId;

        @NameInMap("TotalRecordCount")
        public Integer totalRecordCount;

    }


    @Data
    public static class DBInstance extends TeaModel {
        @NameInMap("DBInstance")
        public List<DescribeDBInstancesResponseBodyDBInstance> DBInstance;
    }

    @Data
    public static class DescribeDBInstancesResponseBodyDBInstance extends DescribeDBInstancesResponseBody.DescribeDBInstancesResponseBodyItemsDBInstance {
        @NameInMap("ResourceGroup")
        public String resourceGroup;

        @NameInMap("Department")
        public String department;
    }
}