package com.futong.gemini.plugin.cloud.ali.v316.common.extend.model;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.tea.NameInMap;
import com.aliyun.tea.TeaModel;
import lombok.Data;

import java.util.List;
/**
 * 创建用户
 *
 * <AUTHOR>
 * @since 2024/10/16 17:03
 */
@Data
public class CreateTenantRequest extends TeaModel {

    /**
     * 系统规定参数。取值：AddUser。
     */
    @NameInMap("Action")
    public String action;

    /**
     * 用户手机号码。
     */
    @NameInMap("CellphoneNum")
    public String cellphoneNum;

    /**
     * 用户显示名。
     */
    @NameInMap("DisplayName")
    public String displayName;

    /**
     * 用户邮箱地址。
     */
    @NameInMap("Email")
    public String email;

    /**
     * 用户登录名。
     */
    @NameInMap("LoginName")
    public String loginName;

    /**
     * 登录策略ID。
     */
    @NameInMap("LoginPolicyId")
    public Integer loginPolicyId;

    /**
     * 国际区号。
     */
    @NameInMap("MobileNationCode")
    public String mobileNationCode;

    /**
     * 组织ID。
     */
    @NameInMap("OrganizationId")
    public Integer organizationId;

    /**
     * 角色ID列表。
     */
    @NameInMap("roleIdList")
    public JSONObject roleIdList;

    /**
     * 角色id集合
     */
    @Data
    public static class RoleIdList {
        /**
         * 角色id集合
         */
        @NameInMap("RoleIdList")
        public List<String> roleId;
    }

    /**
     * 组织UUID。
     */
    @NameInMap("OrganizationUuid")
    public String organizationUuid;

    /**
     * 座机号码。
     */
    @NameInMap("TelephoneNum")
    public String telephoneNum;

    /**
     * 钉钉密钥。
     */
    @NameInMap("DingTalkToken")
    public String dingTalkToken;

    /**
     * 是否启用钉钉通知。
     */
    @NameInMap("EnableDingTalk")
    public Boolean enableDingTalk;

    /**
     * 是否启用短信通知。
     */
    @NameInMap("EnableShortMessage")
    public Boolean enableShortMessage;

    /**
     * 是否启用邮件通知。
     */
    @NameInMap("EnableEmail")
    public Boolean enableEmail;

    /**
     * 是否为多云同步用户：master为主云，slave为从云。
     */
    @NameInMap("MultiCloudStatus")
    public String multiCloudStatus;
}
