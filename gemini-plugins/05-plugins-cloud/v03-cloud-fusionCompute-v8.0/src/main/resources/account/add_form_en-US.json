{"description": "The initial management account only supports the main account for management", "model": [{"type": "main", "name": "Cloud platform operation master account", "description": "Cloud platform operation master account, which can be used for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Cloud Account"}, {"field": "serverIp", "label": "Server Host", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Host"}, {"field": "username", "label": "username", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your username"}, {"field": "password", "label": "password", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your password"}, {"field": "serverPort", "label": "Server Port", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter your Server Port"}, {"field": "protocol", "label": "Protocol", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "http"}, {"label": "HTTPS", "value": "https"}], "required": true, "isUpdate": true, "tips": "Please select your Protocol"}, {"field": "description", "label": "Description", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "Please enter your Description"}]}]}