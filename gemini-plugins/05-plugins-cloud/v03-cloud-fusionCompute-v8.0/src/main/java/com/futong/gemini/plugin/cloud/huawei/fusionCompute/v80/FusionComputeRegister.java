package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80;

import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service.*;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

public class FusionComputeRegister extends BaseCloudRegister {

    @Override
    public void load() {
        //加载云平台操作
        onAfterLoadAccount();
        //加载同步调度信息
        onAfterLoadFetch();
        //加载虚拟机操作
        onAfterLoadCompute();
        //加载磁盘操作
        onAfterLoadStorage();
        //加载虚拟交换机
        onAfterLoadDVSwitch();
        //加载快照操作
        onAfterLoadVmSnapshot();
    }

    public void onAfterLoadAccount() {
        //云账号验证
        register(ActionType.AUTH_PLATFORM_ACCOUNT, AccountService::authAccount);
        //获取云账号表单信息
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, AccountService::getAccountAddForm);
        //获取调度添加模型
        register(ActionType.GET_PLATFORM_FETCH_DISPATCH_MODEL, AccountService::getFetchAddModel);
        //添加默认调度任务
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, AccountService::createFetchDispatch);
    }


    public void onAfterLoadFetch() {
        //urn转uri
        registerBefore(CloudService::defaultSiteUri,
                ActionType.FETCH_COMPUTE_HOST,
                ActionType.FETCH_COMPUTE_INSTANCE,
                ActionType.FETCH_STORAGE_DISK,
                ActionType.FETCH_NEUTRON_SWITCH,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT,
                ActionType.FETCH_STORAGE_POOL,
                ActionType.FETCH_STORAGE_UNIT,
                ActionType.FETCH_STORAGE_RESOURCE,
                ActionType.FETCH_COMPUTE_SECURITYGROUP,
                ActionType.FETCH_COMPUTE_INSTANCE_PERF,
                ActionType.FETCH_COMPUTE_HOST_PERF,
                ActionType.FETCH_PLATFORM_FOLDER,
                ActionType.FETCH_COMPUTE_OS_VERSION);
        //默认查询页码及条数50
        registerBefore(CloudService::defaultPage100,
                ActionType.FETCH_COMPUTE_HOST,
                ActionType.FETCH_COMPUTE_INSTANCE,
                ActionType.FETCH_STORAGE_DISK,
                ActionType.FETCH_NEUTRON_SUBNET,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT,
                ActionType.FETCH_STORAGE_POOL,
                ActionType.FETCH_STORAGE_UNIT,
                ActionType.FETCH_STORAGE_RESOURCE,
                ActionType.FETCH_COMPUTE_SECURITYGROUP);
        //同步站点
        register(ActionType.FETCH_PLATFORM_RESOURCE_POOL, FetchService::fetchSite);
        //同步集群
        register(ActionType.FETCH_PLATFORM_CLUSTER, FetchService::fetchCluster);
        //同步物理机
        register(ActionType.FETCH_COMPUTE_HOST, FetchService::fetchHost);
        //同步云主机
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchInstance);
        //同步磁盘
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchDisk);
        //获取磁盘快照
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchSnapshot);
        //同步交换机
        register(ActionType.FETCH_NEUTRON_SWITCH, FetchService::fetchDVSwitch);
        //获取端口组（Subnet）
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet);
        //获取上行链路组
        register(ActionType.FETCH_NEUTRON_UPLINK_PORTS, FetchService::fetchUplinkPorts);
        //获取Vlan池
        register(ActionType.FETCH_NEUTRON_VLAN, FetchService::fetchVlanPool);
        //获取安全组
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchSecurityGroup);
        //获取安全组规则
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP_RULE, FetchService::fetchSecurityGroupRule);
        //获取数据存储
        register(ActionType.FETCH_STORAGE_POOL, FetchService::fetchDataStorage);
        //获取存储设备
        register(ActionType.FETCH_STORAGE_UNIT, FetchService::fetchStorageUnit);
        //获取存储资源
        register(ActionType.FETCH_STORAGE_RESOURCE, FetchService::fetchStorageResource);
        //获取安全组规则
//        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchCloudService::fetchSecurityGroup);
        //同步云主机性能数据
        registerBefore(ActionType.FETCH_COMPUTE_INSTANCE_PERF,
                FetchService::defaultMetricRequest,
                FetchService::defaultEcsMetricNames,
                FetchService::defaultEcsMetricDimensions);
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchInstancePerf);
        //同步物理机性能数据
        registerBefore(ActionType.FETCH_COMPUTE_HOST_PERF,
                FetchService::defaultMetricRequest,
                FetchService::defaultHostMetricNames,
                FetchService::defaultHostMetricDimensions);
        register(ActionType.FETCH_COMPUTE_HOST_PERF, FetchService::fetchHostPerf);
        //获取告警信息,默认查询时间区间一天
        registerBefore(CloudService::defaultStartEndTimeOneDay,
                ActionType.FETCH_PLATFORM_ALARM,
                ActionType.FETCH_PLATFORM_EVENT);
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm);
        //获取事件
        register(ActionType.FETCH_PLATFORM_EVENT, FetchService::fetchEvent);
        //获取文件夹
        register(ActionType.FETCH_PLATFORM_FOLDER, FetchService::fetchFolder);
        //同步操作系统
        register(ActionType.FETCH_COMPUTE_OS_VERSION, FetchService::fetchOsVersion);
    }

    public void onAfterLoadCompute(){
        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshEcs)
                //从刷新配置中获取实例ID;
                .addTransferCloud("$.refreshConfig.data", "$.urn", BaseUtils::formatSingle);
        //新增虚拟机
        register(ActionType.CREATE_COMPUTE_INSTANCE, VmService::createVm)
                .addAfter(CloudService::toAfterBizResId)
                //设置刷新请求，指定请求Action
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                //设置刷新总次数,刷新频次，首次刷新延迟
                .addSetRefreshConfig(20, 5000, 20000)
                //批量创建基于响应分割刷新任务
                .addSetRefreshSplitData("response","$.data.urn")
                //添加刷新任务
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //修改虚拟机
        register(ActionType.UPDATE_COMPUTE_INSTANCE, VmService::updateVm)
                .addTransferCloud("$.ci.openId", "$.urn")
                .addTransferCloud("$.model.resourceName", "$.name", true)
                .addTransferCloud("$.model.description", "$.description", false)
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(0, 0, 1000)
                //单资源操作,直接设置刷新请求的data信息
                .addSetRefreshData("request","$.body.cloud.urn")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //删除虚拟机
        register(ActionType.DELETE_COMPUTE_INSTANCE, VmService::deleteVm)
                .addBefore(CloudService::defaultVmDelete)
                .addTransferCloud("$.cis.openId", "$.urns")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(20, 2000, 1000)
                //批量操作基于请求分割刷新任务
                .addSetRefreshSplitData("request","$.body.cloud.urns")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //开启虚拟机
        register(ActionType.START_COMPUTE_INSTANCE, VmService::startVm)
                .addTransferCloud("$.cis.openId", "$.urns")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.urns")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //关闭虚拟机
        register(ActionType.STOP_COMPUTE_INSTANCE, VmService::stopVm)
                .addBefore(CloudService::toCloudForce)
                .addTransferCloud("$.cis.openId", "$.urns")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.urns")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //重启虚拟机
        register(ActionType.REBOOT_COMPUTE_INSTANCE, VmService::rebootVm)
                .addBefore(CloudService::toCloudForce)
                .addTransferCloud("$.cis.openId", "$.urns")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.urns")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //获取云主机的VNC远程登录地址
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, VmService::describeInstanceVncUrl)
                .addBefore(CloudService::toCiOpenIdUri);
        //获取云主机密码
        register(ActionType.QUERY_COMPUTE_INSTANCE_PASSWORD, VmService::getEcsPassword)
                .addBefore(CloudService::toCiOpenIdUri);
        //修改云主机密码
        register(ActionType.UPDATE_COMPUTE_INSTANCE_VNC, VmService::resetEcsPassword)
                .addTransferCloud("$.model.vncPassword", "$.vncNewPassword", true);

        //获取云主机数量
        register(ActionType.QUERY_COMPUTE_INSTANCE_TOTAL, VmService::queryInstanceTotal);
        //获取物理机数量
        register(ActionType.QUERY_COMPUTE_HOST_TOTAL, VmService::queryHostTotal);
        //获取模版
        register(ActionType.QUERY_COMPUTE_TEMPLATE, VmService::describeTemplateInstance);

    }

    public void onAfterLoadStorage(){
        //创建云硬盘
        register(ActionType.CREATE_STORAGE_DISK, StorageService::createVolume);
        //修改云硬盘
        register(ActionType.UPDATE_STORAGE_DISK, StorageService::updateVolume)
                .addBefore(CloudService::toCiOpenIdUri)
                .addTransferCloud("$.model.resourceName", "$.name", true)
                .addTransferCloud("$.model.diskMode", "$.diskMode", true);
        //删除云硬盘
        register(ActionType.DELETE_STORAGE_DISK, StorageService::deleteVolume)
                .addTransferCloud("$.cis.openId", "$.volUrns");
        //挂载云硬盘
        register(ActionType.ATTACH_STORAGE_DISK, StorageService::attachVolume)
                .addTransferCloud("$.ci.openId", "$.volUrn")
                .addTransferCloud("$.model.instanceId", "$.instanceId");
        //卸载云硬盘
        register(ActionType.DETACH_STORAGE_DISK, StorageService::detachVolume)
                .addTransferCloud("$.ci.openId", "$.volUrn")
                .addTransferCloud("$.ci.relationInstance.relationOpenId", "$.instanceId");
        //扩容云硬盘
        register(ActionType.RESIZE_STORAGE_DISK, StorageService::resizeVolume)
                .addTransferCloud("$.ci.openId", "$.volUrn")
                .addTransferCloud("$.ci.relationInstance.relationOpenId", "$.instanceId")
                .addTransferCloud("$.model.size", "$.capacity");
    }

    public void onAfterLoadDVSwitch(){
        //创建虚拟交换机
        register(ActionType.CREATE_NEUTRON_VSWITCH, DVSwitchService::createDVSwitch);
        //修改交换机
        register(ActionType.UPDATE_NEUTRON_VSWITCH, DVSwitchService::updateDVSwitch)
                .addBefore(CloudService::toCiOpenIdUri)
                .addTransferCloud("$.model.resourceName", "$.name", true)
                .addTransferCloud("$.model.description", "$.description", false);
        //删除虚拟交换机
        register(ActionType.DELETE_NEUTRON_VSWITCH, DVSwitchService::deleteDVSwitch)
                .addBefore(CloudService::toCiOpenIdUri);
    }

    public void onAfterLoadVmSnapshot() {
        //创建快照
        register(ActionType.CREATE_STORAGE_SNAPSHOT, SnapshotService::createSnapshot);
        //修改快照
        register(ActionType.UPDATE_STORAGE_SNAPSHOT, SnapshotService::updateSnapshot);
        //删除快照
        register(ActionType.DELETE_STORAGE_SNAPSHOT, SnapshotService::deleteSnapshot);
    }

    public static void main(String[] args) {

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 截断到分钟（清除秒和纳秒）
        LocalDateTime truncated = now.truncatedTo(ChronoUnit.MINUTES);

        // 转换为毫秒时间戳
        long minuteMillis = truncated.atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();

        System.out.println("整分钟时间戳: " + minuteMillis);

        long epochSeconds = minuteMillis; // 输入时间戳1745225311

        // 通过秒数直接创建Instant对象
        Instant instant = Instant.ofEpochSecond(epochSeconds);

        // 定义时间格式和时区（UTC）
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd HH:mm:ss");

        String formattedDate = formatter.format(instant);
        System.out.println("UTC时间: " + formattedDate); // 输出: 2025-04-21 17:09:03
    }
}
