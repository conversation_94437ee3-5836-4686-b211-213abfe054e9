package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.function.FTBefore;
import com.futong.common.log.DynamicLoggerConfigurator;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.http.HttpClientUtil;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.service.AccountService;
import com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.vo.LoginResponse;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudPluginTemplate;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.List;
import java.util.Locale;

@Slf4j
public class FusionComputeTemplate extends BaseCloudPluginTemplate {
    @Override
    public void init(String key) {
        super.init(key);
        loadAccountForm();
        loadAccountDispatch();
        //指定log日志目录
        DynamicLoggerConfigurator.addLogger("com.futong.gemini.cloud.fusionCompute", "/cloud/fusionCompute");
    }

    @Override
    public BaseCloudRegister getRegister() {
        return new FusionComputeRegister();
    }

    private static String[] files = {
            "add_form_en-US.json",
            "add_form_zh-CN.json"
    };

    public void loadAccountForm() {
        try {
            for (String file : files) {
                InputStream is = getClass().getClassLoader().getResourceAsStream("account/" + file);
                String text = IoUtil.readUtf8(is);
                JSONObject json = JSON.parseObject(text);
                String lang = extractLocaleFromFilename(file);
                Locale locale = Locale.forLanguageTag(lang);
                AccountService.accountForm.put(locale, json);
            }
        } catch (Exception e) {
            log.error("加载插件账号表单信息失败!", e);
        }
    }

    public void loadAccountDispatch() {
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream("account/add_dispatch.json");
            String text = IoUtil.readUtf8(is);
            String[] split = key.split(":");
            //替换参数插件作用域
            text = StrUtil.replace(text, "#{plugin_realm}", split[0]);
            //替换参数插件版本
            text = StrUtil.replace(text, "#{plugin_version}", split[1]);
            AccountService.accountDispatch = text;
        } catch (Exception e) {
            log.error("加载"+key+"插件账号调度任务信息失败!", e);
        }
    }

    public static String extractLocaleFromFilename(String filename) {
        // 假设文件名格式为 "add_form_<locale>.json"
        // 找到最后一个 "_" 的位置
        int lastUnderscoreIndex = filename.lastIndexOf('_');
        if (lastUnderscoreIndex == -1 || !filename.endsWith(".json")) {
            throw new IllegalArgumentException("Invalid filename format: " + filename);
        }
        // 提取下划线之后、".json"之前的部分
        return filename.substring(lastUnderscoreIndex + 1, filename.length() - 5);
    }

    @Override
    public boolean onBeforeProcess(BaseCloudRequest request, JSONObject context) {
        log.info("云插件服务[" + key + "]收到请求信息:" + request.toJSONString());
        //以下两个将在v4.0.2后废弃,请勿使用,替换为AUTH_PLATFORM_ACCOUNT,GET_PLATFORM_ACCOUNT_ADD_FORM
        if (ActionType.AUTH_CLOUD_ACCOUNT == request.getAction()) {
            request.setAction(ActionType.AUTH_PLATFORM_ACCOUNT);
            if (request.containsKey("auth")) {
                if (request.getBody().getAccess() == null) {
                    request.getBody().put("auth",request.getJSONObject("auth"));
                }
            }
        } else if (ActionType.QUERY_CLOUD_ACCOUNT_MODEL == request.getAction()) {
            request.setAction(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM);
        }
        if(ActionType.AUTH_PLATFORM_ACCOUNT != request.getAction() && ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM != request.getAction()){
            request.getBody().loadAccess();
            CloudAccessBean accessBean = request.getBody().getAccess();
//            accessBean.setServerIp("***********");
//            accessBean.setServerPort("7443");
//            accessBean.setUsername("gesysman");
//            accessBean.setPassword("GeEnginE@123");
//            accessBean.setCmpId("c37d121c93dbfb0bacb790c90ff4b8db");
//            accessBean.setVersion("8.0");
            LoginResponse loginResponse = HttpClientUtil.getToken(accessBean);
            request.getBody().put("authToken",loginResponse);
        }
        List<FTBefore<BaseCloudRequest>> ftBefore = register.getBefore(request.getAction());
        if (null == ftBefore) return true;
        for (FTBefore<BaseCloudRequest> before : ftBefore) {
            if (!before.before(request)) {
                return false;
            }
        }
        return true;
    }
}
