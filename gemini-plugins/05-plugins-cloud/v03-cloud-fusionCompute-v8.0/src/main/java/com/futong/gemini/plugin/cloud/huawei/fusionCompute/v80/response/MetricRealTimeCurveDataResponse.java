package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class MetricRealTimeCurveDataResponse {

    /**
     * Items的个数。
     */
    private Integer total;

    /**
     * 预留。
     */
    private String result;

    /**
     * 指标返回值列表。
     */
    private List<CurveData> items;

    @Data
    public static class CurveData {
        /**
         * 对象标识。
         */
        private String urn;

        /**
         * 对象实例名称。
         */
        private String objectName;

        /**
         * 指标值。
         */
        private List<MetricValue> value;
    }

    @Data
    public static class MetricValue {
        /**
         * 性能指标名。
         */
        private String metricId;

        /**
         * 指标数据（如果是小指标则为一个json字符串）。
         */
        private String metricValue;
    }
}
