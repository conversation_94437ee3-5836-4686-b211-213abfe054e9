package com.futong.gemini.plugin.cloud.huawei.fusionCompute.v80.response;

import lombok.Data;

import java.util.List;

@Data
public class SecurityGroupRuleResponse {

    private Integer total;

    private List<SecurityGroupRuleResponseData> securityGroupRules;

    @Data
    public static class SecurityGroupRuleResponseData {
        private String direction;
        private String fromPort;
        private String ipProtocol;
        private String ipRanges;
        private String ipVersion;
        private String rulesId;
        private String securityGroupId;
        private String toPort;
    }
}
