{"data": [{"required": true, "unique": true, "dispatcher_info": {"jobName": "同步集群信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0 2 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步 Nutanix 集群信息", "jobInfo": "{\"action\": \"FetchPlatformCluster\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步主机信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 10 2 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步 Nutanix 主机信息", "jobInfo": "{\"action\": \"FetchComputeHost\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "同步虚拟机实例-${cluster.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/10 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步虚拟机实例信息", "jobInfo": "{\"action\": \"FetchComputeInstance\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"clusterUuid\": \"${cluster.value}\"}}}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "同步磁盘信息-${cluster.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 4 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步磁盘信息", "jobInfo": "{\"action\": \"FetchStorageDisk\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"clusterUuid\": \"${cluster.value}\"}}}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "同步快照信息-${cluster.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 30 4 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步快照信息", "jobInfo": "{\"action\": \"FetchStorageSnapshot\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"clusterUuid\": \"${cluster.value}\"}}}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "同步网络信息-${cluster.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0 5 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步网络信息", "jobInfo": "{\"action\": \"FetchNeutronSwitch\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"clusterUuid\": \"${cluster.value}\"}}}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步存储容器信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 15 5 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步存储容器信息", "jobInfo": "{\"action\": \"FetchStorageContainer\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步存储池信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 30 5 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步存储池信息", "jobInfo": "{\"action\": \"FetchStoragePool\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}, {"required": false, "unique": false, "dispatcher_info": {"jobName": "同步虚拟机性能数据-${cluster.label}", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/30 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步虚拟机性能数据", "jobInfo": "{\"action\": \"FetchComputeInstancePerf\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {\"clusterUuid\": \"${cluster.value}\"}}}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步子网信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 35 5 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步子网信息", "jobInfo": "{\"action\": \"FetchNeutronSubnet\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步告警信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/15 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步告警信息", "jobInfo": "{\"action\": \"FetchPlatformAlarm\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步事件信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "低", "triggerType": "cron", "triggerTime": "0 0 6 * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步事件信息", "jobInfo": "{\"action\": \"FetchPlatformEvent\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步任务信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "中", "triggerType": "cron", "triggerTime": "0 0/10 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步任务信息", "jobInfo": "{\"action\": \"FetchPlatformTask\", \"body\": {\"auth\": {\"cmpId\": \"${cmpId}\"}, \"cloud\": {}}}"}}]}