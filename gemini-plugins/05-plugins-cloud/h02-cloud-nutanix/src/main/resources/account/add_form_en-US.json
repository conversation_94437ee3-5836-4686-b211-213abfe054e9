{"description": "Initial account management only supports main account management", "model": [{"type": "main", "name": "Nutanix Hyperconverged Platform Main Account", "description": "Nutanix hyperconverged platform main account, available for cloud resource acquisition!", "form": [{"field": "cloudAccount", "label": "Cloud Account", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter cloud account name"}, {"field": "username", "label": "Username", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "Please enter Nutanix cluster username"}, {"field": "password", "label": "Password", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "Please enter Nutanix cluster password"}, {"field": "serverIp", "label": "Server Address", "type": "input", "value": "", "required": true, "isUpdate": false, "tips": "Please enter Nutanix cluster IP address or domain name"}, {"field": "serverPort", "label": "Server Port", "type": "input", "value": "9440", "required": false, "isUpdate": true, "tips": "Default Nutanix port is 9440"}, {"field": "protocol", "label": "Protocol", "type": "select", "value": "https", "required": false, "isUpdate": true, "options": [{"value": "https", "label": "HTTPS"}, {"value": "http", "label": "HTTP"}], "tips": "Connection protocol, HTTPS is recommended"}]}]}