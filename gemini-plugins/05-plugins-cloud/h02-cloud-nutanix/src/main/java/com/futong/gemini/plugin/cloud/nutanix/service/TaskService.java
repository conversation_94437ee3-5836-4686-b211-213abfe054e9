package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 任务管理服务类
 * 负责任务信息的查询和状态跟踪
 *
 * 主要功能：
 * - 实现任务查询 (Prism Element v2.0 /tasks/list 和 Prism Central v3 /tasks)
 * - 支持任务状态跟踪
 * - 支持任务进度监控
 * - 支持异步操作状态查询
 *
 * API 端点:
 * - Prism Element v2.0: /PrismGateway/services/rest/v2.0/tasks/list
 * - Prism Central v3: /api/nutanix/v3/tasks
 *
 * 参考华为云插件实现，遵循 Gemini 云插件开发规范
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class TaskService {

    /**
     * 查询任务详情
     * 根据任务 UUID 获取任务的详细信息和状态
     */
    public static BaseDataResponse<JSONObject> getTaskDetail(BaseCloudRequest request) {
        try {
            String taskUuid = request.getBody().getCloud().getString("taskUuid");
            if (taskUuid == null || taskUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "任务 UUID 不能为空");
            }

            log.info("开始查询任务详情，任务 UUID: {}", taskUuid);

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 调用 Nutanix API 获取任务详情
            JSONObject result = client.doGetDataJSON("/tasks/" + taskUuid);

            log.info("成功获取任务详情，任务状态: {}", result.getString("status"));

            return new BaseDataResponse<>(result);

        } catch (Exception e) {
            log.error("查询任务详情失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询任务详情失败"), e);
        }
    }

    /**
     * 等待任务完成
     * 轮询任务状态直到完成或超时
     */
    public static BaseDataResponse<JSONObject> waitForTaskCompletion(BaseCloudRequest request) {
        try {
            String taskUuid = request.getBody().getCloud().getString("taskUuid");
            int timeoutSeconds = request.getBody().getCloud().getInteger("timeoutSeconds") != null ?
                request.getBody().getCloud().getInteger("timeoutSeconds") : 300; // 默认5分钟超时
            int pollIntervalSeconds = request.getBody().getCloud().getInteger("pollIntervalSeconds") != null ?
                request.getBody().getCloud().getInteger("pollIntervalSeconds") : 5; // 默认5秒轮询间隔

            if (taskUuid == null || taskUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "任务 UUID 不能为空");
            }

            log.info("开始等待任务完成，任务 UUID: {}, 超时时间: {}秒", taskUuid, timeoutSeconds);

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            long startTime = System.currentTimeMillis();
            long timeoutMillis = timeoutSeconds * 1000L;

            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                JSONObject taskDetail = client.doGetDataJSON("/tasks/" + taskUuid);
                String status = taskDetail.getString("status");

                log.debug("任务状态检查，UUID: {}, 状态: {}", taskUuid, status);

                if ("SUCCEEDED".equals(status)) {
                    log.info("任务执行成功，UUID: {}", taskUuid);
                    return new BaseDataResponse<>(taskDetail);
                } else if ("FAILED".equals(status)) {
                    log.error("任务执行失败，UUID: {}", taskUuid);
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("任务执行失败"));
                }

                // 等待下次轮询
                try {
                    Thread.sleep(pollIntervalSeconds * 1000L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("任务等待被中断"), e);
                }
            }

            // 超时
            log.warn("任务等待超时，UUID: {}", taskUuid);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("任务等待超时"));

        } catch (Exception e) {
            log.error("等待任务完成失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("等待任务完成失败"), e);
        }
    }

    /**
     * 取消任务
     * 取消正在执行的任务
     */
    public static BaseResponse cancelTask(BaseCloudRequest request) {
        try {
            String taskUuid = request.getBody().getCloud().getString("taskUuid");
            if (taskUuid == null || taskUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "任务 UUID 不能为空");
            }

            log.info("开始取消任务，任务 UUID: {}", taskUuid);

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 构建取消请求
            JSONObject cancelSpec = new JSONObject();
            cancelSpec.put("action", "CANCEL");

            // 调用 Nutanix API 取消任务
            JSONObject result = client.doPostDataJSON("/tasks/" + taskUuid + "/action", cancelSpec);

            log.info("任务取消请求已提交，UUID: {}", taskUuid);

            return BaseResponse.SUCCESS.of("任务取消请求已提交");

        } catch (Exception e) {
            log.error("取消任务失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("取消任务失败"), e);
        }
    }

}
