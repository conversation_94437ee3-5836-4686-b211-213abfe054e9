package com.futong.gemini.plugin.cloud.nutanix.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.model.BaseResponse;
import com.futong.common.model.BaseDataResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.sniffgourd.sdk.proxy.GourdProxy;
import com.futong.gemini.plugin.cloud.sdk.common.GourdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Nutanix 基础服务类
 * 参考华为云插件 BaseService 实现
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class BaseService {

    /**
     * 账号表单配置缓存
     */
    public static Map<Locale, JSONObject> accountForm = new HashMap<>();

    /**
     * 账号调度配置
     */
    public static String accountDispatch;

    /**
     * 获取账号添加表单
     */
    public static BaseResponse getAccountAddForm(BaseCloudRequest request) {
        return new BaseDataResponse<>(accountForm.get(LocaleContextHolder.getLocale()));
    }
    
    /**
     * 创建获取调度任务
     * #{内部参数},${页面参数}
     */
    public static BaseResponse createFetchDispatch(BaseCloudRequest request) {
        GourdProxy gourdProxy = SpringUtil.getBean(GourdProxy.class);

        //调用gourd服务-添加调度层级，新加云类型层级
        GourdUtils.addGourdLevel(request.getPlugin().getRealm(), null, "云调度-Nutanix");
        //调用gourd服务-添加调度层级，新加调度层级id=cmpId
        GourdUtils.addGourdLevel(request);

        //删除旧的调度任务
        JSONObject oldWhere = new JSONObject();
        oldWhere.put("jobLevel", request.getBody().getAccess().getCmpId());
        BaseResponse response = gourdProxy.stopAndDeleteDispatcher(oldWhere);
        if (BaseResponse.SUCCESS.equals(response)) {
            log.info("删除旧的调度任务成功!");
        } else {
            log.error("删除旧的调度任务失败{}!", JSON.toJSONString(response));
            return BaseResponse.ERROR_BIZ.of("删除旧的调度任务失败");
        }

        //替换占位符${cmpId}=access.cmpId
        JSONObject result = JSON.parseObject(StrUtil.replace(accountDispatch, "${cmpId}", request.getBody().getAccess().getCmpId()));
        //根据集群生成全量调度任务
        NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
        JSONObject clustersResponse = client.doPostDataJSON("/clusters/list", new JSONObject());
        List<JSONObject> dispatchers = listAllDispatcher(result, clustersResponse);
        //调用gourd服务-批量添加调度任务
        return gourdProxy.createDispatchers(dispatchers);
    }

    public static List<JSONObject> listAllDispatcher(JSONObject model, JSONObject clustersResponse) {
        List<JSONObject> dispatchers = new ArrayList<>();
        model.getJSONArray("data").forEach(item -> {
            JSONObject itemObj = (JSONObject) item;
            if (itemObj.getBoolean("unique")) {
                dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
            } else {
                String itemStr = itemObj.getJSONObject("dispatcher_info").toString();
                // 如果有集群信息，为每个集群创建调度任务
                if (clustersResponse != null && clustersResponse.containsKey("entities")) {
                    JSONArray clusters = clustersResponse.getJSONArray("entities");
                    for (Object clusterObject : clusters) {
                        JSONObject cluster = (JSONObject) clusterObject;
                        String itemStrCluster = StrUtil.replace(itemStr, "${cluster.label}", cluster.getJSONObject("spec").getString("name"));
                        itemStrCluster = StrUtil.replace(itemStrCluster, "${cluster.value}", cluster.getJSONObject("metadata").getString("uuid"));
                        dispatchers.add(JSON.parseObject(itemStrCluster));
                    }
                } else {
                    // 如果没有集群信息，直接添加调度任务
                    dispatchers.add(itemObj.getJSONObject("dispatcher_info"));
                }
            }
        });
        return dispatchers;
    }
}
