package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.nutanix.sampler.Convert;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Nutanix 刷新服务类
 * 负责刷新各种资源的状态信息
 *
 * 主要功能：
 * - refreshEcs() 方法 - 刷新虚拟机状态
 * - 配置刷新任务和重试机制
 *
 * 依赖关系: ComputeInstanceService
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class RefreshService {

    /**
     * 刷新虚拟机实例状态
     * 配置刷新任务和重试机制
     * 批量更新虚拟机的最新状态信息
     */
    public static BaseResponse refreshVirtualMachines(BaseCloudRequest request) {
        try {
            log.info("开始刷新 Nutanix 虚拟机实例状态");

            // 获取需要刷新的实例ID列表
            JSONObject refreshConfig = request.getJSONObject("refreshConfig");
            if (refreshConfig == null || !refreshConfig.containsKey("data")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少刷新配置信息");
            }

            Object instanceIdsObj = refreshConfig.get("data");
            List<String> instanceIds = new ArrayList<>();

            // 处理不同格式的实例ID数据
            if (instanceIdsObj instanceof List) {
                for (Object id : (List<?>) instanceIdsObj) {
                    instanceIds.add(String.valueOf(id));
                }
            } else if (instanceIdsObj instanceof String) {
                instanceIds.add((String) instanceIdsObj);
            } else {
                return BaseResponse.FAIL_PARAM_EMPTY.of("实例ID格式不正确");
            }

            log.debug("需要刷新的虚拟机实例: {}", instanceIds);

            // 获取 Nutanix API 客户端
            NutanixClient apiClient = ClientUtils.client(NutanixClient.class, request.getBody());

            List<Object> refreshedInstances = new ArrayList<>();
            int successCount = 0;
            int failureCount = 0;

            // 批量刷新虚拟机状态（支持重试机制）
            for (String vmUuid : instanceIds) {
                int retryCount = 0;
                int maxRetries = 3; // 最大重试次数
                boolean success = false;

                while (retryCount < maxRetries && !success) {
                    try {
                        // 调用 Nutanix API 获取虚拟机最新状态
                        JSONObject vmResponse = apiClient.doGetDataJSON("/vms/" + vmUuid);

                        if (vmResponse != null) {
                            // 注意：RefreshService 中暂时保留简单的数据处理
                            // 完整的 CI 模型转换应在 FetchService 中进行
                            Map<String, Object> vmData = new HashMap<>();
                            vmData.put("uuid", vmResponse.getString("uuid"));
                            vmData.put("name", vmResponse.getJSONObject("spec").getString("name"));
                            refreshedInstances.add(vmData);
                            successCount++;
                            success = true;
                            log.debug("虚拟机 {} 状态刷新成功", vmUuid);
                        } else {
                            throw new RuntimeException("获取虚拟机状态返回空数据");
                        }

                    } catch (Exception e) {
                        retryCount++;
                        log.warn("刷新虚拟机 {} 状态失败，重试次数: {}/{}, 错误: {}",
                                vmUuid, retryCount, maxRetries, e.getMessage());

                        if (retryCount >= maxRetries) {
                            failureCount++;
                            log.error("虚拟机 {} 状态刷新最终失败", vmUuid);
                        } else {
                            // 重试前等待一段时间
                            try {
                                Thread.sleep(1000 * retryCount); // 递增等待时间
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        }
                    }
                }
            }

            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("totalCount", instanceIds.size());
            result.put("successCount", successCount);
            result.put("failureCount", failureCount);
            result.put("refreshedInstances", refreshedInstances);
            result.put("timestamp", System.currentTimeMillis());

            log.info("Nutanix 虚拟机实例状态刷新完成，成功: {}, 失败: {}", successCount, failureCount);
            return CloudService.buildSuccessResponse(result, "虚拟机实例状态刷新完成");
            
        } catch (Exception e) {
            log.error("刷新 Nutanix 虚拟机实例状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "刷新虚拟机实例状态失败");
        }
    }

    /**
     * 刷新镜像状态
     */
    public static BaseResponse refreshImage(BaseCloudRequest request) {
        try {
            log.info("开始刷新 Nutanix 镜像状态");
            
            JSONObject refreshConfig = request.getJSONObject("refreshConfig");
            if (refreshConfig == null || !refreshConfig.containsKey("data")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少刷新配置信息");
            }
            
            Object imageIds = refreshConfig.get("data");
            log.debug("需要刷新的镜像: {}", imageIds);
            
            // TODO: 调用 Nutanix API 获取指定镜像的最新状态
            // ImageClient imageClient = CloudClient.client.client(ImageClient.class, request.getBody());
            
            JSONObject result = new JSONObject();
            result.put("refreshedCount", imageIds instanceof java.util.List ? 
                ((java.util.List<?>) imageIds).size() : 1);
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("Nutanix 镜像状态刷新完成");
            return CloudService.buildSuccessResponse(result, "镜像状态刷新完成");
            
        } catch (Exception e) {
            log.error("刷新 Nutanix 镜像状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "刷新镜像状态失败");
        }
    }

    /**
     * 刷新磁盘状态
     */
    public static BaseResponse refreshDisk(BaseCloudRequest request) {
        try {
            log.info("开始刷新 Nutanix 磁盘状态");
            
            JSONObject refreshConfig = request.getJSONObject("refreshConfig");
            if (refreshConfig == null || !refreshConfig.containsKey("data")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少刷新配置信息");
            }
            
            Object diskIds = refreshConfig.get("data");
            log.debug("需要刷新的磁盘: {}", diskIds);
            
            // TODO: 调用 Nutanix API 获取指定磁盘的最新状态
            // DiskClient diskClient = CloudClient.client.client(DiskClient.class, request.getBody());
            
            JSONObject result = new JSONObject();
            result.put("refreshedCount", diskIds instanceof java.util.List ? 
                ((java.util.List<?>) diskIds).size() : 1);
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("Nutanix 磁盘状态刷新完成");
            return CloudService.buildSuccessResponse(result, "磁盘状态刷新完成");
            
        } catch (Exception e) {
            log.error("刷新 Nutanix 磁盘状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "刷新磁盘状态失败");
        }
    }

    /**
     * 刷新快照状态
     */
    public static BaseResponse refreshSnapshot(BaseCloudRequest request) {
        try {
            log.info("开始刷新 Nutanix 快照状态");
            
            JSONObject refreshConfig = request.getJSONObject("refreshConfig");
            if (refreshConfig == null || !refreshConfig.containsKey("data")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少刷新配置信息");
            }
            
            Object snapshotIds = refreshConfig.get("data");
            log.debug("需要刷新的快照: {}", snapshotIds);
            
            // TODO: 调用 Nutanix API 获取指定快照的最新状态
            // SnapshotClient snapshotClient = CloudClient.client.client(SnapshotClient.class, request.getBody());
            
            JSONObject result = new JSONObject();
            result.put("refreshedCount", snapshotIds instanceof java.util.List ? 
                ((java.util.List<?>) snapshotIds).size() : 1);
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("Nutanix 快照状态刷新完成");
            return CloudService.buildSuccessResponse(result, "快照状态刷新完成");
            
        } catch (Exception e) {
            log.error("刷新 Nutanix 快照状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "刷新快照状态失败");
        }
    }

    /**
     * 刷新网络状态
     */
    public static BaseResponse refreshVpc(BaseCloudRequest request) {
        try {
            log.info("开始刷新 Nutanix 网络状态");
            
            JSONObject refreshConfig = request.getJSONObject("refreshConfig");
            if (refreshConfig == null || !refreshConfig.containsKey("data")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少刷新配置信息");
            }
            
            Object networkIds = refreshConfig.get("data");
            log.debug("需要刷新的网络: {}", networkIds);
            
            // TODO: 调用 Nutanix API 获取指定网络的最新状态
            // NetworkClient networkClient = CloudClient.client.client(NetworkClient.class, request.getBody());
            
            JSONObject result = new JSONObject();
            result.put("refreshedCount", networkIds instanceof java.util.List ? 
                ((java.util.List<?>) networkIds).size() : 1);
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("Nutanix 网络状态刷新完成");
            return CloudService.buildSuccessResponse(result, "网络状态刷新完成");
            
        } catch (Exception e) {
            log.error("刷新 Nutanix 网络状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "刷新网络状态失败");
        }
    }

    /**
     * 通用刷新方法
     */
    public static BaseResponse refreshResource(BaseCloudRequest request, String resourceType) {
        try {
            log.info("开始刷新 Nutanix {} 状态", resourceType);
            
            switch (resourceType.toLowerCase()) {
                case "vm":
                case "instance":
                    return refreshVirtualMachines(request);
                case "image":
                    return refreshImage(request);
                case "disk":
                    return refreshDisk(request);
                case "snapshot":
                    return refreshSnapshot(request);
                case "network":
                case "vpc":
                    return refreshVpc(request);
                default:
                    log.warn("不支持的资源类型: {}", resourceType);
                    return BaseResponse.FAIL_PARAM.of("不支持的资源类型: " + resourceType);
            }
            
        } catch (Exception e) {
            log.error("刷新 Nutanix {} 状态失败", resourceType, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "刷新资源状态失败");
        }
    }

    /**
     * 批量刷新多种资源
     */
    public static BaseResponse refreshMultipleResources(BaseCloudRequest request) {
        try {
            log.info("开始批量刷新 Nutanix 资源状态");
            
            JSONObject cloud = request.getBody().getCloud();
            if (!cloud.containsKey("resourceTypes")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少资源类型参数");
            }
            
            String[] resourceTypes = cloud.getString("resourceTypes").split(",");
            JSONObject results = new JSONObject();
            
            for (String resourceType : resourceTypes) {
                try {
                    BaseResponse result = refreshResource(request, resourceType.trim());
                    if (result instanceof BaseDataResponse) {
                        results.put(resourceType.trim(), ((BaseDataResponse<?>) result).getData());
                    } else {
                        results.put(resourceType.trim(), result.getMessage());
                    }
                } catch (Exception e) {
                    log.error("刷新资源类型 {} 失败", resourceType, e);
                    results.put(resourceType.trim(), "FAILED: " + e.getMessage());
                }
            }
            
            results.put("timestamp", System.currentTimeMillis());
            
            log.info("Nutanix 批量资源状态刷新完成");
            return CloudService.buildSuccessResponse(results, "批量资源状态刷新完成");
            
        } catch (Exception e) {
            log.error("批量刷新 Nutanix 资源状态失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "批量刷新资源状态失败");
        }
    }
}
