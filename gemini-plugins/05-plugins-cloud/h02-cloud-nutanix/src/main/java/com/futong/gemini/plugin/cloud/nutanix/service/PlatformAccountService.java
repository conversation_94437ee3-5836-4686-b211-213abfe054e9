package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 平台账号服务类
 * 参考华为云插件 PlatformAccountService 实现
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class PlatformAccountService {
    
    /**
     * 认证账号
     * 通过获取集群信息来验证账号是否有效
     */
    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 尝试获取集群信息来验证认证
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "cluster");
            requestBody.put("length", 1); // 只获取一个集群信息用于验证
            
            JSONObject result = client.doPostDataJSON("/clusters/list", requestBody);
            
            if (result != null && result.containsKey("entities")) {
                log.info("Nutanix 账号认证成功，用户: {}", request.getBody().getAccess().getUsername());
                return BaseResponse.SUCCESS.of("账号认证成功");
            } else {
                log.error("Nutanix 账号认证失败，无法获取集群信息");
                return BaseResponse.FAIL_OP_CLOUD.of("账号认证失败，无法获取集群信息");
            }
            
        } catch (BaseException e) {
            log.error("Nutanix 账号认证失败", e);
            return BaseResponse.FAIL_OP_CLOUD.of("账号认证失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Nutanix 账号认证异常", e);
            return BaseResponse.ERROR_SYS.of("账号认证异常: " + e.getMessage());
        }
    }
}
