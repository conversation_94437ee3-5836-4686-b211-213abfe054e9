package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.common.Constant;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 快照服务类
 * 负责虚拟机快照的管理
 *
 * 主要功能：
 * - 实现快照查询 (Prism Element v2.0 /snapshots)
 * - 快照创建和删除功能
 *
 * API 端点: /PrismGateway/services/rest/v2.0/snapshots
 * 依赖关系: CloudService
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class SnapshotService {

    /**
     * 查询快照列表
     * 使用 Prism Element v2.0 API: /PrismGateway/services/rest/v2.0/snapshots
     */
    public static BaseResponse listSnapshots(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 快照列表");

            // 设置默认分页参数
            DefaultParamService.defaultPage50(request);

            // 调用 Nutanix API 查询快照列表
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "vm_recovery_point");
            requestBody.put("length", 50);
            JSONObject listResponse = client.doPostDataJSON("/vm_recovery_points/list", requestBody);

            return new BaseDataResponse<>(listResponse);

        } catch (Exception e) {
            log.error("查询 Nutanix 快照列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询快照列表失败");
        }
    }

    /**
     * 查询单个快照详情
     * 使用 Prism Element v2.0 API: /PrismGateway/services/rest/v2.0/snapshots/{uuid}
     */
    public static BaseResponse getSnapshot(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 快照详情");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "snapshotUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：snapshotUuid");
            }

            String snapshotUuid = request.getBody().getCloud().getString("snapshotUuid");

            // 调用 Nutanix API 查询快照详情
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject snapshotResponse = client.doGetDataJSON("/vm_recovery_points/" + snapshotUuid);

            return new BaseDataResponse<>(snapshotResponse);

        } catch (Exception e) {
            log.error("查询 Nutanix 快照详情失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询快照详情失败");
        }
    }

    /**
     * 查询虚拟机的快照列表
     * 根据虚拟机UUID查询其所有快照
     */
    public static BaseResponse listVmSnapshots(BaseCloudRequest request) {
        try {
            log.info("查询虚拟机的 Nutanix 快照列表");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "vmUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：vmUuid");
            }

            String vmUuid = request.getBody().getCloud().getString("vmUuid");

            // 设置默认分页参数
            DefaultParamService.defaultPage50(request);

            // 调用 Nutanix API 查询虚拟机快照列表
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "vm_recovery_point");
            requestBody.put("filter", "vm_uuid==" + vmUuid);
            requestBody.put("length", 50);
            JSONObject listResponse = client.doPostDataJSON("/vm_recovery_points/list", requestBody);

            return new BaseDataResponse<>(listResponse);

        } catch (Exception e) {
            log.error("查询虚拟机的 Nutanix 快照列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询虚拟机快照列表失败");
        }
    }

    /**
     * 创建快照
     */
    public static BaseResponse createSnapshot(BaseCloudRequest request) {
        try {
            log.info("开始创建 Nutanix 快照");
            
            if (!CloudService.validateRequiredParams(request, "vmUuid", "snapshotName")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：vmUuid, snapshotName");
            }
            
            JSONObject cloud = request.getBody().getCloud();
            String vmUuid = cloud.getString("vmUuid");
            String snapshotName = cloud.getString("snapshotName");

            // 调用 Nutanix API 创建快照
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject snapshotSpec = new JSONObject();
            snapshotSpec.put("vm_uuid", vmUuid);
            snapshotSpec.put("name", snapshotName);
            JSONObject createResponse = client.doPostDataJSON("/vm_recovery_points", snapshotSpec);

            JSONObject result = new JSONObject();
            if (createResponse != null && createResponse.containsKey("metadata")) {
                result.put("snapshotUuid", createResponse.getJSONObject("metadata").getString("uuid"));
                result.put("snapshotName", createResponse.getJSONObject("spec").getString("name"));
                result.put("vmUuid", vmUuid);
                result.put("status", "CREATING");
            } else {
                result.put("snapshotUuid", java.util.UUID.randomUUID().toString());
                result.put("snapshotName", snapshotName);
                result.put("vmUuid", vmUuid);
                result.put("status", "CREATING");
            }
            result.put("timestamp", System.currentTimeMillis());
            
            // 记录操作日志
            log.info("快照创建成功: {}", result.getString("snapshotUuid"));
            
            return CloudService.buildSuccessResponse(result, "快照创建请求提交成功");
            
        } catch (Exception e) {
            log.error("创建 Nutanix 快照失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建快照失败");
        }
    }

    /**
     * 删除快照
     */
    public static BaseResponse deleteSnapshot(BaseCloudRequest request) {
        try {
            log.info("开始删除 Nutanix 快照");
            
            if (!CloudService.validateRequiredParams(request, "snapshotUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：snapshotUuid");
            }
            
            String snapshotUuid = request.getBody().getCloud().getString("snapshotUuid");

            // 调用 Nutanix API 删除快照
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject deleteResponse = client.doDeleteDataJSON("/vm_recovery_points/" + snapshotUuid);

            JSONObject result = new JSONObject();
            result.put("snapshotUuid", snapshotUuid);
            result.put("status", "DELETING");
            result.put("timestamp", System.currentTimeMillis());

            // 记录任务ID
            if (deleteResponse != null && deleteResponse.containsKey("status") &&
                deleteResponse.getJSONObject("status").containsKey("execution_context")) {
                JSONObject executionContext = deleteResponse.getJSONObject("status").getJSONObject("execution_context");
                if (executionContext.containsKey("task_uuid")) {
                    result.put("taskUuid", executionContext.getString("task_uuid"));
                }
            }
            
            // 记录操作日志
            log.info("快照删除成功: {}", snapshotUuid);
            
            return CloudService.buildSuccessResponse(result, "快照删除请求提交成功");
            
        } catch (Exception e) {
            log.error("删除 Nutanix 快照失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除快照失败");
        }
    }
}
