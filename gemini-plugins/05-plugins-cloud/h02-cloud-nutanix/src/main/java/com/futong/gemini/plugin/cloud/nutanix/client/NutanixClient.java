package com.futong.gemini.plugin.cloud.nutanix.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * Nutanix REST API 客户端
 * 参考华为云插件 HuaweiClient 实现
 * 使用 Apache HttpClient 实现与 Nutanix REST API v3 的交互
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class NutanixClient {

    private final ConnectionConfig config;
    private final CloseableHttpClient httpClient;
    private final String baseUrl;
    private final String authHeader;

    public NutanixClient(ConnectionConfig config) {
        this.config = config;
        this.httpClient = createHttpClient(config);

        // 构建基础 URL
        this.baseUrl = String.format("%s://%s:%s/api/nutanix/v3",
                config.getProtocol(), config.getHost(), config.getPort());

        // 构建认证头
        String credentials = config.getAuthConfig().getUsername() + ":" + config.getAuthConfig().getPassword();
        this.authHeader = "Basic " + Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));

        log.info("Nutanix 客户端初始化完成，服务地址: {}", baseUrl);
    }

    /**
     * 执行 GET 请求
     */
    public JSONObject doGetDataJSON(String path) {
        return doGetDataJSON(path, new JSONObject());
    }

    /**
     * 执行 GET 请求
     */
    public JSONObject doGetDataJSON(String path, JSONObject params) {
        try {
            String url = baseUrl + path;
            log.debug("执行 GET 请求: {}", url);

            HttpGet request = new HttpGet(url);
            request.setHeader("Authorization", authHeader);
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            HttpResponse response = httpClient.execute(request);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            int statusCode = response.getStatusLine().getStatusCode();
            log.debug("GET 请求响应状态码: {}", statusCode);

            if (statusCode >= 200 && statusCode < 300) {
                return JSON.parseObject(responseBody);
            } else {
                log.error("GET 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("GET 请求失败: " + statusCode));
            }

        } catch (IOException e) {
            log.error("执行 GET 请求异常: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("GET 请求异常"), e);
        }
    }



    /**
     * 执行 POST 请求
     */
    public JSONObject doPostDataJSON(String path, JSONObject requestBody) {
        try {
            String url = baseUrl + path;
            log.debug("执行 POST 请求: {}", url);
            log.debug("请求体: {}", requestBody.toJSONString());

            HttpPost request = new HttpPost(url);
            request.setHeader("Authorization", authHeader);
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            if (requestBody != null) {
                StringEntity entity = new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8);
                request.setEntity(entity);
            }

            HttpResponse response = httpClient.execute(request);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            int statusCode = response.getStatusLine().getStatusCode();
            log.debug("POST 请求响应状态码: {}", statusCode);

            if (statusCode >= 200 && statusCode < 300) {
                return JSON.parseObject(responseBody);
            } else {
                log.error("POST 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("POST 请求失败: " + statusCode));
            }

        } catch (IOException e) {
            log.error("执行 POST 请求异常: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("POST 请求异常"), e);
        }
    }

    /**
     * 执行 PUT 请求
     */
    public JSONObject doPutDataJSON(String path, JSONObject requestBody) {
        try {
            String url = baseUrl + path;
            log.debug("执行 PUT 请求: {}", url);
            log.debug("请求体: {}", requestBody.toJSONString());

            HttpPut request = new HttpPut(url);
            request.setHeader("Authorization", authHeader);
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            if (requestBody != null) {
                StringEntity entity = new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8);
                request.setEntity(entity);
            }

            HttpResponse response = httpClient.execute(request);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            int statusCode = response.getStatusLine().getStatusCode();
            log.debug("PUT 请求响应状态码: {}", statusCode);

            if (statusCode >= 200 && statusCode < 300) {
                return JSON.parseObject(responseBody);
            } else {
                log.error("PUT 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("PUT 请求失败: " + statusCode));
            }

        } catch (IOException e) {
            log.error("执行 PUT 请求异常: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("PUT 请求异常"), e);
        }
    }

    /**
     * 执行 DELETE 请求
     */
    public JSONObject doDeleteDataJSON(String path) {
        try {
            String url = baseUrl + path;
            log.debug("执行 DELETE 请求: {}", url);

            HttpDelete request = new HttpDelete(url);
            request.setHeader("Authorization", authHeader);
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            HttpResponse response = httpClient.execute(request);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            int statusCode = response.getStatusLine().getStatusCode();
            log.debug("DELETE 请求响应状态码: {}", statusCode);

            if (statusCode >= 200 && statusCode < 300) {
                if (responseBody != null && !responseBody.trim().isEmpty()) {
                    return JSON.parseObject(responseBody);
                } else {
                    // DELETE 请求可能返回空响应
                    JSONObject result = new JSONObject();
                    result.put("success", true);
                    return result;
                }
            } else {
                log.error("DELETE 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("DELETE 请求失败: " + statusCode));
            }

        } catch (IOException e) {
            log.error("执行 DELETE 请求异常: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("DELETE 请求异常"), e);
        }
    }

    /**
     * 执行 DELETE 请求
     */
    public JSONObject doDeleteDataJSONWithFullPath(String fullPath) {
        try {
            // 使用协议、主机和端口构建基础 URL，不包含 API 版本路径
            String baseUrlWithoutApiPath = config.getProtocol() + "://" + config.getHost() + ":" + config.getPort();
            String url = baseUrlWithoutApiPath + fullPath;
            log.debug("执行 DELETE 请求: {}", url);

            HttpDelete request = new HttpDelete(url);
            request.setHeader("Authorization", authHeader);
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            HttpResponse response = httpClient.execute(request);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            int statusCode = response.getStatusLine().getStatusCode();
            log.debug("DELETE 请求响应状态码: {}", statusCode);

            if (statusCode >= 200 && statusCode < 300) {
                if (responseBody != null && !responseBody.trim().isEmpty()) {
                    return JSON.parseObject(responseBody);
                } else {
                    // DELETE 请求可能返回空响应
                    JSONObject result = new JSONObject();
                    result.put("success", true);
                    return result;
                }
            } else {
                log.error("DELETE 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("DELETE 请求失败: " + statusCode));
            }

        } catch (IOException e) {
            log.error("执行 DELETE 请求异常: {}", e.getMessage(), e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("DELETE 请求异常"), e);
        }
    }

    /**
     * 测试连接
     */
    public boolean testConnection() {
        try {
            log.info("测试 Nutanix 连接...");

            // 尝试获取集群信息来测试连接
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "cluster");
            requestBody.put("length", 1);

            JSONObject response = doPostDataJSON("/clusters/list", requestBody);

            if (response != null) {
                log.info("Nutanix 连接测试成功");
                return true;
            } else {
                log.error("Nutanix 连接测试失败：响应为空");
                return false;
            }

        } catch (Exception e) {
            log.error("Nutanix 连接测试失败", e);
            return false;
        }
    }

    /**
     * 关闭客户端
     */
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            log.error("关闭 Nutanix 客户端异常", e);
        }
    }

    /**
     * 获取配置信息
     */
    public ConnectionConfig getConfig() {
        return config;
    }

    /**
     * 获取基础 URL
     */
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * 创建支持忽略 SSL 证书验证的 HttpClient
     * Nutanix 集群通常使用自签名证书，需要忽略证书验证
     */
    private CloseableHttpClient createHttpClient(ConnectionConfig config) {
        try {
            // 创建信任所有证书的 SSL 上下文
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial((X509Certificate[] chain, String authType) -> true)
                    .build();

            // 创建 SSL 连接工厂，忽略主机名验证
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    NoopHostnameVerifier.INSTANCE
            );

            // 创建 HttpClient
            return HttpClients.custom()
                    .setSSLSocketFactory(sslSocketFactory)
                    .setDefaultRequestConfig(config.getRequestConfig())
                    .build();

        } catch (Exception e) {
            log.error("创建 HttpClient 失败，使用默认配置", e);
            // 如果创建失败，使用默认配置
            return HttpClients.custom()
                    .setDefaultRequestConfig(config.getRequestConfig())
                    .build();
        }
    }

    /**
     * 带重试机制的 POST 请求
     * 针对 429 (Too Many Requests) 和 503 (Service Unavailable) 错误进行重试
     */
    public JSONObject doPostDataJSONWithRetry(String path, JSONObject requestBody) {
        return doPostDataJSONWithRetry(path, requestBody, 3, 1000);
    }

    /**
     * 带重试机制的 POST 请求
     *
     * @param path 请求路径
     * @param requestBody 请求体
     * @param maxRetries 最大重试次数
     * @param retryDelayMs 重试延迟（毫秒）
     * @return 响应 JSON 对象
     */
    public JSONObject doPostDataJSONWithRetry(String path, JSONObject requestBody, int maxRetries, long retryDelayMs) {
        Exception lastException = null;

        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return doPostDataJSON(path, requestBody);
            } catch (BaseException e) {
                lastException = e;

                // 检查是否是可重试的错误
                if (isRetryableError(e) && attempt < maxRetries) {
                    log.warn("请求失败，第 {} 次重试，路径: {}, 错误: {}", attempt + 1, path, e.getMessage());

                    try {
                        Thread.sleep(retryDelayMs * (attempt + 1)); // 指数退避
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("请求被中断"), ie);
                    }
                } else {
                    // 不可重试的错误或达到最大重试次数
                    break;
                }
            }
        }

        // 所有重试都失败了
        log.error("请求最终失败，已重试 {} 次，路径: {}", maxRetries, path);
        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("请求失败，已达到最大重试次数"), lastException);
    }

    /**
     * 带重试机制的 GET 请求
     */
    public JSONObject doGetDataJSONWithRetry(String path) {
        return doGetDataJSONWithRetry(path, 3, 1000);
    }

    /**
     * 带重试机制的 GET 请求
     */
    public JSONObject doGetDataJSONWithRetry(String path, int maxRetries, long retryDelayMs) {
        Exception lastException = null;

        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return doGetDataJSON(path);
            } catch (BaseException e) {
                lastException = e;

                if (isRetryableError(e) && attempt < maxRetries) {
                    log.warn("GET 请求失败，第 {} 次重试，路径: {}, 错误: {}", attempt + 1, path, e.getMessage());

                    try {
                        Thread.sleep(retryDelayMs * (attempt + 1));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("请求被中断"), ie);
                    }
                } else {
                    break;
                }
            }
        }

        log.error("GET 请求最终失败，已重试 {} 次，路径: {}", maxRetries, path);
        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("GET 请求失败，已达到最大重试次数"), lastException);
    }

    /**
     * 判断是否是可重试的错误
     */
    private boolean isRetryableError(BaseException e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        // 检查 HTTP 状态码
        return message.contains("429") ||  // Too Many Requests
               message.contains("503") ||  // Service Unavailable
               message.contains("502") ||  // Bad Gateway
               message.contains("504") ||  // Gateway Timeout
               message.contains("timeout") || // 超时错误
               message.contains("connection reset") || // 连接重置
               message.contains("connection refused"); // 连接拒绝
    }

    /**
     * 查询 Nutanix 平台版本信息
     *
     * @return 版本信息 JSON 对象
     */
    public JSONObject getVersionInfo() {
        try {
            log.debug("查询 Nutanix 平台版本信息");
            return doGetDataJSON("/version");
        } catch (Exception e) {
            log.warn("无法获取版本信息，可能是旧版本 API", e);
            return null;
        }
    }

    /**
     * 查询 Nutanix 平台能力信息
     *
     * @return 能力信息 JSON 对象
     */
    public JSONObject getCapabilities() {
        try {
            log.debug("查询 Nutanix 平台能力信息");
            return doGetDataJSON("/capabilities");
        } catch (Exception e) {
            log.warn("无法获取能力信息，可能是旧版本 API", e);
            return null;
        }
    }

    /**
     * 检查指定 API 端点是否可用
     *
     * @param endpoint API 端点路径
     * @return 是否可用
     */
    public boolean isEndpointAvailable(String endpoint) {
        try {
            log.debug("检查 API 端点可用性: {}", endpoint);
            JSONObject requestBody = new JSONObject();
            requestBody.put("length", 1); // 只请求一条记录来测试

            doPostDataJSON(endpoint, requestBody);
            return true;
        } catch (Exception e) {
            log.debug("API 端点不可用: {}, 错误: {}", endpoint, e.getMessage());
            return false;
        }
    }

    /**
     * 自动检测最佳 API 版本
     *
     * @return API 版本信息
     */
    public String detectBestApiVersion() {
        // 首先尝试获取版本信息
        JSONObject versionInfo = getVersionInfo();
        if (versionInfo != null) {
            log.info("成功获取版本信息: {}", versionInfo.toJSONString());

            // 解析版本信息
            if (versionInfo.containsKey("version")) {
                String version = versionInfo.getString("version");
                log.info("检测到 Nutanix 版本: {}", version);
                return "v3"; // 如果能获取到版本信息，说明支持 v3 API
            }
        }

        // 如果无法获取版本信息，尝试测试不同的 API 端点
        String[] testEndpoints = {
            "/clusters/list",  // v3 API 测试
            "/vms/list",       // v3 API 测试
            "/subnets/list"    // v3 API 测试
        };

        for (String endpoint : testEndpoints) {
            if (isEndpointAvailable(endpoint)) {
                log.info("检测到支持的 v3 API 端点: {}", endpoint);
                return "v3";
            }
        }

        log.warn("无法检测到支持的 v3 API，将使用 v2.0 API 作为后备");
        return "v2.0";
    }

    /**
     * 使用完整路径执行 GET 请求
     * 用于调用不同版本的 API（如 v2.0 API）
     *
     * @param fullPath 完整的 API 路径，例如 "/PrismGateway/services/rest/v2.0/virtual_disks"
     * @return 响应 JSON 对象
     */
    public JSONObject doGetDataJSONWithFullPath(String fullPath) {
        try {
            // 使用协议、主机和端口构建基础 URL，不包含 API 版本路径
            String baseUrlWithoutApiPath = config.getProtocol() + "://" + config.getHost() + ":" + config.getPort();
            String url = baseUrlWithoutApiPath + fullPath;
            log.debug("执行 GET 请求（完整路径）: {}", url);

            HttpGet request = new HttpGet(url);

            // 设置请求头
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            // 设置认证
            String auth = config.getAuthConfig().getUsername() + ":" + config.getAuthConfig().getPassword();
            String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
            request.setHeader("Authorization", "Basic " + encodedAuth);

            // 执行请求
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            log.debug("GET 请求响应状态码: {}", statusCode);
            log.debug("GET 请求响应内容: {}", responseBody);

            if (statusCode >= 200 && statusCode < 300) {
                return JSONObject.parseObject(responseBody);
            } else {
                log.error("GET 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("GET 请求失败: " + statusCode));
            }

        } catch (BaseException e) {
            // 重新抛出 BaseException
            throw e;
        } catch (Exception e) {
            log.error("GET 请求异常", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("GET 请求异常"), e);
        }
    }

    /**
     * 使用完整路径执行 GET 请求
     * 用于调用不同版本的 API（如 v2.0 API）
     *
     * @param fullPath 完整的 API 路径，例如 "/PrismGateway/services/rest/v2.0/virtual_disks"
     * @return 响应 JSON 对象
     */
    public String doGetDataJSONStringWithFullPath(String fullPath) {
        try {
            // 使用协议、主机和端口构建基础 URL，不包含 API 版本路径
            String baseUrlWithoutApiPath = config.getProtocol() + "://" + config.getHost() + ":" + config.getPort();
            String url = baseUrlWithoutApiPath + fullPath;
            log.debug("执行 GET 请求（完整路径）: {}", url);

            HttpGet request = new HttpGet(url);

            // 设置请求头
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            // 设置认证
            String auth = config.getAuthConfig().getUsername() + ":" + config.getAuthConfig().getPassword();
            String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
            request.setHeader("Authorization", "Basic " + encodedAuth);

            // 执行请求
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            log.debug("GET 请求响应状态码: {}", statusCode);
            log.debug("GET 请求响应内容: {}", responseBody);

            if (statusCode >= 200 && statusCode < 300) {
                return responseBody;
            } else {
                log.error("GET 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("GET 请求失败: " + statusCode));
            }

        } catch (BaseException e) {
            // 重新抛出 BaseException
            throw e;
        } catch (Exception e) {
            log.error("GET 请求异常", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("GET 请求异常"), e);
        }
    }

    /**
     * 使用完整路径执行 POST 请求
     * 用于调用不同版本的 API（如 v2.0 API）
     *
     * @param fullPath 完整的 API 路径，例如 "/PrismGateway/services/rest/v2.0/tasks/list"
     * @param requestBody 请求体
     * @return 响应 JSON 对象
     */
    public JSONObject doPostDataJSONWithFullPath(String fullPath, JSONObject requestBody) {
        try {
            // 使用协议、主机和端口构建基础 URL，不包含 API 版本路径
            String baseUrlWithoutApiPath = config.getProtocol() + "://" + config.getHost() + ":" + config.getPort();
            String url = baseUrlWithoutApiPath + fullPath;
            log.debug("执行 POST 请求（完整路径）: {}", url);
            log.debug("请求体: {}", requestBody.toJSONString());

            HttpPost request = new HttpPost(url);

            // 设置请求头
            request.setHeader("Content-Type", "application/json");
            request.setHeader("Accept", "application/json");

            // 设置认证
            String auth = config.getAuthConfig().getUsername() + ":" + config.getAuthConfig().getPassword();
            String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
            request.setHeader("Authorization", "Basic " + encodedAuth);

            // 设置请求体
            StringEntity entity = new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8);
            request.setEntity(entity);

            // 执行请求
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            log.debug("POST 请求响应状态码: {}", statusCode);
            log.debug("POST 请求响应内容: {}", responseBody);

            if (statusCode >= 200 && statusCode < 300) {
                return JSONObject.parseObject(responseBody);
            } else {
                log.error("POST 请求失败，状态码: {}, 响应: {}", statusCode, responseBody);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("POST 请求失败: " + statusCode));
            }

        } catch (BaseException e) {
            // 重新抛出 BaseException
            throw e;
        } catch (Exception e) {
            log.error("POST 请求异常", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("POST 请求异常"), e);
        }
    }
}
