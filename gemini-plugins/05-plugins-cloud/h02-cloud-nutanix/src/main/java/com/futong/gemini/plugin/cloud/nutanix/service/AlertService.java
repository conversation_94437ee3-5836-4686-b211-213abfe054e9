package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 告警服务类
 * 负责告警信息的查询和管理
 *
 * 主要功能：
 * - 实现告警查询 (Prism Element v2.0 /alerts)
 * - 支持按时间范围和严重性过滤
 * - 支持告警确认和解决
 *
 * API 端点: /PrismGateway/services/rest/v2.0/alerts
 * 依赖关系: CloudService
 *
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class AlertService extends BaseCloudService {

    // 注意：根据开发规范，查询方法无需写入，所有查询方法已移除
    // 告警查询应通过 FetchService 进行数据同步，或直接调用 API 客户端
    // 只保留操作方法（确认、解决等）

    /**
     * 确认告警
     * 将告警状态设置为已确认
     */
    public static BaseResponse acknowledgeAlert(BaseCloudRequest request) {
        try {
            log.info("确认 Nutanix 告警");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "alertUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：alertUuid");
            }
            
            String alertUuid = request.getBody().getCloud().getString("alertUuid");
            
            // 调用 Nutanix API 确认告警
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject acknowledgeResponse = client.doPutDataJSON("/alerts/" + alertUuid + "/acknowledge", new JSONObject());

            log.info("Nutanix 告警确认成功");
            return new BaseDataResponse<>(acknowledgeResponse);
            
        } catch (Exception e) {
            log.error("确认 Nutanix 告警失败", e);
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    /**
     * 解决告警
     * 将告警状态设置为已解决
     */
    public static BaseResponse resolveAlert(BaseCloudRequest request) {
        try {
            log.info("解决 Nutanix 告警");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "alertUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：alertUuid");
            }
            
            String alertUuid = request.getBody().getCloud().getString("alertUuid");
            
            // 调用 Nutanix API 解决告警
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject resolveResponse = client.doPutDataJSON("/alerts/" + alertUuid + "/resolve", new JSONObject());

            log.info("Nutanix 告警解决成功");
            return new BaseDataResponse<>(resolveResponse);
            
        } catch (Exception e) {
            log.error("解决 Nutanix 告警失败", e);
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    // 注意：统计和过滤类方法已移除，查询方法应保持轻量化
    // 复杂的业务逻辑应在前端或其他业务层处理
}
