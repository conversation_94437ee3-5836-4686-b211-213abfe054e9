package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.nutanix.sampler.Convert;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Nutanix 主机服务类
 * 负责物理主机的管理和监控
 * 
 * 主要功能：
 * - 实现主机列表查询 (Prism Element v2.0 /hosts)
 * - 实现主机性能数据查询 (Prism Element v1 /hosts)
 * 
 * API 端点: /PrismGateway/services/rest/v2.0/hosts
 * 依赖关系: CloudService
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class HostService {

    /**
     * 查询主机列表
     * 使用 Prism Element v2.0 API: /PrismGateway/services/rest/v2.0/hosts
     */
    public static BaseResponse listHosts(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 主机列表");
            
            // 设置默认分页参数
            DefaultParamService.defaultPage50(request);
            
            // 调用 Nutanix API 查询主机列表
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "host");
            requestBody.put("length", request.getBody().getCloud().getInteger("length"));
            JSONObject listResponse = client.doPostDataJSON("/hosts/list", requestBody);

            return new BaseDataResponse<>(listResponse);
            
        } catch (Exception e) {
            log.error("查询 Nutanix 主机列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询主机列表失败");
        }
    }

    /**
     * 查询单个主机详情
     * 使用 Prism Element v2.0 API: /PrismGateway/services/rest/v2.0/hosts/{uuid}
     */
    public static BaseResponse getHost(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 主机详情");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "hostUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：hostUuid");
            }
            
            String hostUuid = request.getBody().getCloud().getString("hostUuid");
            
            // 调用 Nutanix API 查询主机详情
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject hostResponse = client.doGetDataJSON("/hosts/" + hostUuid);

            return new BaseDataResponse<>(hostResponse);
            
        } catch (Exception e) {
            log.error("查询 Nutanix 主机详情失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询主机详情失败");
        }
    }

    /**
     * 查询主机性能数据
     * 使用 Prism Element v1 API: /PrismGateway/services/rest/v1/hosts
     */
    public static BaseResponse getHostPerformance(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 主机性能数据");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "hostUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：hostUuid");
            }
            
            String hostUuid = request.getBody().getCloud().getString("hostUuid");
            
            // 设置默认时间范围（24小时）
            DefaultParamService.defaultStartEndTimeOneDay(request);
            
            // 调用 Nutanix API 查询主机性能数据
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject perfResponse = client.doGetDataJSON("/hosts/" + hostUuid + "/stats");

            return new BaseDataResponse<>(perfResponse);
            
        } catch (Exception e) {
            log.error("查询 Nutanix 主机性能数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询主机性能数据失败");
        }
    }

    /**
     * 批量查询主机性能数据
     * 获取所有主机的性能统计信息
     */
    public static BaseResponse listHostsPerformance(BaseCloudRequest request) {
        try {
            log.info("批量查询 Nutanix 主机性能数据");
            
            // 设置默认时间范围（24小时）
            DefaultParamService.defaultStartEndTimeOneDay(request);
            
            // 首先获取主机列表
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "host");
            requestBody.put("length", 100);
            JSONObject hostListResponse = client.doPostDataJSON("/hosts/list", requestBody);
            
            List<Map<String, Object>> hostPerfData = new ArrayList<>();
            
            if (hostListResponse != null && hostListResponse.containsKey("entities")) {
                JSONArray entities = hostListResponse.getJSONArray("entities");
                
                for (int i = 0; i < entities.size(); i++) {
                    JSONObject host = entities.getJSONObject(i);
                    String hostUuid = host.getJSONObject("metadata").getString("uuid");
                    
                    try {
                        // 获取每个主机的性能数据
                        JSONObject perfResponse = client.doGetDataJSON("/hosts/" + hostUuid + "/stats");
                        
                        if (perfResponse != null) {
                            Map<String, Object> hostPerf = Convert.convertHostPerfData(host, perfResponse);
                            hostPerfData.add(hostPerf);
                        }
                    } catch (Exception e) {
                        log.warn("获取主机 {} 性能数据失败: {}", hostUuid, e.getMessage());
                        // 继续处理其他主机，不中断整个流程
                    }
                }
            }
            
            JSONObject result = new JSONObject();
            result.put("hostPerformanceData", hostPerfData);
            result.put("totalCount", hostPerfData.size());
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("Nutanix 主机性能数据查询完成，共 {} 台主机", hostPerfData.size());
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("批量查询 Nutanix 主机性能数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "批量查询主机性能数据失败");
        }
    }

    /**
     * 查询主机资源使用情况
     * 获取主机的 CPU、内存、存储使用统计
     */
    public static BaseResponse getHostResourceUsage(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 主机资源使用情况");
            
            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "hostUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：hostUuid");
            }
            
            String hostUuid = request.getBody().getCloud().getString("hostUuid");
            
            // 获取主机详情
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject hostResponse = client.doGetDataJSON("/hosts/" + hostUuid);

            // 获取主机性能数据
            JSONObject perfResponse = client.doGetDataJSON("/hosts/" + hostUuid + "/stats");
            
            // 合并资源使用情况
            JSONObject resourceUsage = Convert.convertHostResourceUsage(hostResponse, perfResponse);
            
            return new BaseDataResponse<>(resourceUsage);
            
        } catch (Exception e) {
            log.error("查询 Nutanix 主机资源使用情况失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询主机资源使用情况失败");
        }
    }
}
