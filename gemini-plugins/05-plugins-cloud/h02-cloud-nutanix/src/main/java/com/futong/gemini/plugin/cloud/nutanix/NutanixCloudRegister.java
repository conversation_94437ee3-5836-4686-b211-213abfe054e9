package com.futong.gemini.plugin.cloud.nutanix;
import com.futong.gemini.plugin.cloud.nutanix.sampler.FetchService;
import com.futong.gemini.plugin.cloud.nutanix.service.*;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 云插件注册类
 * 参考华为云插件 HarmonyRegister 实现
 * 负责注册所有的操作类型和对应的处理方法
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class NutanixCloudRegister extends BaseCloudRegister {

    @Override
    public void load() {
        log.info("开始加载 Nutanix 云插件操作注册");

        // 资源操作加载
        onAfterLoadPlatform();      // 加载云平台操作
        onAfterLoadFetch();         // 加载同步调度信息
        onAfterLoadQuery();         // 加载查询操作
        onAfterLoadCompute();       // 加载虚拟机操作
        onAfterLoadStorage();       // 加载存储操作
        onAfterLoadNetwork();       // 加载网络操作
        onAfterLoadHost();          // 加载主机操作
        onAfterLoadCluster();       // 加载集群操作

        log.info("Nutanix 云插件操作注册完成");
    }

    /**
     * 加载平台相关操作
     * 参考华为云插件 HarmonyRegister.onAfterLoadPlatform() 实现
     */
    public void onAfterLoadPlatform() {
        log.info("注册 Nutanix 平台操作");

        // 基础操作
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, BaseService::getAccountAddForm); // 获取云账号表单信息
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, BaseService::createFetchDispatch); // 添加默认调度任务

        // 平台账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, PlatformAccountService::authAccount); // 认证云账号

        log.info("Nutanix 平台操作注册完成");
    }

    /**
     * 加载数据同步相关操作
     * 参考华为云插件 HarmonyRegister.onAfterLoadFetch() 实现
     */
    public void onAfterLoadFetch() {
        log.info("注册 Nutanix 数据同步操作");

        // 基础资源同步
        register(ActionType.FETCH_COMPUTE_HOST, FetchService::fetchHost); // 同步主机
        register(ActionType.FETCH_PLATFORM_CLUSTER, FetchService::fetchCluster); // 同步集群

        // 虚拟机数据同步
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchVirtualMachines); // 同步虚拟机

//        registerBefore(FetchService::defaultVmBeforeBasicInfo,
//                ActionType.FETCH_COMPUTE_INSTANCE_PERF);

        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchVirtualMachinePerformance); // 同步虚拟机性能

        // 存储数据同步
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchDisk); // 同步虚拟磁盘  实际走的V3的同步磁盘
        register(ActionType.FETCH_STORAGE_RESOURCE, FetchService::fetchVirtualDisk); // 同步虚拟磁盘 没ActionType,暂用存储资源ActionType
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchSnapshot); // 同步快照
        register(ActionType.FETCH_STORAGE_CONTAINER, FetchService::fetchStorageContainer); // 同步存储容器
        register(ActionType.FETCH_STORAGE_POOL, FetchService::fetchStoragePool); // 同步存储池

        // 网络数据同步
        register(ActionType.FETCH_NEUTRON_SWITCH, FetchService::fetchNetwork); // 同步虚拟交换机
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet); // 同步子网

        // 监控数据同步
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlert); // 同步告警信息
        register(ActionType.FETCH_PLATFORM_EVENT, FetchService::fetchEvent); // 同步事件信息

        // 任务数据同步
        register(ActionType.FETCH_PLATFORM_TASK, FetchService::fetchTasks); // 同步任务信息

        log.info("Nutanix 数据同步操作注册完成");
    }

    /**
     * 加载查询相关操作
     * 注册各种资源的查询方法
     */
    public void onAfterLoadQuery() {
        log.info("注册 Nutanix 查询操作");

        // API 测试 - 使用账号认证方法作为 API 测试
        register(ActionType.API_TEST, PlatformAccountService::authAccount); // API 测试

        // 任务查询操作 - 暂时注释，等待 ActionType 枚举添加
        // register(ActionType.QUERY_PLATFORM_TASK_DETAIL, TaskService::getTaskDetail); // 查询任务详情
        // register(ActionType.QUERY_PLATFORM_TASK_STATUS, TaskService::waitForTaskCompletion); // 等待任务完成

        // 虚拟机查询操作
        // 注意：使用现有的 ActionType，如果没有对应的枚举值，可能需要使用通用的查询操作

        // 存储查询操作
        // 存储容器和存储池的查询操作暂时没有对应的标准 ActionType
        // 可以考虑在 FetchService 中实现，或者使用自定义操作类型

        // 网络查询操作
        // 网络查询操作暂时没有对应的标准 ActionType

        // 主机查询操作
        // 主机查询操作暂时没有对应的标准 ActionType

        // 集群查询操作
        // 集群查询操作暂时没有对应的标准 ActionType

        log.info("Nutanix 查询操作注册完成");
    }
    /**
     * 加载虚拟机相关操作
     */
    public void onAfterLoadCompute() {
        log.info("注册 Nutanix 虚拟机操作");

        // 虚拟机查询操作（使用现有的枚举值）
        // 注意：QUERY_COMPUTE_INSTANCE_INFO 和 QUERY_COMPUTE_INSTANCE_LIST 枚举值不存在，暂时注释
        register(ActionType.REFRESH_COMPUTE_INSTANCE, ComputeInstanceService::refreshEcs)
                //从刷新配置中获取实例ID;
                .addTransferCloud("$.refreshConfig.data", "$.openId", BaseUtils::formatSingle);
        // 虚拟机生命周期操作
        register(ActionType.CREATE_COMPUTE_INSTANCE, ComputeInstanceService::createInstance); // 创建虚拟机
        register(ActionType.DELETE_COMPUTE_INSTANCE, ComputeInstanceService::deleteInstance)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);// 删除虚拟机

        // 虚拟机电源操作
        register(ActionType.START_COMPUTE_INSTANCE, ComputeInstanceService::startInstance)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);// 启动虚拟机
        register(ActionType.STOP_COMPUTE_INSTANCE, ComputeInstanceService::stopInstance)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);/// 停止虚拟机
        register(ActionType.REBOOT_COMPUTE_INSTANCE, ComputeInstanceService::restartInstance)
                .addTransferCloud("$.cis.openId","$.openId")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 2000)
                .addSetRefreshSplitData("request","$.body.cloud.openId")
                .addAfter(BaseCloudService::addRefreshGourdJob);// 重启虚拟机

        // 集群管理操作 - 暂时注释，等待 ActionType 定义
        // register(ActionType.LIST_CLUSTER, ClusterService::listClusters); // 查询集群列表
        // register(ActionType.GET_CLUSTER_DETAIL, ClusterService::getClusterDetail); // 查询集群详情

        log.info("Nutanix 虚拟机操作注册完成");
    }

    /**
     * 加载存储相关操作
     */
    public void onAfterLoadStorage() {
        log.info("注册 Nutanix 存储操作");

        // 磁盘操作
        register(ActionType.CREATE_STORAGE_DISK, DiskService::createDisk); // 创建磁盘
        register(ActionType.DELETE_STORAGE_DISK, DiskService::deleteDisk); // 删除磁盘
        register(ActionType.ATTACH_STORAGE_DISK, DiskService::attachDisk); // 挂载磁盘
        register(ActionType.DETACH_STORAGE_DISK, DiskService::detachDisk); // 卸载磁盘

        // 快照操作
        register(ActionType.CREATE_STORAGE_SNAPSHOT, SnapshotService::createSnapshot); // 创建快照
        register(ActionType.DELETE_STORAGE_SNAPSHOT, SnapshotService::deleteSnapshot); // 删除快照

        // 存储容器和存储池查询操作
        // 注意：这些是查询操作，没有对应的标准 ActionType，可能需要使用自定义操作类型

        log.info("Nutanix 存储操作注册完成");
    }

    /**
     * 加载网络相关操作
     */
    public void onAfterLoadNetwork() {
        log.info("注册 Nutanix 网络操作");

        // 网络操作
        register(ActionType.CREATE_NEUTRON_VPC, NetworkService::createNetwork); // 创建网络
        register(ActionType.DELETE_NEUTRON_VPC, NetworkService::deleteNetwork); // 删除网络

        log.info("Nutanix 网络操作注册完成");
    }

    /**
     * 加载主机相关操作
     */
    public void onAfterLoadHost() {
        log.info("注册 Nutanix 主机操作");

        // 主机操作 - 暂时没有维护模式相关的方法实现
        // 可以在后续版本中添加维护模式相关的操作

        log.info("Nutanix 主机操作注册完成");
    }

    /**
     * 加载集群相关操作
     */
    public void onAfterLoadCluster() {
        log.info("注册 Nutanix 集群操作");

        // 集群查询操作 - 使用现有的 ActionType
        // 注意：集群的具体管理操作（如创建、删除）通常不在云插件中实现
        // 这些是基础设施级别的操作，由 Nutanix 自身管理

        log.info("Nutanix 集群操作注册完成");
    }





}
