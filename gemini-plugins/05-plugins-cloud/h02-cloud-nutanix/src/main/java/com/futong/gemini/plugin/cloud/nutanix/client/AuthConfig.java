package com.futong.gemini.plugin.cloud.nutanix.client;

import cn.hutool.core.util.StrUtil;

/**
 * Nutanix 认证配置类
 * 参考华为云插件 AuthConfig 实现
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class AuthConfig {
    
    private String username;
    private String password;
    
    private AuthConfig(Builder builder) {
        this.username = builder.username;
        this.password = builder.password;
    }
    
    public String getUsername() {
        return username;
    }
    
    public String getPassword() {
        return password;
    }
    
    /**
     * 认证配置构建器
     */
    public static class Builder {
        private String username;
        private String password;
        
        public Builder username(String username) {
            this.username = username;
            return this;
        }
        
        public Builder password(String password) {
            this.password = password;
            return this;
        }
        
        public AuthConfig build() {
            // 验证必需参数
            if (StrUtil.isEmpty(username)) {
                throw new IllegalArgumentException("用户名不能为空");
            }
            if (StrUtil.isEmpty(password)) {
                throw new IllegalArgumentException("密码不能为空");
            }
            
            return new AuthConfig(this);
        }
    }
    
    @Override
    public String toString() {
        return "AuthConfig{" +
                "username='" + username + '\'' +
                ", password='***'" + // 不显示密码
                '}';
    }
}
