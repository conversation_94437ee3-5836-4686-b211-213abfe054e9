package com.futong.gemini.plugin.cloud.nutanix.common;

/**
 * Nutanix 插件常量类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class Constant {
    
    /**
     * 云平台类型
     */
    public static final String CLOUD_TYPE = "nutanix";
    
    /**
     * 插件版本
     */
    public static final String PLUGIN_VERSION = "1.0.0";
    
    /**
     * API 相关常量
     */
    public static class Api {
        public static final String DEFAULT_API_VERSION = "v3";
        public static final String CONTENT_TYPE = "application/json";
        public static final String AUTHORIZATION_HEADER = "Authorization";
        public static final String BASIC_AUTH_PREFIX = "Basic ";
    }
    
    /**
     * 虚拟机相关常量
     */
    public static class VM {
        public static final String POWER_STATE_ON = "ON";
        public static final String POWER_STATE_OFF = "OFF";
        public static final String POWER_STATE_PAUSED = "PAUSED";
        
        public static final String VM_DISK_DEVICE_TYPE_DISK = "DISK";
        public static final String VM_DISK_DEVICE_TYPE_CDROM = "CDROM";
        
        public static final String BOOT_TYPE_LEGACY = "LEGACY";
        public static final String BOOT_TYPE_UEFI = "UEFI";
    }
    
    /**
     * 镜像相关常量
     */
    public static class Image {
        public static final String IMAGE_TYPE_DISK_IMAGE = "DISK_IMAGE";
        public static final String IMAGE_TYPE_ISO_IMAGE = "ISO_IMAGE";
        
        public static final String IMAGE_STATE_ACTIVE = "ACTIVE";
        public static final String IMAGE_STATE_INACTIVE = "INACTIVE";
    }
    
    /**
     * 磁盘相关常量
     */
    public static class Disk {
        public static final String DEVICE_TYPE_DISK = "DISK";
        public static final String DEVICE_TYPE_CDROM = "CDROM";
        
        public static final String STORAGE_CONTAINER_TYPE = "STORAGE_CONTAINER";
        public static final String VOLUME_GROUP_TYPE = "VOLUME_GROUP";
    }
    
    /**
     * 网络相关常量
     */
    public static class Network {
        public static final String NETWORK_TYPE_VLAN = "VLAN";
        public static final String NETWORK_TYPE_OVERLAY = "OVERLAY";
        
        public static final String IP_CONFIG_TYPE_DHCP = "DHCP";
        public static final String IP_CONFIG_TYPE_STATIC = "STATIC";
    }
    
    /**
     * 集群相关常量
     */
    public static class Cluster {
        public static final String CLUSTER_STATE_NORMAL = "NORMAL";
        public static final String CLUSTER_STATE_MAINTENANCE = "MAINTENANCE";
    }
    
    /**
     * 任务相关常量
     */
    public static class Task {
        public static final String TASK_STATUS_QUEUED = "QUEUED";
        public static final String TASK_STATUS_RUNNING = "RUNNING";
        public static final String TASK_STATUS_SUCCEEDED = "SUCCEEDED";
        public static final String TASK_STATUS_FAILED = "FAILED";
        public static final String TASK_STATUS_ABORTED = "ABORTED";
    }
    
    /**
     * 错误码相关常量
     */
    public static class ErrorCode {
        public static final String AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED";
        public static final String AUTHORIZATION_FAILED = "AUTHORIZATION_FAILED";
        public static final String RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND";
        public static final String INVALID_REQUEST = "INVALID_REQUEST";
        public static final String INTERNAL_ERROR = "INTERNAL_ERROR";
    }
    
    /**
     * 默认配置常量
     */
    public static class Default {
        public static final int DEFAULT_PAGE_SIZE = 50;
        public static final int MAX_PAGE_SIZE = 500;
        public static final int DEFAULT_TIMEOUT = 30000;
        public static final int DEFAULT_RETRY_COUNT = 3;
        public static final String DEFAULT_CLUSTER = "default"; // Nutanix 使用集群概念
    }
    
    /**
     * 资源类型常量
     */
    public static class ResourceType {
        public static final String VM = "vm";
        public static final String IMAGE = "image";
        public static final String DISK = "disk";
        public static final String NETWORK = "network";
        public static final String CLUSTER = "cluster";
        public static final String HOST = "host";
        public static final String STORAGE_CONTAINER = "storage_container";
        public static final String VOLUME_GROUP = "volume_group";
        public static final String SNAPSHOT = "snapshot";
    }
    
    /**
     * 操作类型常量
     */
    public static class Operation {
        public static final String CREATE = "CREATE";
        public static final String UPDATE = "UPDATE";
        public static final String DELETE = "DELETE";
        public static final String START = "START";
        public static final String STOP = "STOP";
        public static final String RESTART = "RESTART";
        public static final String PAUSE = "PAUSE";
        public static final String RESUME = "RESUME";
        public static final String CLONE = "CLONE";
        public static final String SNAPSHOT = "SNAPSHOT";
    }
    
    /**
     * 监控指标常量
     */
    public static class Metrics {
        public static final String CPU_USAGE = "hypervisor_cpu_usage_ppm";
        public static final String MEMORY_USAGE = "hypervisor_memory_usage_ppm";
        public static final String STORAGE_USAGE = "storage.usage_bytes";
        public static final String NETWORK_RECEIVED = "hypervisor_num_received_bytes";
        public static final String NETWORK_TRANSMITTED = "hypervisor_num_transmitted_bytes";
    }
}
