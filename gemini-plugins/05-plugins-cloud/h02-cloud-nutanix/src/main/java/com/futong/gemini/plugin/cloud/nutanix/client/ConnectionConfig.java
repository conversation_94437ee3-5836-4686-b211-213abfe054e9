package com.futong.gemini.plugin.cloud.nutanix.client;

import cn.hutool.core.util.StrUtil;
import org.apache.http.client.config.RequestConfig;

/**
 * Nutanix 连接配置类
 * 参考华为云插件 ConnectionConfig 实现
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ConnectionConfig {
    
    private String protocol;
    private String host;
    private String port;
    private AuthConfig authConfig;
    private String proxyHost;
    private Integer proxyPort;
    private RequestConfig requestConfig;
    
    private ConnectionConfig(Builder builder) {
        this.protocol = builder.protocol;
        this.host = builder.host;
        this.port = builder.port;
        this.authConfig = builder.authConfig;
        this.proxyHost = builder.proxyHost;
        this.proxyPort = builder.proxyPort;
        this.requestConfig = buildRequestConfig();
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public String getHost() {
        return host;
    }
    
    public String getPort() {
        return port;
    }
    
    public AuthConfig getAuthConfig() {
        return authConfig;
    }
    
    public String getProxyHost() {
        return proxyHost;
    }
    
    public Integer getProxyPort() {
        return proxyPort;
    }
    
    public RequestConfig getRequestConfig() {
        return requestConfig;
    }
    
    private RequestConfig buildRequestConfig() {
        RequestConfig.Builder configBuilder = RequestConfig.custom()
                .setConnectTimeout(30000)  // 连接超时 30 秒
                .setSocketTimeout(60000)   // 读取超时 60 秒
                .setConnectionRequestTimeout(10000); // 从连接池获取连接超时 10 秒
        
        // 如果配置了代理，设置代理
        if (StrUtil.isNotEmpty(proxyHost) && proxyPort != null) {
            // 这里可以添加代理配置
            // configBuilder.setProxy(new HttpHost(proxyHost, proxyPort));
        }
        
        return configBuilder.build();
    }
    
    /**
     * 连接配置构建器
     */
    public static class Builder {
        private String protocol = "https"; // 默认使用 HTTPS
        private String host;
        private String port = "9440"; // Nutanix 默认端口
        private AuthConfig authConfig;
        private String proxyHost;
        private Integer proxyPort;
        
        public Builder protocol(String protocol) {
            if (StrUtil.isNotEmpty(protocol)) {
                this.protocol = protocol;
            }
            return this;
        }
        
        public Builder host(String host) {
            this.host = host;
            return this;
        }
        
        public Builder port(String port) {
            if (StrUtil.isNotEmpty(port)) {
                this.port = port;
            }
            return this;
        }
        
        public Builder authConfig(AuthConfig authConfig) {
            this.authConfig = authConfig;
            return this;
        }
        
        public Builder proxyHost(String proxyHost) {
            this.proxyHost = proxyHost;
            return this;
        }
        
        public Builder proxyPort(Integer proxyPort) {
            this.proxyPort = proxyPort;
            return this;
        }
        
        public ConnectionConfig build() {
            // 验证必需参数
            if (StrUtil.isEmpty(host)) {
                throw new IllegalArgumentException("主机地址不能为空");
            }
            if (authConfig == null) {
                throw new IllegalArgumentException("认证配置不能为空");
            }
            
            return new ConnectionConfig(this);
        }
    }
    
    @Override
    public String toString() {
        return "ConnectionConfig{" +
                "protocol='" + protocol + '\'' +
                ", host='" + host + '\'' +
                ", port='" + port + '\'' +
                ", authConfig=" + authConfig +
                ", proxyHost='" + proxyHost + '\'' +
                ", proxyPort=" + proxyPort +
                '}';
    }
}
