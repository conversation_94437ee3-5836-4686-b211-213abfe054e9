package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.function.FTAction;
import com.futong.common.function.FTExecute;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.common.Constant;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 云服务基础类
 * 提供通用的云服务操作方法
 *
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class CloudService {

    /**
     * 转换为 FTAction
     * 参考阿里云插件的 toFTAction 实现
     */
    public static <Q, R, C> FTAction<BaseCloudRequest> toFTAction(FTExecute<C, Q, R> exec) {
        return (BaseCloudRequest body) -> doAction(body, exec);
    }

    /**
     * 执行云操作
     * 参考阿里云插件的 doAction 实现
     */
    public static <Q, R, C> BaseDataResponse<R> doAction(BaseCloudRequest request, FTExecute<C, Q, R> exec) {
        try {
            // 注意：CloudClient.execute 方法已废弃，需要使用 ClientUtils.client 方式
            // 这里暂时抛出异常，提示需要重新实现
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "doAction 方法需要重新实现，请使用具体的服务类");
        } catch (Exception e) {
            String message = request.getAction().cname() + "失败!" + e.getMessage();
            log.error(message, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, message);
        }
    }



    /**
     * 验证必需参数
     */
    public static boolean validateRequiredParams(BaseCloudRequest request, String... paramNames) {
        for (String paramName : paramNames) {
            if (!request.getBody().getCloud().containsKey(paramName) || 
                request.getBody().getCloud().getString(paramName) == null ||
                request.getBody().getCloud().getString(paramName).trim().isEmpty()) {
                log.error("缺少必需参数: {}", paramName);
                return false;
            }
        }
        return true;
    }





    /**
     * 构建错误响应
     */
    public static BaseResponse buildErrorResponse(String message, Exception e) {
        log.error(message, e);
        return BaseResponse.ERROR_BIZ.of(message + ": " + e.getMessage());
    }

    /**
     * 构建成功响应
     */
    public static BaseResponse buildSuccessResponse(Object data) {
        return new BaseDataResponse<>(data);
    }

    /**
     * 构建成功响应（带消息）
     */
    public static BaseResponse buildSuccessResponse(Object data, String message) {
        BaseDataResponse<Object> response = new BaseDataResponse<>(data);
        response.setMessage(message);
        return response;
    }
}
