package com.futong.gemini.plugin.cloud.nutanix.service;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.nxc.entity.CmdbInstanceRes;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.nutanix.sampler.Convert;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.GourdJobResponse;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
/**
 * Nutanix 虚拟机实例服务类
 * 负责虚拟机的生命周期管理，包括创建、删除、启动、停止、重启等操作
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class ComputeInstanceService {

    /**
     * 创建虚拟机实例
     * 在 Nutanix 环境中创建新的虚拟机
     */
    public static BaseDataResponse<JSONObject> createInstance(BaseCloudRequest request) {
        try {
            log.info("开始创建 Nutanix 虚拟机实例");

            JSONObject cloud = request.getBody().getCloud();

            // 验证必需参数
            if (!cloud.containsKey("vmName") || cloud.getString("vmName").trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "虚拟机名称不能为空");
            }

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 构建虚拟机创建规格
            JSONObject vmSpec = buildVmCreateSpec(cloud);

            // 调用 Nutanix API 创建虚拟机
            JSONObject result = client.doPostDataJSON("/vms", vmSpec);

            if (result != null && result.containsKey("metadata")) {
                String vmUuid = result.getJSONObject("metadata").getString("uuid");
                log.info("虚拟机创建成功，UUID: {}", vmUuid);

                JSONObject response = new JSONObject();
                response.put("vmUuid", vmUuid);
                response.put("vmName", cloud.getString("vmName"));
                response.put("status", "CREATING");

                return new BaseDataResponse<>(response);
            } else {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "虚拟机创建失败，响应异常");
            }

        } catch (Exception e) {
            log.error("创建虚拟机实例失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建虚拟机实例失败"), e);
        }
    }

    /**
     * 删除虚拟机实例
     * 从 Nutanix 环境中删除指定的虚拟机
     */
    public static BaseDataResponse<JSONObject> deleteInstance(BaseCloudRequest request) {
        try {
//            String vmUuid = request.getBody().getCloud().getString("vmUuid");
            List<String> vmUuids = request.getBody().getCloud().getObject("openId", List.class);
            if (CollUtil.isEmpty(vmUuids)) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "虚拟机 UUID 不能为空");
            }
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            log.info("开始删除虚拟机实例，UUID: {}", vmUuids);
            for (String vmUuid : vmUuids) {
                // 调用 Nutanix API 删除虚拟机
//                JSONObject result = client.doDeleteDataJSON("/vms/" + vmUuid);
                JSONObject result = client.doDeleteDataJSONWithFullPath("/PrismGateway/services/rest/v2.0/vms/" + vmUuid );
                log.info("虚拟机删除成功，UUID: {}", vmUuid);

            }

            JSONObject response = new JSONObject();
            response.put("vmUuid", vmUuids);
            response.put("status", "DELETING");

            return new BaseDataResponse<>(response);

        } catch (Exception e) {
            log.error("删除虚拟机实例失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除虚拟机实例失败"), e);
        }
    }

    /**
     * 启动虚拟机实例
     * 启动处于关闭状态的虚拟机
     */
    public static BaseDataResponse<JSONObject> startInstance(BaseCloudRequest request) {
        try {
//            String vmUuid = request.getBody().getCloud().getString("vmUuid");
            List<String> vmUuids = request.getBody().getCloud().getObject("openId", List.class);
            if (CollUtil.isEmpty(vmUuids)) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "虚拟机 UUID 不能为空");
            }
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            log.info("开始启动虚拟机实例，UUID: {}", vmUuids);
            for (String vmUuid : vmUuids) {
                // 构建电源操作请求
                JSONObject powerSpec = new JSONObject();
                powerSpec.put("transition", "ON");

                // 调用 Nutanix API 启动虚拟机
//                JSONObject result = client.doPostDataJSON("/vms/" + vmUuid + "/set_power_state", powerSpec);
                JSONObject result = client.doPostDataJSONWithFullPath("/PrismGateway/services/rest/v2.0/vms/" + vmUuid + "/set_power_state", powerSpec);
                log.info("虚拟机启动成功，UUID: {}", vmUuid);
            }





            JSONObject response = new JSONObject();
            response.put("vmUuid", vmUuids);
            response.put("status", "STARTING");

            return new BaseDataResponse<>(response);

        } catch (Exception e) {
            log.error("启动虚拟机实例失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("启动虚拟机实例失败"), e);
        }
    }

    /**
     * 停止虚拟机实例
     * 停止正在运行的虚拟机
     */
    public static BaseDataResponse<JSONObject> stopInstance(BaseCloudRequest request) {
        try {
//            String vmUuid = request.getBody().getCloud().getString("vmUuid");
            List<String> vmUuids = request.getBody().getCloud().getObject("openId", List.class);
            if (CollUtil.isEmpty(vmUuids)) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "虚拟机 UUID 不能为空");
            }
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            log.info("开始停止虚拟机实例，UUID: {}", vmUuids);
            for (String vmUuid : vmUuids) {
                // 构建电源操作请求
                JSONObject powerSpec = new JSONObject();
                powerSpec.put("transition", "OFF");

                // 调用 Nutanix API 停止虚拟机
//                JSONObject result = client.doPostDataJSON("/vms/" + vmUuid + "/acpi_power_state", powerSpec);
                JSONObject result = client.doPostDataJSONWithFullPath("/PrismGateway/services/rest/v2.0/vms/" + vmUuid + "/set_power_state", powerSpec);
                log.info("虚拟机停止成功，UUID: {}", vmUuid);
            }

            JSONObject response = new JSONObject();
            response.put("vmUuid", vmUuids);
            response.put("status", "STOPPING");

            return new BaseDataResponse<>(response);

        } catch (Exception e) {
            log.error("停止虚拟机实例失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("停止虚拟机实例失败"), e);
        }
    }

    /**
     * 重启虚拟机实例
     * 重启正在运行的虚拟机
     */
    public static BaseDataResponse<JSONObject> restartInstance(BaseCloudRequest request) {
        try {
//            String vmUuid = request.getBody().getCloud().getString("vmUuid");
            List<String> vmUuids = request.getBody().getCloud().getObject("openId", List.class);

            if (CollUtil.isEmpty(vmUuids)) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "虚拟机 UUID 不能为空");
            }
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            log.info("开始重启虚拟机实例，UUID: {}", vmUuids);
            for (String vmUuid : vmUuids) {
                // 构建电源操作请求
                JSONObject powerSpec = new JSONObject();
                powerSpec.put("transition", "RESET");

                // 调用 Nutanix API 重启虚拟机
//                JSONObject result = client.doPostDataJSON("/vms/" + vmUuid + "/acpi_power_state", powerSpec);
                JSONObject result = client.doPostDataJSONWithFullPath("/PrismGateway/services/rest/v2.0/vms/" + vmUuid + "/set_power_state", powerSpec);
                log.info("虚拟机重启成功，UUID: {}", vmUuid);
            }

            JSONObject response = new JSONObject();
            response.put("vmUuid", vmUuids);
            response.put("status", "RESTARTING");

            return new BaseDataResponse<>(response);

        } catch (Exception e) {
            log.error("重启虚拟机实例失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("重启虚拟机实例失败"), e);
        }
    }

    /**
     * 查询虚拟机实例详情
     * 获取指定虚拟机的详细信息
     */
    public static BaseDataResponse<JSONObject> getInstanceDetail(BaseCloudRequest request) {
        try {
            String vmUuid = request.getBody().getCloud().getString("vmUuid");
            if (vmUuid == null || vmUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "虚拟机 UUID 不能为空");
            }

            log.info("开始查询虚拟机详情，UUID: {}", vmUuid);

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 调用 Nutanix API 获取虚拟机详情
            JSONObject result = client.doGetDataJSON("/vms/" + vmUuid);

            log.info("成功获取虚拟机详情，名称: {}", result.getString("spec.name"));

            return new BaseDataResponse<>(result);

        } catch (Exception e) {
            log.error("查询虚拟机详情失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询虚拟机详情失败"), e);
        }
    }

    /**
     * 查询虚拟机实例列表
     * 获取所有虚拟机的列表信息
     */
    public static BaseDataResponse<JSONObject> listInstances(BaseCloudRequest request) {
        try {
            log.info("开始查询虚拟机实例列表");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "vm");

            // 设置分页参数
            if (request.getBody().getCloud().containsKey("length")) {
                requestBody.put("length", request.getBody().getCloud().getInteger("length"));
            } else {
                requestBody.put("length", 100);
            }

            if (request.getBody().getCloud().containsKey("offset")) {
                requestBody.put("offset", request.getBody().getCloud().getInteger("offset"));
            }

            // 调用 Nutanix API 获取虚拟机列表
            JSONObject result = client.doPostDataJSON("/vms/list", requestBody);

            log.info("成功获取虚拟机列表，共 {} 个虚拟机",
                result.containsKey("entities") ? result.getJSONArray("entities").size() : 0);

            return new BaseDataResponse<>(result);

        } catch (Exception e) {
            log.error("查询虚拟机列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询虚拟机列表失败"), e);
        }
    }

    /**
     * 构建虚拟机创建规格
     * 根据请求参数构建 Nutanix 虚拟机创建的规格
     */
    private static JSONObject buildVmCreateSpec(JSONObject cloud) {
        JSONObject vmSpec = new JSONObject();

        // 基本信息
        JSONObject spec = new JSONObject();
        spec.put("name", cloud.getString("vmName"));
        spec.put("description", cloud.getString("description"));

        // 资源配置
        JSONObject resources = new JSONObject();
        resources.put("power_state", "OFF"); // 创建后默认关机状态

        // CPU 配置
        if (cloud.containsKey("vcpus")) {
            resources.put("num_vcpus_per_socket", cloud.getInteger("vcpus"));
            resources.put("num_sockets", 1);
        }

        // 内存配置（单位：MiB）
        if (cloud.containsKey("memory")) {
            resources.put("memory_size_mib", cloud.getInteger("memory"));
        }

        spec.put("resources", resources);
        vmSpec.put("spec", spec);

        // 元数据
        JSONObject metadata = new JSONObject();
        metadata.put("kind", "vm");
        vmSpec.put("metadata", metadata);

        return vmSpec;
    }

    public static BaseResponse refreshEcs(BaseCloudRequest request) {
        Integer refreshCount = request.getBody().getGourd().getCount();
        Integer refreshMaxCount = 5;
        Integer refreshInterval = 5000;
        JSONObject refreshConfig = request.getBody().getJSONObject("refreshConfig");
        if (refreshConfig != null) {
            if (refreshConfig.containsKey("refreshMaxCount")) {
                refreshMaxCount = refreshConfig.getInteger("refreshMaxCount");
            }
            if (refreshConfig.containsKey("refreshInterval")) {
                refreshInterval = refreshConfig.getInteger("refreshInterval");
            }
        }
        NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.put("kind", "vm");
        requestBody.put("length", 500); // 获取最多500个虚拟机

        // 调用 API 获取虚拟机列表
        JSONObject response = client.doPostDataJSON("/vms/list", requestBody);
        Map<Class, List> result = Convert.convertVmData(request, response);

        List<CmdbInstanceRes> res = result.get(CmdbInstanceRes.class);
        if (CollUtil.isEmpty(res)) {
            //发送已删除
            String resId = IdUtils.encryptId(request.getBody().getAccess().getCmpId(),refreshConfig.getString("data"));
            CmdbInstanceRes cmdbInstanceRes = new CmdbInstanceRes();
            cmdbInstanceRes.setRes_id(resId);
            List<CmdbInstanceRes> data = CollUtil.newArrayList(cmdbInstanceRes);
            BaseCloudService.toRefreshMessageAndSend("delete", data, CmdbInstanceRes.class,request.getBody().getAccess().getCloudType(),request.getBody().getAccess().getCmpId());
        } else {
            //发送同步更新任务
            BaseCloudService.refreshUpdateSend(request, result);
            CmdbInstanceRes cmdbInstanceRes = res.get(0);
            if (INTERMEDIATE_ECS.contains(cmdbInstanceRes.getOpen_status())//状态为中间状态，则进行调度
                    && refreshCount > 0//刷新为0，则表示调度没有传递次数，则不进行调度
                    && refreshMaxCount > refreshCount//刷新次数小于最大刷新次数
            ) {
                JobInfo jobInfo = BaseCloudService.toRefreshJob(request, request.cloneJSONObject(), refreshInterval);
                return new GourdJobResponse(CollUtil.newArrayList(jobInfo));
            }
        }
        return BaseResponse.SUCCESS;
    }
    private final static Set<String> INTERMEDIATE_ECS = new HashSet<String>() {
        {
            add("starting");//开机中
            add("stoping");//关机中
            add("clone");//克隆中
            add("backup");//备份中
            add("recovery");//恢复中
            add("run_backup");//备份中
            add("resetting");//重启中
            add("suspending");//挂起中
            add("updating");//更新中
            add("deleting");//删除中
            add("snapshotting");//快照中
            add("creating");//创建中
            add("migrate");//迁移中
            add("deriving");//部署中
            add("tpl_updating");//更新中
        }
    };
}
