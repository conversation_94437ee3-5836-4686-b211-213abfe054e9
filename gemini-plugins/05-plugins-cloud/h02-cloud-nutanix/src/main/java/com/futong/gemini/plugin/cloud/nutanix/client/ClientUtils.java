package com.futong.gemini.plugin.cloud.nutanix.client;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 客户端工具类
 * 参考华为云插件 ClientUtils 实现
 * 负责创建和管理 Nutanix 客户端实例
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class ClientUtils {
    
    /**
     * 获取 Nutanix 客户端实例
     * 
     * @param clazz 客户端类型
     * @param body 请求体，包含认证信息
     * @param <C> 客户端类型
     * @return 客户端实例
     */
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        // 加载认证对象信息
        body.loadAccess();
        
        try {
            // 请求 Client 对象配置信息
            ConnectionConfig.Builder builder = new ConnectionConfig.Builder()
                    .protocol(body.getAccess().getProtocol())
                    .host(body.getAccess().getServerIp())
                    .port(body.getAccess().getServerPort())
                    .authConfig(new AuthConfig.Builder()
                            .username(body.getAccess().getUsername())
                            .password(body.getAccess().getPassword())
                            .build());
            
            // 代理配置暂时不支持，因为 CloudAccessBean 中没有相关方法
            // 如果需要代理支持，需要在 CloudAccessBean 中添加相应的字段和方法
            
            ConnectionConfig config = builder.build();
            
            // 创建并返回客户端实例
            if (clazz == NutanixClient.class) {
                return (C) new NutanixClient(config);
            } else {
                throw new BaseException(BaseResponse.ERROR_SYS, "不支持的客户端类型: " + clazz.getName());
            }

        } catch (Exception e) {
            throw new BaseException(BaseResponse.ERROR_SYS, e, "创建 Nutanix 客户端失败");
        }
    }

    /**
     * 获取带版本检测的 Nutanix 客户端实例
     *
     * @param clazz 客户端类型
     * @param body 请求体，包含认证信息
     * @param <C> 客户端类型
     * @return 客户端实例
     */
    public static <C> C clientWithVersionDetection(Class<C> clazz, BaseCloudRequestBody body) {
        C client = client(clazz, body);

        if (client instanceof NutanixClient) {
            NutanixClient nutanixClient = (NutanixClient) client;

            try {
                // 执行版本检测
                String detectedVersion = nutanixClient.detectBestApiVersion();
                log.info("检测到的最佳 API 版本: {}", detectedVersion);

                // 可以根据检测结果调整客户端行为
                // 这里暂时只记录日志，实际使用时可以根据版本调整 API 调用策略

            } catch (Exception e) {
                log.warn("版本检测失败，将使用默认配置", e);
            }
        }

        return client;
    }
}
