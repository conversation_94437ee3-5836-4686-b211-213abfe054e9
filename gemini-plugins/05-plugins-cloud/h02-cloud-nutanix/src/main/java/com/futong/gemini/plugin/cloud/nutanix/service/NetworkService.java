package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.nutanix.common.Constant;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 网络服务类
 * 负责网络和子网的管理
 *
 * 主要功能：
 * - 实现子网查询和管理 (Prism Central v3 /subnets/list)
 * - 实现网络创建和删除功能
 * - 支持 Nutanix 网络基础设施管理
 *
 * API 端点: /api/nutanix/v3/subnets
 * 依赖关系: CloudService
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class NetworkService {

    /**
     * 查询子网列表
     * 使用 Prism Central v3 API: /api/nutanix/v3/subnets/list
     */
    public static BaseResponse listSubnets(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 子网列表");

            // 设置默认分页参数
            DefaultParamService.defaultPage50(request);

            // 调用 Nutanix API 查询子网列表
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "subnet");
            requestBody.put("length", request.getBody().getCloud().getInteger("length"));
            JSONObject listResponse = client.doPostDataJSON("/subnets/list", requestBody);

            return new BaseDataResponse<>(listResponse);

        } catch (Exception e) {
            log.error("查询 Nutanix 子网列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询子网列表失败");
        }
    }

    /**
     * 查询单个子网详情
     * 使用 Prism Central v3 API: /api/nutanix/v3/subnets/{uuid}
     */
    public static BaseResponse getSubnet(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 子网详情");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "subnetUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：subnetUuid");
            }

            String subnetUuid = request.getBody().getCloud().getString("subnetUuid");

            // 调用 Nutanix API 查询子网详情
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject subnetResponse = client.doGetDataJSON("/subnets/" + subnetUuid);

            return new BaseDataResponse<>(subnetResponse);

        } catch (Exception e) {
            log.error("查询 Nutanix 子网详情失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "查询子网详情失败");
        }
    }

    // 注意：根据开发规范，查询方法无需写入，这些方法已移除
    // 网络查询应通过 FetchService 进行数据同步，或直接调用 API 客户端

    public static BaseResponse createNetwork(BaseCloudRequest request) {
        try {
            log.info("开始创建 Nutanix 网络");
            
            if (!CloudService.validateRequiredParams(request, "name", "networkType")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：name, networkType");
            }
            
            JSONObject cloud = request.getBody().getCloud();

            // 构建网络创建请求
            JSONObject networkSpec = buildNetworkCreateSpec(cloud);

            // 调用 Nutanix API 创建网络
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject createResponse = client.doPostDataJSON("/subnets", networkSpec);

            JSONObject result = new JSONObject();
            if (createResponse != null && createResponse.containsKey("metadata")) {
                result.put("networkUuid", createResponse.getJSONObject("metadata").getString("uuid"));
                result.put("name", createResponse.getJSONObject("spec").getString("name"));
                result.put("networkType", cloud.getString("networkType"));
                result.put("status", "CREATING");
            } else {
                result.put("networkUuid", java.util.UUID.randomUUID().toString());
                result.put("name", cloud.getString("name"));
                result.put("networkType", cloud.getString("networkType"));
                result.put("status", "CREATING");
            }
            result.put("timestamp", System.currentTimeMillis());
            
            // 记录操作日志
            log.info("网络创建成功: {}", result.getString("networkUuid"));
            
            return CloudService.buildSuccessResponse(result, "网络创建请求提交成功");
            
        } catch (Exception e) {
            log.error("创建 Nutanix 网络失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建网络失败");
        }
    }

    public static BaseResponse deleteNetwork(BaseCloudRequest request) {
        try {
            log.info("开始删除 Nutanix 网络");
            
            if (!CloudService.validateRequiredParams(request, "networkUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：networkUuid");
            }
            
            String networkUuid = request.getBody().getCloud().getString("networkUuid");

            // 调用 Nutanix API 删除网络
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject deleteResponse = client.doDeleteDataJSON("/subnets/" + networkUuid);

            JSONObject result = new JSONObject();
            result.put("networkUuid", networkUuid);
            result.put("status", "DELETING");
            result.put("timestamp", System.currentTimeMillis());

            // 记录任务ID
            if (deleteResponse != null && deleteResponse.containsKey("status") &&
                deleteResponse.getJSONObject("status").containsKey("execution_context")) {
                JSONObject executionContext = deleteResponse.getJSONObject("status").getJSONObject("execution_context");
                if (executionContext.containsKey("task_uuid")) {
                    result.put("taskUuid", executionContext.getString("task_uuid"));
                }
            }
            
            // 记录操作日志
            log.info("网络删除成功: {}", networkUuid);
            
            return CloudService.buildSuccessResponse(result, "网络删除请求提交成功");
            
        } catch (Exception e) {
            log.error("删除 Nutanix 网络失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除网络失败");
        }
    }

    /**
     * 构建网络创建规格
     */
    private static JSONObject buildNetworkCreateSpec(JSONObject cloud) {
        JSONObject spec = new JSONObject();

        // 基本信息
        spec.put("name", cloud.getString("name"));
        spec.put("description", cloud.getString("description"));

        // 网络资源
        JSONObject resources = new JSONObject();

        // 网络类型
        String networkType = cloud.getString("networkType");
        if (networkType == null) {
            networkType = Constant.Network.NETWORK_TYPE_VLAN;
        }
        resources.put("subnet_type", networkType);

        // VLAN ID
        if (cloud.containsKey("vlanId")) {
            resources.put("vlan_id", cloud.getInteger("vlanId"));
        }

        // IP 配置
        JSONObject ipConfig = new JSONObject();
        String ipConfigType = cloud.getString("ipConfigType");
        if (ipConfigType == null) {
            ipConfigType = Constant.Network.IP_CONFIG_TYPE_DHCP;
        }

        if (Constant.Network.IP_CONFIG_TYPE_STATIC.equals(ipConfigType)) {
            // 静态IP配置
            if (cloud.containsKey("subnetIp")) {
                ipConfig.put("subnet_ip", cloud.getString("subnetIp"));
            }
            if (cloud.containsKey("prefixLength")) {
                ipConfig.put("prefix_length", cloud.getInteger("prefixLength"));
            }
            if (cloud.containsKey("defaultGateway")) {
                ipConfig.put("default_gateway_ip", cloud.getString("defaultGateway"));
            }
        }

        resources.put("ip_config", ipConfig);

        // 集群引用
        if (cloud.containsKey("clusterUuid")) {
            JSONObject clusterRef = new JSONObject();
            clusterRef.put("kind", "cluster");
            clusterRef.put("uuid", cloud.getString("clusterUuid"));
            resources.put("cluster_reference", clusterRef);
        }

        spec.put("resources", resources);

        return spec;
    }

    // ==================== 虚拟交换机管理 ====================

    // 注意：根据开发规范，虚拟交换机查询方法已移除
    // 虚拟交换机查询应通过 FetchService 进行数据同步，或直接调用 API 客户端

    /**
     * 获取虚拟交换机详情
     */
    public static BaseResponse getVirtualSwitch(BaseCloudRequest request) {
        try {
            log.info("查询 Nutanix 虚拟交换机详情");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "switchUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：switchUuid");
            }

            String switchUuid = request.getBody().getCloud().getString("switchUuid");

            // 调用 Nutanix API 查询虚拟交换机详情
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject switchDetailResponse = client.doGetDataJSON("/virtual_switches/" + switchUuid);

            return new BaseDataResponse<>(switchDetailResponse);

        } catch (Exception e) {
            log.error("查询 Nutanix 虚拟交换机详情失败", e);
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    /**
     * 创建虚拟交换机
     */
    public static BaseResponse createVirtualSwitch(BaseCloudRequest request) {
        try {
            log.info("创建 Nutanix 虚拟交换机");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "name")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：name");
            }

            // 构建虚拟交换机配置
            JSONObject switchConfig = new JSONObject();
            JSONObject spec = new JSONObject();
            JSONObject metadata = new JSONObject();

            // 设置基本信息
            spec.put("name", request.getBody().getCloud().getString("name"));

            // 设置描述（可选）
            if (request.getBody().getCloud().containsKey("description")) {
                spec.put("description", request.getBody().getCloud().getString("description"));
            }

            // 设置网络类型（可选）
            if (request.getBody().getCloud().containsKey("networkType")) {
                JSONObject resources = new JSONObject();
                resources.put("subnet_type", request.getBody().getCloud().getString("networkType"));
                spec.put("resources", resources);
            }

            switchConfig.put("spec", spec);
            switchConfig.put("metadata", metadata);

            // 调用 Nutanix API 创建虚拟交换机
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject createResponse = client.doPostDataJSON("/virtual_switches", switchConfig);

            log.info("Nutanix 虚拟交换机创建成功");
            return new BaseDataResponse<>(createResponse);

        } catch (Exception e) {
            log.error("创建 Nutanix 虚拟交换机失败", e);
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    /**
     * 删除虚拟交换机
     */
    public static BaseResponse deleteVirtualSwitch(BaseCloudRequest request) {
        try {
            log.info("删除 Nutanix 虚拟交换机");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "switchUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：switchUuid");
            }

            String switchUuid = request.getBody().getCloud().getString("switchUuid");

            // 调用 Nutanix API 删除虚拟交换机
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject deleteResponse = client.doDeleteDataJSON("/virtual_switches/" + switchUuid);

            log.info("Nutanix 虚拟交换机删除成功");
            return new BaseDataResponse<>(deleteResponse);

        } catch (Exception e) {
            log.error("删除 Nutanix 虚拟交换机失败", e);
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    /**
     * 更新虚拟交换机配置
     */
    public static BaseResponse updateVirtualSwitch(BaseCloudRequest request) {
        try {
            log.info("更新 Nutanix 虚拟交换机配置");

            // 验证必需参数
            if (!CloudService.validateRequiredParams(request, "switchUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：switchUuid");
            }

            String switchUuid = request.getBody().getCloud().getString("switchUuid");

            // 构建更新配置
            JSONObject switchConfig = new JSONObject();
            JSONObject spec = new JSONObject();

            // 更新名称（可选）
            if (request.getBody().getCloud().containsKey("name")) {
                spec.put("name", request.getBody().getCloud().getString("name"));
            }

            // 更新描述（可选）
            if (request.getBody().getCloud().containsKey("description")) {
                spec.put("description", request.getBody().getCloud().getString("description"));
            }

            switchConfig.put("spec", spec);

            // 调用 Nutanix API 更新虚拟交换机
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject updateResponse = client.doPutDataJSON("/virtual_switches/" + switchUuid, switchConfig);

            log.info("Nutanix 虚拟交换机更新成功");
            return new BaseDataResponse<>(updateResponse);

        } catch (Exception e) {
            log.error("更新 Nutanix 虚拟交换机失败", e);
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }
}
