package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.model.BaseDataResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.nutanix.common.Constant;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Nutanix 集群服务类
 * 负责集群相关的操作，包括查询集群列表、集群详情、集群性能等
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class ClusterService {
    
    /**
     * 查询集群列表
     * 获取 Nutanix 环境中的所有集群信息
     */
    public static BaseDataResponse<JSONObject> listClusters(BaseCloudRequest request) {
        try {
            log.info("开始查询 Nutanix 集群列表");
            
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "cluster");
            
            // 设置分页参数
            if (request.getBody().getCloud().containsKey("length")) {
                requestBody.put("length", request.getBody().getCloud().getInteger("length"));
            } else {
                requestBody.put("length", 100); // 默认获取100个
            }
            
            if (request.getBody().getCloud().containsKey("offset")) {
                requestBody.put("offset", request.getBody().getCloud().getInteger("offset"));
            }
            
            // 调用 Nutanix API 获取集群列表
            JSONObject result = client.doPostDataJSON("/clusters/list", requestBody);
            
            log.info("成功获取集群列表，共 {} 个集群", 
                result.containsKey("entities") ? result.getJSONArray("entities").size() : 0);
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询集群列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群列表失败"), e);
        }
    }
    
    /**
     * 查询集群详情
     * 根据集群 UUID 获取集群的详细信息
     */
    public static BaseDataResponse<JSONObject> getClusterDetail(BaseCloudRequest request) {
        try {
            String clusterUuid = request.getBody().getCloud().getString("clusterUuid");
            if (clusterUuid == null || clusterUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "集群 UUID 不能为空");
            }
            
            log.info("开始查询集群详情，集群 UUID: {}", clusterUuid);
            
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 调用 Nutanix API 获取集群详情
            JSONObject result = client.doGetDataJSON("/clusters/" + clusterUuid);
            
            log.info("成功获取集群详情，集群名称: {}", result.getString("spec.name"));
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询集群详情失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群详情失败"), e);
        }
    }
    
    /**
     * 查询集群性能数据
     * 获取集群的 CPU、内存、存储等性能指标
     */
    public static BaseDataResponse<JSONObject> getClusterPerformance(BaseCloudRequest request) {
        try {
            String clusterUuid = request.getBody().getCloud().getString("clusterUuid");
            if (clusterUuid == null || clusterUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "集群 UUID 不能为空");
            }
            
            log.info("开始查询集群性能数据，集群 UUID: {}", clusterUuid);
            
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 调用 Nutanix API 获取集群性能数据
            JSONObject result = client.doGetDataJSON("/clusters/" + clusterUuid + "/stats");
            
            log.info("成功获取集群性能数据");
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询集群性能数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群性能数据失败"), e);
        }
    }
    
    /**
     * 查询集群主机列表
     * 获取指定集群下的所有主机信息
     */
    public static BaseDataResponse<JSONObject> getClusterHosts(BaseCloudRequest request) {
        try {
            String clusterUuid = request.getBody().getCloud().getString("clusterUuid");
            if (clusterUuid == null || clusterUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "集群 UUID 不能为空");
            }
            
            log.info("开始查询集群主机列表，集群 UUID: {}", clusterUuid);
            
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "host");
            requestBody.put("filter", "cluster_reference==" + clusterUuid);
            requestBody.put("length", 100);
            
            // 调用 Nutanix API 获取主机列表
            JSONObject result = client.doPostDataJSON("/hosts/list", requestBody);
            
            log.info("成功获取集群主机列表，共 {} 个主机", 
                result.containsKey("entities") ? result.getJSONArray("entities").size() : 0);
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询集群主机列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群主机列表失败"), e);
        }
    }
    
    /**
     * 查询集群存储信息
     * 获取集群的存储容器和存储池信息
     */
    public static BaseDataResponse<JSONObject> getClusterStorage(BaseCloudRequest request) {
        try {
            String clusterUuid = request.getBody().getCloud().getString("clusterUuid");
            if (clusterUuid == null || clusterUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "集群 UUID 不能为空");
            }
            
            log.info("开始查询集群存储信息，集群 UUID: {}", clusterUuid);
            
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "storage_container");
            requestBody.put("filter", "cluster_reference==" + clusterUuid);
            requestBody.put("length", 50);
            
            // 调用 Nutanix API 获取存储容器列表
            JSONObject result = client.doPostDataJSON("/storage_containers/list", requestBody);
            
            log.info("成功获取集群存储信息，共 {} 个存储容器", 
                result.containsKey("entities") ? result.getJSONArray("entities").size() : 0);
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询集群存储信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群存储信息失败"), e);
        }
    }
    
    /**
     * 查询集群网络信息
     * 获取集群的网络和子网信息
     */
    public static BaseDataResponse<JSONObject> getClusterNetworks(BaseCloudRequest request) {
        try {
            String clusterUuid = request.getBody().getCloud().getString("clusterUuid");
            if (clusterUuid == null || clusterUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "集群 UUID 不能为空");
            }
            
            log.info("开始查询集群网络信息，集群 UUID: {}", clusterUuid);
            
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "subnet");
            requestBody.put("filter", "cluster_reference==" + clusterUuid);
            requestBody.put("length", 50);
            
            // 调用 Nutanix API 获取子网列表
            JSONObject result = client.doPostDataJSON("/subnets/list", requestBody);
            
            log.info("成功获取集群网络信息，共 {} 个子网", 
                result.containsKey("entities") ? result.getJSONArray("entities").size() : 0);
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询集群网络信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群网络信息失败"), e);
        }
    }
    
    /**
     * 查询集群虚拟机列表
     * 获取指定集群下的所有虚拟机信息
     */
    public static BaseDataResponse<JSONObject> getClusterVms(BaseCloudRequest request) {
        try {
            String clusterUuid = request.getBody().getCloud().getString("clusterUuid");
            if (clusterUuid == null || clusterUuid.trim().isEmpty()) {
                throw new BaseException(BaseResponse.FAIL_PARAM, "集群 UUID 不能为空");
            }
            
            log.info("开始查询集群虚拟机列表，集群 UUID: {}", clusterUuid);
            
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "vm");
            requestBody.put("filter", "cluster_reference==" + clusterUuid);
            requestBody.put("length", 200);
            
            // 调用 Nutanix API 获取虚拟机列表
            JSONObject result = client.doPostDataJSON("/vms/list", requestBody);
            
            log.info("成功获取集群虚拟机列表，共 {} 个虚拟机", 
                result.containsKey("entities") ? result.getJSONArray("entities").size() : 0);
            
            return new BaseDataResponse<>(result);
            
        } catch (Exception e) {
            log.error("查询集群虚拟机列表失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("查询集群虚拟机列表失败"), e);
        }
    }
}
