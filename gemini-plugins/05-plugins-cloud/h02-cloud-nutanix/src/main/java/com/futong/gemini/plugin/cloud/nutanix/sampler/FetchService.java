package com.futong.gemini.plugin.cloud.nutanix.sampler;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.PageUtils;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.gjc.entity.TaskInfoBean;
import com.futong.gemini.model.otc.nxc.entity.CmdbStorageContainerRes;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.JobInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Nutanix 数据获取服务类
 * 简化版本，专注于数据获取，转换功能移到 service 包
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class FetchService {

    /**
     * 平台资源 API 版本缓存 - 支持按资源类型缓存
     * 第一层 Key: 平台标识（host:port 或 cmpId）
     * 第二层 Key: 资源类型（storage_container, disk, storage_pool, network 等）
     * Value: API 版本（v3, v2.0, v1）
     */
    private static final Map<String, Map<String, String>> platformResourceApiVersions = new ConcurrentHashMap<>();

    public static Map<String, JSONObject> virtualDiskMap = new ConcurrentHashMap<String, JSONObject>();

    /**
     * 平台默认 API 版本缓存（向后兼容）
     * Key: 平台标识（host:port 或 cmpId），Value: 默认 API 版本
     */
    private static final Map<String, String> platformDefaultApiVersions = new ConcurrentHashMap<>();

    /**
     * 通用的响应处理方法
     * 基于版本检测智能解析响应，简化日志输出
     */
    private static BaseResponse processResponse(BaseCloudRequest request, JSONObject response,
                                                String resourceName, String[] v2Fields,
                                                java.util.function.Function<JSONObject, Map<Class, List>> converter) {
        if (response == null) {
            log.warn("{}响应为空", resourceName);
            return BaseResponse.SUCCESS.of("未获取到" + resourceName + "信息");
        }

        // 基于版本智能解析响应字段
        JSONArray entities = parseResponseByVersion(response, resourceName, v2Fields);

        if (entities == null || entities.isEmpty()) {
            log.info("{}响应中未包含有效数据", resourceName);
            return BaseResponse.SUCCESS.of("未获取到" + resourceName + "信息");
        }

        log.info("成功获取到 {} 个{}信息", entities.size(), resourceName);

        try {
            // 转换数据格式并发送到消息队列
            Map<Class, List> convertedData = converter.apply(response);

            if (convertedData != null && !convertedData.isEmpty()) {
                BaseCloudService.fetchSend(request, convertedData);
                log.info("{}数据已发送到消息队列", resourceName);
            } else {
                log.warn("{}数据转换后为空，跳过发送", resourceName);
            }

            return BaseResponse.SUCCESS.of(resourceName + "信息获取成功");
        } catch (Exception e) {
            log.error("处理{}数据时发生异常", resourceName, e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("处理" + resourceName + "数据失败: " + e.getMessage()), e);
        }
    }

    /**
     * 基于响应内容智能解析响应字段
     * 不再依赖版本缓存，直接根据响应内容判断
     */
    private static JSONArray parseResponseByVersion(JSONObject response, String resourceName, String[] v2Fields) {
        // 优先尝试 v3 API 的标准字段
        if (response.containsKey("entities")) {
            return response.getJSONArray("entities");
        }

        // 尝试 v2.0 API 的特定字段
        if (v2Fields != null) {
            for (String field : v2Fields) {
                if (response.containsKey(field)) {
                    return response.getJSONArray(field);
                }
            }
        }

        log.debug("未识别的{}响应格式: {}", resourceName, response.keySet());
        return null;
    }


    /**
     * 获取并缓存特定平台和资源类型的 API 版本
     *
     * @param client Nutanix 客户端
     * @param request 请求对象
     * @param resourceType 资源类型（storage_container, disk, storage_pool, network 等）
     * @return 该资源类型的最佳 API 版本
     */
    private static String getApiVersion(NutanixClient client, BaseCloudRequest request, String resourceType) {
        // 生成平台唯一标识
        String platformKey = generatePlatformKey(request);

        // 从缓存中获取该资源类型的版本信息
        Map<String, String> resourceVersions = platformResourceApiVersions.get(platformKey);
        if (resourceVersions != null && resourceVersions.containsKey(resourceType)) {
            String cachedVersion = resourceVersions.get(resourceType);
            log.info("使用缓存的 API 版本 [{}] 对于资源类型 [{}]", cachedVersion, resourceType);
            return cachedVersion;
        }

        // 检测该资源类型的最佳 API 版本
        String apiVersion = detectResourceApiVersion(client, resourceType);

        // 缓存检测结果
        resourceVersions = platformResourceApiVersions.computeIfAbsent(platformKey, k -> new ConcurrentHashMap<>());
        resourceVersions.put(resourceType, apiVersion);

        log.info("检测到 Nutanix 平台 [{}] 资源类型 [{}] 最佳 API 版本: {}", platformKey, resourceType, apiVersion);
        return apiVersion;
    }

    /**
     * 获取并缓存特定平台的默认 API 版本（向后兼容）
     */
    private static String getApiVersion(NutanixClient client, BaseCloudRequest request) {
        // 生成平台唯一标识
        String platformKey = generatePlatformKey(request);

        // 从缓存中获取版本信息
        String apiVersion = platformDefaultApiVersions.get(platformKey);
        if (apiVersion == null) {
            // 检测并缓存版本信息
            apiVersion = client.detectBestApiVersion();
            platformDefaultApiVersions.put(platformKey, apiVersion);
            log.info("检测到 Nutanix 平台 [{}] 默认 API 版本: {}", platformKey, apiVersion);
        }
        return apiVersion;
    }

    /**
     * 检测特定资源类型的最佳 API 版本
     *
     * @param client Nutanix 客户端
     * @param resourceType 资源类型
     * @return 该资源类型的最佳 API 版本
     */
    private static String detectResourceApiVersion(NutanixClient client, String resourceType) {
        log.debug("开始检测资源类型 [{}] 的 API 版本", resourceType);

        // 根据资源类型定义测试端点
        String[] testEndpoints = getTestEndpointsForResource(resourceType);

        // 测试 v3 API 端点
        for (String endpoint : testEndpoints) {
            if (client.isEndpointAvailable(endpoint)) {
                log.info("资源类型 [{}] 支持 v3 API 端点: {}", resourceType, endpoint);
                return "v3";
            }
        }

        // 对于存储池，需要进一步检测 v2.0 和 v1 API 的可用性
        if ("storage_pool".equals(resourceType)) {
            return detectStoragePoolApiVersion(client);
        }

        log.warn("资源类型 [{}] 不支持 v3 API，将使用 v2.0 API 作为后备", resourceType);
        return "v2.0";
    }

    /**
     * 专门检测存储池的 API 版本支持
     *
     * @param client Nutanix 客户端
     * @return 存储池支持的最佳 API 版本
     */
    private static String detectStoragePoolApiVersion(NutanixClient client) {
        log.debug("检测存储池的 API 版本支持");

        // 检测 v2.0 API 可用性 - 使用 GET 请求测试
        if (isStoragePoolEndpointAvailable(client, "v2.0")) {
            log.info("存储池支持 v2.0 API");
            return "v2.0";
        }

        // 检测 v1 API 可用性 - 使用 GET 请求测试
        if (isStoragePoolEndpointAvailable(client, "v1")) {
            log.info("存储池支持 v1 API");
            return "v1";
        }

        log.warn("存储池不支持任何已知的 API 版本，默认使用 v2.0");
        return "v2.0";
    }

    /**
     * 检测存储池特定版本的端点是否可用
     *
     * @param client Nutanix 客户端
     * @param version API 版本 (v1, v2.0)
     * @return 是否可用
     */
    private static boolean isStoragePoolEndpointAvailable(NutanixClient client, String version) {
        try {
            String endpoint = "/PrismGateway/services/rest/" + version + "/storage_pools";
            log.debug("检查存储池 {} API 端点可用性: {}", version, endpoint);

            // 使用 GET 请求测试端点可用性
            JSONObject response = client.doGetDataJSONWithFullPath(endpoint);

            // 检查响应是否有效
            if (response != null) {
                log.debug("存储池 {} API 端点可用", version);
                return true;
            } else {
                log.debug("存储池 {} API 端点返回空响应", version);
                return false;
            }
        } catch (Exception e) {
            log.debug("存储池 {} API 端点不可用: {}", version, e.getMessage());
            return false;
        }
    }

    /**
     * 根据资源类型获取测试端点
     *
     * @param resourceType 资源类型
     * @return 该资源类型的 v3 API 测试端点数组
     */
    private static String[] getTestEndpointsForResource(String resourceType) {
        switch (resourceType) {
            case "storage_container":
                return new String[]{"/storage_containers/list", "/containers/list"};
            case "disk":
                return new String[]{"/disks/list"};
            case "storage_pool":
                return new String[]{"/storage_pools/list", "/pools/list"};
            case "network":
                return new String[]{"/subnets/list", "/networks/list"};
            case "vm":
                return new String[]{"/vms/list"};
            case "host":
                return new String[]{"/hosts/list"};
            case "cluster":
                return new String[]{"/clusters/list"};
            case "task":
                return new String[]{"/tasks"};
            case "alert":
                return new String[]{"/alerts/list"};
            case "event":
                return new String[]{"/events/list"};
            default:
                log.warn("未知的资源类型: {}，使用默认测试端点", resourceType);
                return new String[]{"/clusters/list", "/vms/list"};
        }
    }

    /**
     * 更新特定资源类型的 API 版本缓存
     *
     * @param request 请求对象
     * @param resourceType 资源类型
     * @param apiVersion 新的 API 版本
     */
    private static void updateResourceApiVersion(BaseCloudRequest request, String resourceType, String apiVersion) {
        String platformKey = generatePlatformKey(request);
        Map<String, String> resourceVersions = platformResourceApiVersions.computeIfAbsent(platformKey, k -> new ConcurrentHashMap<>());
        resourceVersions.put(resourceType, apiVersion);
        log.info("已更新平台 [{}] 资源类型 [{}] 的 API 版本缓存为: {}", platformKey, resourceType, apiVersion);
    }

    /**
     * 使用 v3 API 获取存储池信息
     */
    private static JSONObject fetchStoragePoolWithV3Api(NutanixClient client) throws BaseException {
        log.debug("使用 Prism Central v3 API 获取存储池列表");

        JSONObject requestBody = new JSONObject();
        requestBody.put("length", 500); // 获取最多500个存储池

        // 定义 v3 API 端点优先级列表
        String[] v3Endpoints = {"/storage_pools/list", "/pools/list"};

        for (String endpoint : v3Endpoints) {
            try {
                log.debug("尝试 v3 API 端点: {}", endpoint);
                JSONObject response = client.doPostDataJSON(endpoint, requestBody);

                if (response != null && response.containsKey("entities")) {
                    log.info("使用 Prism Central v3 API ({}) 成功获取存储池信息", endpoint);
                    return response;
                } else {
                    log.debug("v3 API 端点 {} 返回无效响应", endpoint);
                }
            } catch (Exception e) {
                log.debug("v3 API 端点 {} 调用失败: {}", endpoint, e.getMessage());
            }
        }

        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("所有 v3 API 端点都不可用"));
    }

    /**
     * 使用 v2.0 API 获取存储池信息
     */
    private static JSONObject fetchStoragePoolWithV2Api(NutanixClient client) throws BaseException {
        log.debug("使用 Prism Element v2.0 API 获取存储池列表");

        String v2ApiPath = "/PrismGateway/services/rest/v2.0/storage_pools";
        JSONObject response = client.doGetDataJSONWithFullPath(v2ApiPath);

        if (response != null) {
            log.info("使用 Prism Element v2.0 API 成功获取存储池信息");
            return response;
        } else {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("v2.0 API 返回无效响应"));
        }
    }

    /**
     * 使用 v1 API 获取存储池信息
     */
    private static JSONObject fetchStoragePoolWithV1Api(NutanixClient client) throws BaseException {
        log.debug("使用 Prism Element v1 API 获取存储池列表");

        String v1ApiPath = "/PrismGateway/services/rest/v1/storage_pools";
        JSONObject response = client.doGetDataJSONWithFullPath(v1ApiPath);

        if (response != null) {
            log.info("使用 Prism Element v1 API 成功获取存储池信息");
            return response;
        } else {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("v1 API 返回无效响应"));
        }
    }

    /**
     * 带降级策略的存储池获取方法
     *
     * @param client Nutanix 客户端
     * @param request 请求对象
     * @param preferredVersion 首选的 API 版本
     * @return API 响应
     * @throws BaseException 当所有 API 版本都失败时抛出异常
     */
    private static JSONObject fetchStoragePoolWithFallback(NutanixClient client, BaseCloudRequest request, String preferredVersion) throws BaseException {
        log.debug("使用降级策略获取存储池，首选版本: {}", preferredVersion);

        // 定义尝试顺序
        String[] versionOrder;
        if ("v3".equals(preferredVersion)) {
            versionOrder = new String[]{"v3", "v2.0", "v1"};
        } else if ("v2.0".equals(preferredVersion)) {
            versionOrder = new String[]{"v2.0", "v1", "v3"};
        } else {
            versionOrder = new String[]{"v1", "v2.0", "v3"};
        }

        Exception lastException = null;

        for (String version : versionOrder) {
            try {
                log.debug("尝试使用 {} API 获取存储池", version);

                JSONObject response = null;
                if ("v3".equals(version)) {
                    response = fetchStoragePoolWithV3Api(client);
                } else if ("v2.0".equals(version)) {
                    response = fetchStoragePoolWithV2Api(client);
                } else if ("v1".equals(version)) {
                    response = fetchStoragePoolWithV1Api(client);
                }

                if (response != null) {
                    log.info("成功使用 {} API 获取存储池信息", version);

                    // 如果使用的版本与首选版本不同，更新缓存
                    if (!version.equals(preferredVersion)) {
                        updateResourceApiVersion(request, "storage_pool", version);
                        log.info("已更新存储池 API 版本缓存为: {}", version);
                    }

                    return response;
                }
            } catch (Exception e) {
                log.debug("{} API 获取存储池失败: {}", version, e.getMessage());
                lastException = e;
            }
        }

        // 所有版本都失败了
        String errorMessage = "所有 API 版本 (v3, v2.0, v1) 都无法获取存储池信息";
        if (lastException != null) {
            errorMessage += ": " + lastException.getMessage();
        }
        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of(errorMessage));
    }

    /**
     * 生成平台唯一标识
     */
    private static String generatePlatformKey(BaseCloudRequest request) {
        try {
            // 使用 cmpId 作为平台标识
            String cmpId = request.getBody().getAccess().getCmpId();
            if (cmpId != null && !cmpId.isEmpty()) {
                return cmpId;
            }

            // 如果没有 cmpId，尝试使用用户名
            String username = request.getBody().getAccess().getUsername();
            if (username != null && !username.isEmpty()) {
                return username;
            }

            // 最后使用默认标识
            return "default";
        } catch (Exception e) {
            log.warn("生成平台标识失败，使用默认标识", e);
            return "default";
        }
    }

    /**
     * 获取集群信息
     * Nutanix 使用集群概念，不存在传统公有云的区域概念
     */
    public static BaseResponse fetchCluster(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 集群信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "cluster");
            requestBody.put("length", 100); // 获取最多100个集群

            // 调用 API 获取集群列表
            JSONObject response = client.doPostDataJSON("/clusters/list", requestBody);

            return processResponse(request, response, "集群", null,
                                 resp -> Convert.convertClusterData(request, resp));

        } catch (Exception e) {
            log.error("获取集群信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取集群信息失败"), e);
        }
    }

    /**
     * 获取主机信息
     * Nutanix 使用主机概念，不存在传统公有云的可用区概念
     */
    public static BaseResponse fetchHost(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 主机信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "host");
            requestBody.put("length", 500); // 获取最多500个主机

            // 调用 API 获取主机列表
            JSONObject response = client.doPostDataJSON("/hosts/list", requestBody);

            if (response != null) {
                JSONArray hosts = null;

                // 处理不同 API 版本的响应格式
                if (response.containsKey("entities")) {
                    // Prism Central v3 API 或 Prism Element v2.0 API 格式
                    hosts = response.getJSONArray("entities");
                }

                if (hosts != null) {
                    log.info("成功获取到 {} 个主机信息", hosts.size());
                    log.debug("原始主机数据: {}", JSON.toJSONString(response));

                    // 转换数据格式并发送到消息队列
                    Map<Class, List> convertedData = Convert.convertHostData(request, response);

                    BaseCloudService.fetchSend(request, convertedData);
                    log.info("主机数据已发送到消息队列");

                    return BaseResponse.SUCCESS.of("主机信息获取成功");
                } else {
                    return BaseResponse.SUCCESS.of("未获取到主机信息");
                }
            } else {
                return BaseResponse.SUCCESS.of("API 响应为空");
            }

        } catch (Exception e) {
            log.error("获取主机信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取主机信息失败"), e);
        }
    }

    /**
     * 获取虚拟机实例信息
     * 从 Nutanix 平台同步虚拟机列表数据
     */
    public static BaseResponse fetchVirtualMachines(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 虚拟机实例信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "vm");
            requestBody.put("length", 500); // 获取最多500个虚拟机

            // 调用 API 获取虚拟机列表
            JSONObject response = client.doPostDataJSON("/vms/list", requestBody);

            return processResponse(request, response, "虚拟机", null,
                                 resp -> Convert.convertVmData(request, resp));

        } catch (Exception e) {
            log.error("获取虚拟机信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟机信息失败"), e);
        }
    }

//    /**
//     * 获取虚拟机性能数据
//     * 支持 Prism Element v2.0 和 Prism Central v3 API
//     * 同步虚拟机的 CPU、内存、磁盘、网络性能指标
//     */
//    public static BaseResponse fetchVirtualMachinePerformance(BaseCloudRequest request) {
//        try {
//            log.info("开始获取 Nutanix 虚拟机性能数据");
//
//            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
//
//            // 获取虚拟机列表
//            JSONObject vmListRequest = new JSONObject();
//            vmListRequest.put("kind", "vm");
//            vmListRequest.put("length", 100);
//
//            JSONObject vmResponse = client.doPostDataJSON("/vms/list", vmListRequest);
//
//            if (vmResponse != null && vmResponse.containsKey("entities")) {
//                JSONArray vms = vmResponse.getJSONArray("entities");
//                log.info("开始获取 {} 个虚拟机的性能数据", vms.size());
//                log.debug("原始虚拟机性能数据: {}", JSON.toJSONString(vmResponse));
//
//                // 转换性能数据格式并发送到消息队列
//                Map<String, PerfInfoBean> convertedData = Convert.convertVmPerformanceData(request, vmResponse);
////                log.info("转换监控信息为:{}", JSON.toJSONString(convertedData));
//                BaseCloudService.toPerfMessageAndSend(new ArrayList<>(convertedData.values()), "API");
////                BaseCloudService.fetchSend(request, convertedData);
//                log.info("虚拟机性能数据已发送到消息队列");
//
//                return BaseResponse.SUCCESS.of("虚拟机性能数据获取成功");
//            } else {
//                log.warn("未获取到虚拟机性能数据");
//                return BaseResponse.SUCCESS.of("未获取到虚拟机性能数据");
//            }
//
//        } catch (Exception e) {
//            log.error("获取虚拟机性能数据失败", e);
//            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟机性能数据失败"), e);
//        }
//    }
    /**
     * 获取虚拟机性能数据
     * 支持 Prism Element v2.0 和 Prism Central v3 API
     * 同步虚拟机的 CPU、内存、磁盘、网络性能指标
     */
    public static BaseResponse fetchVirtualMachinePerformance(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 虚拟机性能数据");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 获取虚拟机列表
            JSONObject vmListRequest = new JSONObject();
            vmListRequest.put("kind", "vm");
            vmListRequest.put("length", 100);

            JSONObject vmResponse = client.doPostDataJSON("/vms/list", vmListRequest);

            if (vmResponse != null && vmResponse.containsKey("entities")) {
                JSONArray vms = vmResponse.getJSONArray("entities");
                log.info("开始获取 {} 个虚拟机的性能数据", vms.size());
                log.info("原始虚拟机性能数据: {}", JSON.toJSONString(vmResponse));

                // 转换性能数据格式并发送到消息队列
                Map<String, PerfInfoBean> convertedData = Convert.convertVmPerformanceData(client,request, vmResponse);
//                log.info("转换监控信息为:{}", JSON.toJSONString(convertedData));
                BaseCloudService.toPerfMessageAndSend(new ArrayList<>(convertedData.values()), "API");
//                BaseCloudService.fetchSend(request, convertedData);
                log.info("虚拟机性能数据已发送到消息队列");

                return BaseResponse.SUCCESS.of("虚拟机性能数据获取成功");
            } else {
                log.warn("未获取到虚拟机性能数据");
                return BaseResponse.SUCCESS.of("未获取到虚拟机性能数据");
            }

        } catch (Exception e) {
            log.error("获取虚拟机性能数据失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟机性能数据失败"), e);
        }
    }
    public static boolean defaultVmBeforeBasicInfo(BaseCloudRequest request) {
        //转换分页获取云主机列表查询入参
        BasePageSortSearchRequest searchRequest = Convert.toBasePageSortSearchRequest(request);
        searchRequest.setSize(50);
        if (searchRequest.getCurrent() == null || searchRequest.getCurrent() == 0) {
            searchRequest.setCurrent(1);
        }
        //从南新仓获取可查询监控得主机集合
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> result = ApiFactory.Api.res.listInstanceDisk(searchRequest);
        if (result.getData() == null || CollUtil.isEmpty(result.getData().getList())) {
            throw new BaseException(BaseResponse.SUCCESS.of("无可获取监控得云主机"));
        }
        String message = StrUtil.format("本次从南新仓获取可查询监控云主机信息,页码：{},条数：{},本次获取条数：{},数据底座共有云主机：{}", searchRequest.getCurrent(), 50, result.getData().getList().size(), result.getData().getCount());
        BaseResponse baseResponse = BaseResponse.SUCCESS.of(message);
        if (request.getBody().getGourd().isZero()) {
            List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(result.getData().getCount(), 50);
            baseResponse = BaseCloudService.toGourdResponse(baseResponse, totalPage, (Integer t) -> {
                JobInfo jobInfo = new JobInfo();
                searchRequest.setCurrent(t);
                jobInfo.setRequest(request.cloneJSONObject());
                return jobInfo;
            });
        }
        request.getBody().getCloud().put("vmResponse", baseResponse);

        Map<String, ResInstanceDiskApiModel> instanceMap = CollStreamUtil.toMap(result.getData().getList(), ResInstanceDiskApiModel::getOpen_id, t -> t);
        request.getBody().getCloud().put("vmInstanceMap", instanceMap);
        //设置采集监控数据主机ID
        Set<String> instanceIds = instanceMap.keySet();
        request.getBody().getCloud().put("vmInstanceIds", instanceIds);
        return true;
    }
    /**
     * 获取虚拟机磁盘信息（虚拟磁盘）
     */
    public static BaseResponse fetchDisk(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 虚拟机磁盘信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;

            // 使用资源特定的版本检测来智能选择 API
            String apiVersion = getApiVersion(client, request, "disk");

            if ("v3".equals(apiVersion)) {
                // 使用 Prism Central v3 API
                log.debug("使用 Prism Central v3 API 获取磁盘列表");
                JSONObject requestBody = new JSONObject();
                requestBody.put("length", 500); // 获取最多500个磁盘

                try {
                    response = client.doPostDataJSON("/disks/list", requestBody);
                    log.info("使用 Prism Central v3 API 成功获取磁盘信息");
                } catch (Exception v3Exception) {
                    log.warn("v3 API 获取磁盘失败，自动降级到 v2.0 API: {}", v3Exception.getMessage());
                    // 自动降级到 v2.0 API
                    try {
                        log.debug("降级使用 Prism Element v2.0 API 获取磁盘列表");
                        String v2ApiPath = "/PrismGateway/services/rest/v2.0/virtual_disks";
                        response = client.doGetDataJSONWithFullPath(v2ApiPath);
                        log.info("降级使用 Prism Element v2.0 API 成功获取磁盘信息");

                        // 更新磁盘资源类型的 API 版本为 v2.0
                        updateResourceApiVersion(request, "disk", "v2.0");
                    } catch (Exception v2Exception) {
                        log.error("v2.0 API 也失败了: {}", v2Exception.getMessage());
                        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("v3 和 v2.0 API 都不可用: " + v2Exception.getMessage()));
                    }
                }
            } else {
                // 使用 Prism Element v2.0 API
                log.debug("使用 Prism Element v2.0 API 获取磁盘列表");
                String v2ApiPath = "/PrismGateway/services/rest/v2.0/virtual_disks";
                response = client.doGetDataJSONWithFullPath(v2ApiPath);
                log.info("使用 Prism Element v2.0 API 成功获取磁盘信息");
            }

            return processResponse(request, response, "磁盘", new String[]{"virtual_disks"},
                    resp -> Convert.convertDiskData(request, resp));

        } catch (Exception e) {
            log.error("获取虚拟磁盘信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟磁盘信息失败"), e);
        }
    }

    /**
     * 获取虚拟机磁盘信息（虚拟磁盘）
     */
    public static BaseResponse fetchVirtualDisk(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 虚拟机磁盘信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;


                // 使用 Prism Element v2.0 API
                log.debug("使用 Prism Element v2.0 API 获取磁盘列表");
                String v2ApiPath = "/PrismGateway/services/rest/v2.0/virtual_disks";
                response = client.doGetDataJSONWithFullPath(v2ApiPath);
                log.info("使用 Prism Element v2.0 API 成功获取磁盘信息");

            Convert.convertVirtualDiskData(request, response);

            return BaseResponse.SUCCESS;

        } catch (Exception e) {
            log.error("获取虚拟磁盘信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟磁盘信息失败"), e);
        }
    }

    /**
     * 获取快照信息
     */
    public static BaseResponse fetchSnapshot(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 快照信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("kind", "vm_recovery_point");
            requestBody.put("length", 500);

            // 调用 API 获取快照列表
            JSONObject response = client.doPostDataJSON("/vm_recovery_points/list", requestBody);

            return processResponse(request, response, "快照", null,
                                 resp -> Convert.convertSnapshotData(request, resp));

        } catch (Exception e) {
            log.error("获取快照信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取快照信息失败"), e);
        }
    }

    /**
     * 获取存储容器信息
     */
    public static BaseResponse fetchStorageContainer(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 存储容器信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;

            // 使用资源特定的版本检测来智能选择 API
            String apiVersion = getApiVersion(client, request, "storage_container");

            if ("v3".equals(apiVersion)) {
                // 使用 Prism Central v3 API
                log.debug("使用 Prism Central v3 API 获取存储容器列表");
                JSONObject requestBody = new JSONObject();
                requestBody.put("kind", "storage_container");
                requestBody.put("length", 200);

                // 尝试多个可能的 v3 API 端点
                String[] v3Endpoints = {"/storage_containers/list", "/containers/list"};
                boolean success = false;

                for (String endpoint : v3Endpoints) {
                    try {
                        log.debug("尝试 v3 API 端点: {}", endpoint);
                        response = client.doPostDataJSON(endpoint, requestBody);

                        if (response != null && response.containsKey("entities")) {
                            log.info("使用 Prism Central v3 API ({}) 成功获取存储容器信息", endpoint);
                            success = true;
                            break;
                        }
                    } catch (Exception endpointException) {
                        log.debug("v3 API 端点 {} 失败: {}", endpoint, endpointException.getMessage());
                    }
                }

                if (!success) {
                    log.warn("所有 v3 API 端点都不可用，自动降级到 v2.0 API");
                    // 自动降级到 v2.0 API
                    try {
                        log.debug("降级使用 Prism Element v2.0 API 获取存储容器列表");
                        String v2ApiPath = "/PrismGateway/services/rest/v2.0/storage_containers";
                        response = client.doGetDataJSONWithFullPath(v2ApiPath);
                        log.info("降级使用 Prism Element v2.0 API 成功获取存储容器信息");

                        // 更新存储容器资源类型的 API 版本为 v2.0，避免下次再尝试 v3
                        updateResourceApiVersion(request, "storage_container", "v2.0");
                    } catch (Exception v2Exception) {
                        log.error("v2.0 API 也失败了: {}", v2Exception.getMessage());
                        throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("v3 和 v2.0 API 都不可用: " + v2Exception.getMessage()));
                    }
                }
            } else {
                // 使用 Prism Element v2.0 API
                log.debug("使用 Prism Element v2.0 API 获取存储容器列表");
                String v2ApiPath = "/PrismGateway/services/rest/v2.0/storage_containers";
                response = client.doGetDataJSONWithFullPath(v2ApiPath);
                log.info("使用 Prism Element v2.0 API 成功获取存储容器信息");
            }

            if (response != null) {
                // 处理不同 API 版本的响应格式
                JSONArray containers = null;
                if (response.containsKey("entities")) {
                    // Prism Central v3 或 Prism Element v2.0 格式
                    containers = response.getJSONArray("entities");
                } else if (response.containsKey("storage_containers")) {
                    // 某些 v2.0 API 可能使用不同的字段名
                    containers = response.getJSONArray("storage_containers");
                } else if (response.containsKey("containers")) {
                    // v3 API 中的容器信息
                    containers = response.getJSONArray("containers");
                } else {
                    log.warn("未识别的存储容器响应格式: {}", response.keySet());
                    return BaseResponse.SUCCESS.of("未获取到存储容器信息");
                }

                if (containers != null) {
                    log.info("成功获取到 {} 个存储容器信息", containers.size());
                    log.debug("原始存储容器数据: {}", JSON.toJSONString(response));

                    // 转换数据格式并发送到消息队列
//                    Map<Class, List> convertedData = Convert.convertStorageData(request, response);
//                    BaseCloudService.fetchSend(request, convertedData);
//                    log.info("存储容器数据已发送到消息队列");

                    Map<Class, List> classListMap = Convert.convertStorageData(request, response);
                    return new BaseDataResponse<>(classListMap.get(CmdbStorageContainerRes.class));

//                    return BaseResponse.SUCCESS.of("存储容器信息获取成功");
                } else {
                    return BaseResponse.SUCCESS.of("未获取到存储容器信息");
                }
            } else {
                return BaseResponse.SUCCESS.of("未获取到存储容器信息");
            }

        } catch (Exception e) {
            log.error("获取存储容器信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取存储容器信息失败"), e);
        }
    }

    /**
     * 获取存储池信息
     * 从 Nutanix 平台同步存储池数据
     * 使用 Prism Element v1 API
     */
    public static BaseResponse fetchStoragePool(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 存储池信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;

            // 使用资源特定的版本检测来智能选择 API
            String apiVersion = getApiVersion(client, request, "storage_pool");
            log.debug("检测到存储池资源的 API 版本: {}", apiVersion);

            // 尝试使用检测到的 API 版本，如果失败则降级
            response = fetchStoragePoolWithFallback(client, request, apiVersion);

            if (response != null) {
                // 处理不同 API 版本的响应格式
                JSONArray storagePools = null;
                if (response.containsKey("entities")) {
                    // Prism Central v3 格式
                    storagePools = response.getJSONArray("entities");
                } else if (response.containsKey("storage_pools")) {
                    // Prism Element v1/v2.0 格式
                    storagePools = response.getJSONArray("storage_pools");
                } else if (response.containsKey("pools")) {
                    // v3 API 中的池信息
                    storagePools = response.getJSONArray("pools");
                } else {
                    log.warn("未识别的存储池响应格式: {}", response.keySet());
                    return BaseResponse.SUCCESS.of("未获取到存储池信息");
                }

                if (storagePools != null) {
                    log.info("成功获取到 {} 个存储池信息", storagePools.size());
                    log.debug("原始存储池数据: {}", JSON.toJSONString(response));

                    // 转换数据格式并发送到消息队列
                    Map<Class, List> convertedData = Convert.convertStoragePoolData(request, response);


                    BaseCloudService.fetchSend(request, convertedData);
                    log.info("存储池数据已发送到消息队列");

                    return BaseResponse.SUCCESS.of("存储池信息获取成功");
                } else {
                    return BaseResponse.SUCCESS.of("未获取到存储池信息");
                }
            } else {
                return BaseResponse.SUCCESS.of("未获取到存储池信息");
            }

        } catch (Exception e) {
            log.error("获取存储池信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取存储池信息失败"), e);
        }
    }

    /**
     * 获取子网信息
     * 从 Nutanix 平台同步子网数据
     * 支持 Prism Central v3 和 Prism Element v2.0 API
     */
    public static BaseResponse fetchSubnet(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 子网信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;

            // 使用资源特定的版本检测来智能选择 API
            String apiVersion = getApiVersion(client, request, "network");

            if ("v3".equals(apiVersion)) {
                // 使用 Prism Central v3 API
                log.debug("使用 Prism Central v3 API 获取子网列表");
                JSONObject requestBody = new JSONObject();
                requestBody.put("length", 500); // 获取最多500个子网

                response = client.doPostDataJSON("/subnets/list", requestBody);
                log.info("使用 Prism Central v3 API 成功获取子网信息");
            } else {
                // 使用 Prism Element v2.0 API
                log.debug("使用 Prism Element v2.0 API 获取子网列表");
                String v2ApiPath = "/PrismGateway/services/rest/v2.0/networks";
                response = client.doGetDataJSONWithFullPath(v2ApiPath);
                log.info("使用 Prism Element v2.0 API 成功获取子网信息");
            }

            return processResponse(request, response, "子网", new String[]{"networks", "subnets"},
                    resp -> Convert.convertSubnetData(request, resp));

        } catch (Exception e) {
            log.error("获取子网信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取子网信息失败"), e);
        }
    }

    /**
     * 获取虚拟交换机信息
     */
    public static BaseResponse fetchNetwork(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 虚拟交换机信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

//            JSONObject response = null;

//            // 使用资源特定的版本检测来智能选择 API
//            String apiVersion = getApiVersion(client, request, "network");
//
//            if ("v3".equals(apiVersion)) {
//                // 使用 Prism Central v3 API
//                log.debug("使用 Prism Central v3 API 获取网络列表");
//                JSONObject requestBody = new JSONObject();
//                requestBody.put("length", 500); // 获取最多500个网络
//
//                response = client.doPostDataJSON("/subnets/list", requestBody);
//                log.info("使用 Prism Central v3 API 成功获取网络信息");
//            } else {
                // 使用 Prism Element v2.0 API
                log.debug("使用 Prism Element v2.0 API 获取网络列表");
//                String v2ApiPath = "/PrismGateway/services/rest/v2.0/virtual_switches";
            String v2ApiPath = "/api/networking/v2.a1/dvs/virtual-switches";
                String response = client.doGetDataJSONStringWithFullPath(v2ApiPath);
                log.info("使用 Prism Element v2.0 API 成功获取网络信息");
            log.info("获取到的交换机数据result:{}", JSON.toJSONString(response));
//            }

            Map<Class, List> result = Convert.convertNetworkData(request, response);
            BaseResponse baseResponse = BaseCloudService.fetchSend(request, result);
            return  BaseResponse.SUCCESS;
//            return processResponse(request, response, "虚拟交换机", new String[]{"virtual_switches", "subnets"},
//                    resp -> Convert.convertNetworkData(request, resp));

        } catch (Exception e) {
            log.error("获取虚拟交换机信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟交换机信息失败"), e);
        }
    }

    /**
     * 获取任务信息
     * 从 Nutanix 平台同步任务列表数据
     * 支持 Prism Element v2.0 和 Prism Central v3 API
     */
    public static BaseResponse fetchTasks(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 任务信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;

            // 使用资源特定的版本检测来智能选择 API
            String apiVersion = getApiVersion(client, request, "task");

            if ("v3".equals(apiVersion)) {
                // 使用 Prism Central v3 API
                log.debug("使用 Prism Central v3 API 获取任务列表");
                response = client.doGetDataJSON("/tasks");
                log.info("使用 Prism Central v3 API 成功获取任务信息");
            } else {
                // 使用 Prism Element v2.0 API - 根据需求文档使用POST方法和/tasks/list端点
                log.debug("使用 Prism Element v2.0 API 获取任务列表");
                String v2ApiPath = "/PrismGateway/services/rest/v2.0/tasks/list";
                // v2.0 tasks/list API 需要使用 POST 方法
                JSONObject requestBody = new JSONObject();
                response = client.doPostDataJSONWithFullPath(v2ApiPath, requestBody);
                log.info("使用 Prism Element v2.0 API 成功获取任务信息");
            }

            // 使用通用的响应处理方法
//            return processResponse(request, response, "任务",
//                new String[]{"tasks", "task_list"}, // v2.0 API 可能的字段名
//                (resp) -> Convert.convertTaskData(request, resp));
            Map<Class, List> classListMap = Convert.convertTaskData(request, response);
            return new BaseDataResponse<>(classListMap.get(TaskInfoBean.class));

        } catch (Exception e) {
            log.error("获取任务信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取任务信息失败"), e);
        }
    }

    /**
     * 获取告警信息
     * 从 Nutanix 平台同步告警数据
     * 支持 Prism Element v2.0 和 Prism Central v3 API
     */
    public static BaseResponse fetchAlert(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 告警信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;

            // 使用资源特定的版本检测来智能选择 API
            String apiVersion = getApiVersion(client, request, "alert");

            if ("v3".equals(apiVersion)) {
                // 使用 Prism Central v3 API
                log.debug("使用 Prism Central v3 API 获取告警列表");
                JSONObject requestBody = new JSONObject();
                requestBody.put("length", 500); // 获取最多500个告警

                // 尝试多个可能的 v3 API 端点
                String[] v3Endpoints = {"/alerts/list", "/alarms/list"};
                boolean success = false;

                for (String endpoint : v3Endpoints) {
                    try {
                        log.debug("尝试 v3 API 端点: {}", endpoint);
                        response = client.doPostDataJSON(endpoint, requestBody);

                        if (response != null && response.containsKey("entities")) {
                            log.info("使用 Prism Central v3 API ({}) 成功获取告警信息", endpoint);
                            success = true;
                            break;
                        }
                    } catch (Exception endpointException) {
                        log.debug("v3 API 端点 {} 失败: {}", endpoint, endpointException.getMessage());
                    }
                }

                if (!success) {
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("所有 v3 API 端点都不可用"));
                }
            } else {
                // 使用 Prism Element v2.0 API
                log.debug("使用 Prism Element v2.0 API 获取告警列表");
                String v2ApiPath = "/PrismGateway/services/rest/v2.0/alerts";
                response = client.doGetDataJSONWithFullPath(v2ApiPath);
                log.info("使用 Prism Element v2.0 API 成功获取告警信息");
            }

            if (response != null) {
                // 处理不同 API 版本的响应格式
                JSONArray alerts = null;
                if (response.containsKey("entities")) {
                    // Prism Central v3 格式
                    alerts = response.getJSONArray("entities");
                } else if (response.containsKey("alerts")) {
                    // Prism Element v2.0 格式
                    alerts = response.getJSONArray("alerts");
                } else {
                    log.warn("未识别的告警响应格式: {}", response.keySet());
                    return BaseResponse.SUCCESS.of("未获取到告警信息");
                }

                if (alerts != null) {
                    log.info("成功获取到 {} 个告警信息", alerts.size());
                    log.debug("原始告警数据: {}", JSON.toJSONString(response));

                    // 转换数据格式并发送到消息队列
                    List<AlarmInfoBean> convertedData = Convert.convertAlertData(request, response);
                    convertedData.removeIf(Objects::isNull);
                    BaseCloudService.toAetMessageAndSend(convertedData, "alarm");
//                    BaseCloudService.fetchSend(request, convertedData);
                    log.info("告警数据已发送到消息队列");

                    return BaseResponse.SUCCESS.of("告警信息获取成功");
                } else {
                    return BaseResponse.SUCCESS.of("未获取到告警信息");
                }
            } else {
                return BaseResponse.SUCCESS.of("未获取到告警信息");
            }

        } catch (Exception e) {
            log.error("获取告警信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取告警信息失败"), e);
        }
    }

    /**
     * 获取事件信息
     * 从 Nutanix 平台同步事件数据
     * 支持 Prism Element v2.0 和 Prism Central v3 API
     */
    public static BaseResponse fetchEvent(BaseCloudRequest request) {
        try {
            log.info("开始获取 Nutanix 事件信息");

            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());

            JSONObject response = null;

            // 使用资源特定的版本检测来智能选择 API
            String apiVersion = getApiVersion(client, request, "event");

            if ("v3".equals(apiVersion)) {
                // 使用 Prism Central v3 API
                log.info("使用 Prism Central v3 API 获取事件列表");
                JSONObject requestBody = new JSONObject();
                requestBody.put("length", 500); // 获取最多500个事件

                // 尝试多个可能的 v3 API 端点
                String[] v3Endpoints = {"/audit_logs/list", "/events/list"};
                boolean success = false;

                for (String endpoint : v3Endpoints) {
                    try {
                        log.info("尝试 v3 API 端点: {}", endpoint);
                        response = client.doPostDataJSON(endpoint, requestBody);
                        log.info("尝试 v3 API response: {}", response);
                        if (response != null && response.containsKey("entities")) {
                            log.info("使用 Prism Central v3 API ({}) 成功获取事件信息", endpoint);
                            success = true;
                            break;
                        }
                    } catch (Exception endpointException) {
                        log.debug("v3 API 端点 {} 失败: {}", endpoint, endpointException.getMessage());
                    }
                }

                if (!success) {
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("所有 v3 API 端点都不可用"));
                }
            } else {
                // 使用 Prism Element v2.0 API
                log.debug("使用 Prism Element v2.0 API 获取事件列表");
                String v2ApiPath = "/PrismGateway/services/rest/v2.0/events";
                response = client.doGetDataJSONWithFullPath(v2ApiPath);
                log.info("使用 Prism Element v2.0 API 成功获取事件信息");
            }

            if (response != null) {
                // 处理不同 API 版本的响应格式
                JSONArray events = null;
                if (response.containsKey("entities")) {
                    // Prism Central v3 格式
                    events = response.getJSONArray("entities");
                } else if (response.containsKey("events")) {
                    // Prism Element v2.0 格式
                    events = response.getJSONArray("events");
                } else {
                    log.warn("未识别的事件响应格式: {}", response.keySet());
                    return BaseResponse.SUCCESS.of("未获取到事件信息");
                }

                if (events != null) {
                    log.info("成功获取到 {} 个事件信息", events.size());
                    log.debug("原始事件数据: {}", JSON.toJSONString(response));

                    // 转换数据格式并发送到消息队列
                    List<EventInfoBean> convertedData = Convert.convertEventData(request, response);
                    convertedData.removeIf(Objects::isNull);
                    BaseCloudService.toAetMessageAndSend(convertedData, "event");
//                    BaseCloudService.fetchSend(request, convertedData);
                    log.info("事件数据已发送到消息队列");

                    return BaseResponse.SUCCESS.of("事件信息获取成功");
                } else {
                    return BaseResponse.SUCCESS.of("未获取到事件信息");
                }
            } else {
                return BaseResponse.SUCCESS.of("未获取到事件信息");
            }

        } catch (Exception e) {
            log.error("获取事件信息失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取事件信息失败"), e);
        }
    }


}
