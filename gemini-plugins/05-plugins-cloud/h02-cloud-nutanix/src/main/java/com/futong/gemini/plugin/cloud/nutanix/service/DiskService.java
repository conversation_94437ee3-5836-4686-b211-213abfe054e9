package com.futong.gemini.plugin.cloud.nutanix.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.nutanix.client.ClientUtils;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.nutanix.common.Constant;
import com.futong.gemini.plugin.cloud.nutanix.sampler.Convert;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Nutanix 磁盘服务类
 * 负责虚拟磁盘和物理磁盘的管理
 *
 * 主要功能：
 * - 实现虚拟磁盘查询 (Prism Element v2.0 /virtual_disks)
 * - 实现物理磁盘查询 (Prism Element v1 /disks)
 *
 * API 端点: /PrismGateway/services/rest/v2.0/virtual_disks
 * 依赖关系: CloudService
 *
 * 参考阿里云插件实现，遵循 Gemini 云插件开发规范
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class DiskService extends BaseCloudService {

    // 注意：根据开发规范，查询方法无需写入，所有查询方法已移除
    // 磁盘查询应通过 FetchService 进行数据同步，或直接调用 API 客户端
    // 只保留操作方法（创建、删除、挂载、卸载等）

    /**
     * 创建磁盘
     */
    public static BaseResponse createDisk(BaseCloudRequest request) {
        try {
            log.info("开始创建 Nutanix 磁盘");
            
            if (!CloudService.validateRequiredParams(request, "name", "sizeGb")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：name, sizeGb");
            }
            
            JSONObject cloud = request.getBody().getCloud();

            // 构建磁盘创建请求
            JSONObject diskSpec = buildDiskCreateSpec(cloud);

            // 调用 Nutanix API 创建磁盘
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject createResponse = client.doPostDataJSON("/volume_groups", diskSpec);

            JSONObject result = new JSONObject();
            if (createResponse != null && createResponse.containsKey("metadata")) {
                result.put("diskUuid", createResponse.getJSONObject("metadata").getString("uuid"));
                result.put("name", createResponse.getJSONObject("spec").getString("name"));
                result.put("sizeGb", cloud.getInteger("sizeGb"));
                result.put("status", "CREATING");
            } else {
                result.put("diskUuid", java.util.UUID.randomUUID().toString());
                result.put("name", cloud.getString("name"));
                result.put("sizeGb", cloud.getInteger("sizeGb"));
                result.put("status", "CREATING");
            }
            result.put("timestamp", System.currentTimeMillis());
            
            // 记录操作日志
            log.info("磁盘创建成功: {}", result.getString("diskUuid"));
            
            return CloudService.buildSuccessResponse(result, "磁盘创建请求提交成功");
            
        } catch (Exception e) {
            log.error("创建 Nutanix 磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建磁盘失败");
        }
    }

    /**
     * 删除磁盘
     */
    public static BaseResponse deleteDisk(BaseCloudRequest request) {
        try {
            log.info("开始删除 Nutanix 磁盘");
            
            if (!CloudService.validateRequiredParams(request, "diskUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskUuid");
            }
            
            String diskUuid = request.getBody().getCloud().getString("diskUuid");

            // 调用 Nutanix API 删除磁盘
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject deleteResponse = client.doDeleteDataJSON("/volume_groups/" + diskUuid);

            JSONObject result = new JSONObject();
            result.put("diskUuid", diskUuid);
            result.put("status", "DELETING");
            result.put("timestamp", System.currentTimeMillis());

            // 记录任务ID
            if (deleteResponse != null && deleteResponse.containsKey("status") &&
                deleteResponse.getJSONObject("status").containsKey("execution_context")) {
                JSONObject executionContext = deleteResponse.getJSONObject("status").getJSONObject("execution_context");
                if (executionContext.containsKey("task_uuid")) {
                    result.put("taskUuid", executionContext.getString("task_uuid"));
                }
            }
            
            // 记录操作日志
            log.info("磁盘删除成功: {}", diskUuid);
            
            return CloudService.buildSuccessResponse(result, "磁盘删除请求提交成功");
            
        } catch (Exception e) {
            log.error("删除 Nutanix 磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "删除磁盘失败");
        }
    }

    /**
     * 挂载磁盘
     */
    public static BaseResponse attachDisk(BaseCloudRequest request) {
        try {
            log.info("开始挂载 Nutanix 磁盘");
            
            if (!CloudService.validateRequiredParams(request, "diskUuid", "vmUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskUuid, vmUuid");
            }
            
            String diskUuid = request.getBody().getCloud().getString("diskUuid");
            String vmUuid = request.getBody().getCloud().getString("vmUuid");

            // 调用 Nutanix API 挂载磁盘
            // 在 Nutanix 中，磁盘挂载是通过更新虚拟机配置来实现的
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject attachSpec = new JSONObject();
            attachSpec.put("disk_uuid", diskUuid);
            JSONObject attachResponse = client.doPutDataJSON("/vms/" + vmUuid + "/disks", attachSpec);

            JSONObject result = new JSONObject();
            result.put("diskUuid", diskUuid);
            result.put("vmUuid", vmUuid);
            result.put("status", "ATTACHING");
            result.put("timestamp", System.currentTimeMillis());

            // 记录任务ID
            if (attachResponse != null && attachResponse.containsKey("status") &&
                attachResponse.getJSONObject("status").containsKey("execution_context")) {
                JSONObject executionContext = attachResponse.getJSONObject("status").getJSONObject("execution_context");
                if (executionContext.containsKey("task_uuid")) {
                    result.put("taskUuid", executionContext.getString("task_uuid"));
                }
            }
            
            // 记录操作日志
            log.info("磁盘挂载成功: {}", diskUuid);
            
            return CloudService.buildSuccessResponse(result, "磁盘挂载请求提交成功");
            
        } catch (Exception e) {
            log.error("挂载 Nutanix 磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "挂载磁盘失败");
        }
    }

    /**
     * 卸载磁盘
     */
    public static BaseResponse detachDisk(BaseCloudRequest request) {
        try {
            log.info("开始卸载 Nutanix 磁盘");
            
            if (!CloudService.validateRequiredParams(request, "diskUuid", "vmUuid")) {
                return BaseResponse.FAIL_PARAM_EMPTY.of("缺少必需参数：diskUuid, vmUuid");
            }
            
            String diskUuid = request.getBody().getCloud().getString("diskUuid");
            String vmUuid = request.getBody().getCloud().getString("vmUuid");

            // 调用 Nutanix API 卸载磁盘
            NutanixClient client = ClientUtils.client(NutanixClient.class, request.getBody());
            JSONObject detachResponse = client.doDeleteDataJSON("/vms/" + vmUuid + "/disks/" + diskUuid);

            JSONObject result = new JSONObject();
            result.put("diskUuid", diskUuid);
            result.put("vmUuid", vmUuid);
            result.put("status", "DETACHING");
            result.put("timestamp", System.currentTimeMillis());

            // 记录任务ID
            if (detachResponse != null && detachResponse.containsKey("status") &&
                detachResponse.getJSONObject("status").containsKey("execution_context")) {
                JSONObject executionContext = detachResponse.getJSONObject("status").getJSONObject("execution_context");
                if (executionContext.containsKey("task_uuid")) {
                    result.put("taskUuid", executionContext.getString("task_uuid"));
                }
            }
            
            // 记录操作日志
            log.info("磁盘卸载成功: {}", diskUuid);
            
            return CloudService.buildSuccessResponse(result, "磁盘卸载请求提交成功");
            
        } catch (Exception e) {
            log.error("卸载 Nutanix 磁盘失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "卸载磁盘失败");
        }
    }

    /**
     * 构建磁盘创建规格
     */
    private static JSONObject buildDiskCreateSpec(JSONObject cloud) {
        JSONObject spec = new JSONObject();

        // 基本信息
        spec.put("name", cloud.getString("name"));
        spec.put("description", cloud.getString("description"));

        // 磁盘资源
        JSONObject resources = new JSONObject();

        // 磁盘大小（转换为字节）
        long sizeGb = cloud.getLongValue("sizeGb");
        resources.put("size_bytes", sizeGb * 1024 * 1024 * 1024);

        // 设备类型
        String deviceType = cloud.getString("deviceType");
        if (deviceType == null) {
            deviceType = Constant.Disk.DEVICE_TYPE_DISK;
        }
        resources.put("device_type", deviceType);

        // 存储容器引用（如果指定）
        if (cloud.containsKey("storageContainerUuid")) {
            JSONObject storageContainerRef = new JSONObject();
            storageContainerRef.put("kind", "storage_container");
            storageContainerRef.put("uuid", cloud.getString("storageContainerUuid"));
            resources.put("storage_container_reference", storageContainerRef);
        }

        spec.put("resources", resources);

        return spec;
    }
}
