package com.futong.gemini.plugin.cloud.nutanix.sampler;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.entity.BaseSearchApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderDevops;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevops;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsLink;
import com.futong.gemini.model.otc.bxc.entity.TmdbResourceSet;
import com.futong.gemini.model.otc.common.model.CiResCloud;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.gjc.entity.EventInfoBean;
import com.futong.gemini.model.otc.gjc.entity.PerfInfoBean;
import com.futong.gemini.model.otc.gjc.entity.TaskInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.nutanix.client.NutanixClient;
import com.futong.gemini.plugin.cloud.sdk.constant.BaseConstant;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

import java.text.DecimalFormat;
import java.util.*;

import static com.futong.gemini.plugin.cloud.nutanix.sampler.FetchService.virtualDiskMap;
/**
 * Nutanix 数据转换工具类
 * 负责将 Nutanix API 返回的数据转换为统一格式
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
public class Convert {

    /**
     * 设置 CI 资源云的通用属性
     */
    public static <T extends CiResCloud> void toCiResCloud(BaseCloudRequest request, T t) {
        t.setCloud_type(request.getPlugin().getRealm()); // 动态获取云类型
        t.setAccount_id(request.getBody().getAccess().getCmpId());
    }

    /**
     * 转换虚拟机数据为 CI 模型
     * 完整提取虚拟机的配置、状态和时间信息
     */
    public static Map<Class, List> convertVmData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (response == null || !response.containsKey("entities")) {
            log.warn("虚拟机数据响应为空或不包含entities字段");
            result.put(CmdbInstanceRes.class, new ArrayList<>());
            result.put(TmdbResourceSet.class, new ArrayList<>());
            result.put(Association.class, new ArrayList<>());
            return result;
        }

        List<CmdbBaseMetainfo> metainfos = new ArrayList<>();
        List<CmdbInstanceRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(), // 动态获取云类型
                        ResourceType.CMDB_INSTANCE_RES.value());

        JSONArray entities = response.getJSONArray("entities");
        for (int i = 0; i < entities.size(); i++) {
            JSONObject nutanixVm = entities.getJSONObject(i);
            CmdbInstanceRes vm = new CmdbInstanceRes();

            try {
                // 基本信息 - 从 metadata.uuid 获取虚拟机 UUID
//                log.info("获取虚拟机列表信息nutanixVm: {}", nutanixVm);
//                vm.setExtend3(JSON.toJSONString(nutanixVm));
                String vmUuid = null;
                if (nutanixVm.containsKey("metadata")) {
                    JSONObject metadata = nutanixVm.getJSONObject("metadata");
                    if (metadata != null && metadata.containsKey("uuid")) {
                        vmUuid = metadata.getString("uuid");
                    }
                }

                // 如果 metadata.uuid 不存在，尝试从根级别获取（兼容性处理）
                if (vmUuid == null && nutanixVm.containsKey("uuid")) {
                    vmUuid = nutanixVm.getString("uuid");
                }
                // 最后尝试从 id 字段获取
                if (vmUuid == null && nutanixVm.containsKey("id")) {
                    vmUuid = nutanixVm.getString("id");
                }

                if (vmUuid != null) {
                    vm.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vmUuid));
                    vm.setOpen_id(vmUuid);
                }

                // 虚拟机名称
                String vmName = null;
                if (nutanixVm.containsKey("spec") && nutanixVm.getJSONObject("spec").containsKey("name")) {
                    vmName = nutanixVm.getJSONObject("spec").getString("name");
                } else if (nutanixVm.containsKey("name")) {
                    vmName = nutanixVm.getString("name");
                }
                vm.setOpen_name(vmName != null ? vmName : "Unknown VM");

//                if(vmName.equals("cmptest0819")){
//                    log.info("获取虚拟机列表信息nutanixVm: {}", nutanixVm);
//                }

                // 状态信息
                if (nutanixVm.containsKey("status")) {
                    JSONObject status = nutanixVm.getJSONObject("status");



                    // 资源配置信息
                    if (status.containsKey("resources")) {
                        JSONObject resources = status.getJSONObject("resources");

                        // 电源状态
                        if (resources.containsKey("power_state")) {
                            String powerState = resources.getString("power_state");
                            vm.setStatus(convertVmStatus(powerState));
                            vm.setOpen_status(powerState);
                        } else if (status.containsKey("state")) {
                            String state = status.getString("state");
                            vm.setStatus(convertVmStatus(state));
                            vm.setOpen_status(state);
                        }


                        // CPU 配置
                        if (resources.containsKey("num_vcpus_per_socket") && resources.containsKey("num_sockets")) {
                            Integer vcpusPerSocket = resources.getInteger("num_vcpus_per_socket");
                            Integer numSockets = resources.getInteger("num_sockets");
                            if (vcpusPerSocket != null && numSockets != null) {
                                vm.setCpu_size(vcpusPerSocket * numSockets);
                            }
                        } else if (resources.containsKey("num_vcpus")) {
                            vm.setCpu_size(resources.getInteger("num_vcpus"));
                        }

                        // 内存配置 (转换为 MB)
                        if (resources.containsKey("memory_size_mib")) {
                            vm.setMem_size(resources.getInteger("memory_size_mib"));
                        } else if (resources.containsKey("memory_size_bytes")) {
                            Long memoryBytes = resources.getLong("memory_size_bytes");
                            vm.setMem_size((int) (memoryBytes / (1024 * 1024)));
                        }



                    }

                    // 集群关联
                    if (status.containsKey("cluster_reference")) {
                        JSONObject clusterRef = status.getJSONObject("cluster_reference");
                        if (clusterRef != null && clusterRef.containsKey("uuid")) {
                            builderResourceSet.withData(vm.getRes_id(), DevopsSide.DEVOPS_CLUSTER,
                                IdUtils.encryptId(request.getBody().getAccess().getCmpId(), clusterRef.getString("uuid")));
                        }
                    }
                }

                // 虚拟机规格信息
                if (nutanixVm.containsKey("spec")) {
                    JSONObject spec = nutanixVm.getJSONObject("spec");

                    // 描述信息
                    if (spec.containsKey("description")) {
                        vm.setDesc(spec.getString("description"));
                    }

                    // 检查是否为模板
                    if (spec.containsKey("resources")) {
                        JSONObject specResources = spec.getJSONObject("resources");
                        if (specResources.containsKey("is_template")) {
                            Boolean isTemplate = specResources.getBoolean("is_template");
                            vm.setIs_template(isTemplate != null && isTemplate ? 1 : 0);
                        }
                    }
                }

                // 时间信息
                if (nutanixVm.containsKey("metadata")) {
                    JSONObject metadata = nutanixVm.getJSONObject("metadata");

                    // 创建时间
                    if (metadata.containsKey("creation_time")) {
                        String creationTime = metadata.getString("creation_time");
                        try {
                            long createTimeMillis = parseNutanixTime(creationTime);
                            vm.setCreate_time(createTimeMillis);
                            vm.setOpen_create_time(createTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析虚拟机创建时间: {}", creationTime);
                        }
                    }

                    // 更新时间
                    if (metadata.containsKey("last_update_time")) {
                        String updateTime = metadata.getString("last_update_time");
                        try {
                            long updateTimeMillis = parseNutanixTime(updateTime);
                            vm.setUpdate_time(updateTimeMillis);
                            vm.setOpen_update_time(updateTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析虚拟机更新时间: {}", updateTime);
                        }
                    }
                }

                // 设置云类型和账号信息
                toCiResCloud(request, vm);
                data.add(vm);

                CmdbBaseMetainfo metainfo = new CmdbBaseMetainfo();
                metainfo.setRes_id(vm.getRes_id());
                metainfo.setTable("cmdb_instance_res");
                metainfo.setMetainfo(JSON.toJSONString(nutanixVm));
                toCiResCloud(request, metainfo);
                metainfos.add(metainfo);

            } catch (Exception e) {
                // 获取虚拟机 UUID 用于错误日志
                String errorVmUuid = "Unknown";
                try {
                    if (nutanixVm.containsKey("metadata")) {
                        JSONObject metadata = nutanixVm.getJSONObject("metadata");
                        if (metadata != null && metadata.containsKey("uuid")) {
                            errorVmUuid = metadata.getString("uuid");
                        }
                    }
                    if ("Unknown".equals(errorVmUuid) && nutanixVm.containsKey("uuid")) {
                        errorVmUuid = nutanixVm.getString("uuid");
                    }
                } catch (Exception ex) {
                    // 忽略获取 UUID 时的异常，使用默认值
                }
                log.error("转换虚拟机数据失败，虚拟机 UUID: {}", errorVmUuid, e);
            }
        }

        log.info("成功转换 {} 个虚拟机数据", data.size());
        result.put(CmdbInstanceRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换镜像数据
     */
    public static Map<String, Object> convertImageData(JSONObject nutanixImage) {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("imageId", nutanixImage.getString("uuid"));
            result.put("imageName", nutanixImage.getJSONObject("spec").getString("name"));

            JSONObject status = nutanixImage.getJSONObject("status");
            if (status != null) {
                // 尝试直接从 status 获取状态信息
                String state = status.getString("state");
                if (state != null) {
                    result.put("status", state);
                }

                // 尝试获取镜像类型信息
                String imageType = status.getString("image_type");
                if (imageType != null) {
                    result.put("imageType", imageType);
                    result.put("status", convertImageStatus(imageType));
                }

                JSONObject resources = status.getJSONObject("resources");
                if (resources != null) {
                    result.put("sizeBytes", resources.getLong("size_bytes"));
                }
            }

            result.put("createdTime", nutanixImage.getJSONObject("metadata").getString("creation_time"));

        } catch (Exception e) {
            log.error("转换镜像数据失败", e);
        }

        return result;
    }

    /**
     * 转换磁盘数据
     */
    public static Map<String, Object> convertDiskData(JSONObject nutanixDisk) {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("diskId", nutanixDisk.getString("uuid"));
            result.put("diskName", nutanixDisk.getJSONObject("spec").getString("name"));

            JSONObject status = nutanixDisk.getJSONObject("status");
            if (status != null) {
                // 尝试直接从 status 获取状态信息
                String state = status.getString("state");
                if (state != null) {
                    result.put("status", state);
                }

                // 尝试获取设备类型信息
                String deviceType = status.getString("device_type");
                if (deviceType != null) {
                    result.put("deviceType", deviceType);
                }

                JSONObject resources = status.getJSONObject("resources");
                if (resources != null) {
                    result.put("sizeBytes", resources.getLong("size_bytes"));
                }
            }

            // 挂载信息
            JSONObject vmDiskRef = nutanixDisk.getJSONObject("spec").getJSONObject("vm_disk_reference");
            if (vmDiskRef != null) {
                result.put("vmUuid", vmDiskRef.getString("vm_reference").replace("vm:", ""));
            }

            result.put("createdTime", nutanixDisk.getJSONObject("metadata").getString("creation_time"));

        } catch (Exception e) {
            log.error("转换磁盘数据失败", e);
        }

        return result;
    }

    /**
     * 转换网络数据
     */
    public static Map<String, Object> convertNetworkData(JSONObject nutanixNetwork) {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("networkId", nutanixNetwork.getString("uuid"));
            result.put("networkName", nutanixNetwork.getJSONObject("spec").getString("name"));

            JSONObject status = nutanixNetwork.getJSONObject("status");
            if (status != null) {
                // 尝试直接从 status 获取状态信息
                String state = status.getString("state");
                if (state != null) {
                    result.put("status", state);
                }

                // 尝试获取子网类型信息
                String subnetType = status.getString("subnet_type");
                if (subnetType != null) {
                    result.put("networkType", subnetType);
                }

                JSONObject resources = status.getJSONObject("resources");
                if (resources != null) {
                    result.put("vlanId", resources.getInteger("vlan_id"));

                    JSONObject ipConfig = resources.getJSONObject("ip_config");
                    if (ipConfig != null) {
                        result.put("ipConfigType", ipConfig.getString("subnet_ip"));
                    }
                }
            }

            result.put("createdTime", nutanixNetwork.getJSONObject("metadata").getString("creation_time"));

        } catch (Exception e) {
            log.error("转换网络数据失败", e);
        }

        return result;
    }

    /**
     * 转换集群数据为 CI 模型
     * Nutanix 集群数据转换，不使用区域概念
     */
    public static Map<Class, List> convertClusterData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (response == null || !response.containsKey("entities")) {
            log.warn("集群数据响应为空或不包含entities字段");
            result.put(TmdbDevops.class, new ArrayList<>());
            result.put(TmdbDevopsLink.class, new ArrayList<>());
            return result;
        }

        // 将集群信息转换为 DevOps 模型
        List<JSONObject> clusterList = new ArrayList<>();
        JSONArray entities = response.getJSONArray("entities");
        for (int i = 0; i < entities.size(); i++) {
            clusterList.add(entities.getJSONObject(i));
        }

        BuilderDevops devops = new BuilderDevops().withData(
                request.getBody().getAccess().getCmpId(),
                request.getPlugin().getRealm(), // 动态获取云类型
                DevopsSide.DEVOPS_CLUSTER.value(),
                clusterList,
                cluster -> cluster.getJSONObject("spec").getString("name"),
                cluster -> {
                    // 优先从 metadata.uuid 获取集群 UUID
                    if (cluster.containsKey("metadata")) {
                        JSONObject metadata = cluster.getJSONObject("metadata");
                        if (metadata != null && metadata.containsKey("uuid")) {
                            return metadata.getString("uuid");
                        }
                    }
                    // 兼容性处理：从根级别获取 UUID
                    return cluster.getString("uuid");
                }
        );

        result.put(TmdbDevops.class, devops.getData());
        result.put(TmdbDevopsLink.class, devops.getLink());
        return result;
    }

    /**
     * 转换主机数据为 CI 模型
     * Nutanix 主机数据转换，完整提取主机硬件和状态信息
     */
    public static Map<Class, List> convertHostData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (response == null || !response.containsKey("entities")) {
            log.warn("主机数据响应为空或不包含entities字段");
            result.put(CmdbHostRes.class, new ArrayList<>());
            result.put(TmdbResourceSet.class, new ArrayList<>());
            result.put(Association.class, new ArrayList<>());
            return result;
        }

        List<CmdbHostRes> data = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        BuilderResourceSet builderResourceSet = BuilderResourceSet.of()
                .withInfoString(request.getBody().getAccess().getCmpId(),
                        request.getPlugin().getRealm(), // 动态获取云类型
                        ResourceType.CMDB_HOST_RES.value());

        JSONArray entities = response.getJSONArray("entities");
        for (int i = 0; i < entities.size(); i++) {
            JSONObject nutanixHost = entities.getJSONObject(i);
            CmdbHostRes host = new CmdbHostRes();

            try {
                // 基本信息 - 从 metadata.uuid 获取主机 UUID
                String hostUuid = null;
                if (nutanixHost.containsKey("metadata")) {
                    JSONObject metadata = nutanixHost.getJSONObject("metadata");
                    if (metadata != null && metadata.containsKey("uuid")) {
                        hostUuid = metadata.getString("uuid");
                    }
                }

                // 如果 metadata.uuid 不存在，尝试从根级别获取（兼容性处理）
                if (hostUuid == null && nutanixHost.containsKey("uuid")) {
                    hostUuid = nutanixHost.getString("uuid");
                }

                if (hostUuid != null) {
                    host.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), hostUuid));
                    host.setOpen_id(hostUuid);
                }

                // 主机名称
                String hostName = null;
                if (nutanixHost.containsKey("spec") && nutanixHost.getJSONObject("spec").containsKey("name")) {
                    hostName = nutanixHost.getJSONObject("spec").getString("name");
                } else if (nutanixHost.containsKey("name")) {
                    hostName = nutanixHost.getString("name");
                }
                host.setOpen_name(hostName != null ? hostName : "Unknown Host");

                // 状态信息
                JSONObject status = nutanixHost.getJSONObject("status");
                if (status != null) {
                    // 主机状态转换
                    String state = status.getString("state");
                    if (state != null) {
                        host.setStatus(convertHostStatus(state)); // 转换为标准状态
                        host.setOpen_status(state); // 保留原始状态
                    }

                    // 硬件资源信息
                    if (status.containsKey("resources")) {
                        JSONObject resources = status.getJSONObject("resources");

                        // CPU 信息
                        if (resources.containsKey("num_cpu_cores")) {
                            host.setCpu_size(resources.getInteger("num_cpu_cores"));
                        }

                        // 内存信息 (转换为 MB)
                        if (resources.containsKey("memory_capacity_mib")) {
                            host.setMem_size(resources.getInteger("memory_capacity_mib"));
                        }

                        // 存储容量信息 (转换为 GB)
                        if (resources.containsKey("storage_capacity_bytes")) {
                            Long storageBytes = resources.getLong("storage_capacity_bytes");
                            host.setTotal_size(storageBytes / (1024.0f * 1024.0f * 1024.0f));
                        }

                        // 主机 IP 地址
                        if (resources.containsKey("hypervisor") && resources.getJSONObject("hypervisor").containsKey("ip")) {
                            host.setIp(resources.getJSONObject("hypervisor").getString("ip"));
                        }

                        // 序列号
                        if (resources.containsKey("serial_number")) {
                            host.setSn(resources.getString("serial_number"));
                        }

                        // 主机型号和厂商信息
                        if (resources.containsKey("hardware_info")) {
                            JSONObject hardwareInfo = resources.getJSONObject("hardware_info");
                            if (hardwareInfo.containsKey("model")) {
                                host.setModel(hardwareInfo.getString("model"));
                            }
                            if (hardwareInfo.containsKey("vendor")) {
                                host.setManufacturer(hardwareInfo.getString("vendor"));
                            }
                        }
                    }

                    // 集群关联
                    if (status.containsKey("cluster_reference")) {
                        JSONObject clusterRef = status.getJSONObject("cluster_reference");
                        if (clusterRef != null && clusterRef.containsKey("uuid")) {
                            builderResourceSet.withData(host.getRes_id(), DevopsSide.DEVOPS_CLUSTER,
                                IdUtils.encryptId(request.getBody().getAccess().getCmpId(), clusterRef.getString("uuid")));
                        }
                    }
                }

                // 时间信息
                if (nutanixHost.containsKey("metadata")) {
                    JSONObject metadata = nutanixHost.getJSONObject("metadata");

                    // 创建时间
                    if (metadata.containsKey("creation_time")) {
                        String creationTime = metadata.getString("creation_time");
                        try {
                            // Nutanix 时间格式通常是 ISO 8601 或时间戳
                            long createTimeMillis = parseNutanixTime(creationTime);
                            host.setCreate_time(createTimeMillis);
                            host.setOpen_create_time(createTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析主机创建时间: {}", creationTime);
                        }
                    }

                    // 更新时间
                    if (metadata.containsKey("last_update_time")) {
                        String updateTime = metadata.getString("last_update_time");
                        try {
                            long updateTimeMillis = parseNutanixTime(updateTime);
                            host.setUpdate_time(updateTimeMillis);
                            host.setOpen_update_time(updateTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析主机更新时间: {}", updateTime);
                        }
                    }
                }

                // 维护模式检查
                if (nutanixHost.containsKey("spec")) {
                    JSONObject spec = nutanixHost.getJSONObject("spec");
                    if (spec.containsKey("resources") && spec.getJSONObject("resources").containsKey("maintenance_mode")) {
                        Boolean maintenanceMode = spec.getJSONObject("resources").getBoolean("maintenance_mode");
                        host.setMaintain_mode(maintenanceMode != null && maintenanceMode ? "MAINTENANCE" : "NORMAL");
                    }
                }

                // 在线状态 (基于主机状态判断)
                String hostStatus = host.getStatus();
                if ("ONLINE".equals(hostStatus) || "NORMAL".equals(hostStatus)) {
                    host.setOn_line("ONLINE");
                } else {
                    host.setOn_line("OFFLINE");
                }

                // 设置云类型和账号信息
                toCiResCloud(request, host);
                data.add(host);

            } catch (Exception e) {
                // 获取主机 UUID 用于错误日志
                String errorHostUuid = "Unknown";
                try {
                    if (nutanixHost.containsKey("metadata")) {
                        JSONObject metadata = nutanixHost.getJSONObject("metadata");
                        if (metadata != null && metadata.containsKey("uuid")) {
                            errorHostUuid = metadata.getString("uuid");
                        }
                    }
                    if ("Unknown".equals(errorHostUuid) && nutanixHost.containsKey("uuid")) {
                        errorHostUuid = nutanixHost.getString("uuid");
                    }
                } catch (Exception ex) {
                    // 忽略获取 UUID 时的异常，使用默认值
                }
                log.error("转换主机数据失败，主机 UUID: {}", errorHostUuid, e);
            }
        }

        log.info("成功转换 {} 个主机数据", data.size());
        result.put(CmdbHostRes.class, data);
        result.put(TmdbResourceSet.class, builderResourceSet.getData());
        result.put(Association.class, associations);
        return result;
    }

    /**
     * 转换快照数据
     */
    public static Map<String, Object> convertSnapshotData(JSONObject nutanixSnapshot) {
        Map<String, Object> result = new HashMap<>();

        try {
            result.put("snapshotId", nutanixSnapshot.getString("uuid"));
            result.put("snapshotName", nutanixSnapshot.getJSONObject("spec").getString("name"));

            JSONObject status = nutanixSnapshot.getJSONObject("status");
            if (status != null) {
                JSONObject resources = status.getJSONObject("resources");
                if (resources != null) {
                    // VM 引用
                    JSONObject vmRef = resources.getJSONObject("vm_reference");
                    if (vmRef != null) {
                        result.put("vmUuid", vmRef.getString("uuid"));
                    }

                    // 快照大小
                    if (resources.containsKey("size_bytes")) {
                        result.put("sizeBytes", resources.getLong("size_bytes"));
                    }
                }
            }

            // 创建时间
            result.put("createdTime", nutanixSnapshot.getJSONObject("metadata").getString("creation_time"));

        } catch (Exception e) {
            log.error("转换快照数据失败", e);
        }

        return result;
    }

    // ==================== 状态转换辅助方法 ====================

    /**
     * 转换虚拟机状态
     * 处理 Nutanix 虚拟机的两种状态类型：
     * 1. power_state: ON/OFF/PAUSED (电源状态)
     * 2. state: COMPLETE/PENDING/ERROR 等 (生命周期状态)
     *
     * @param status 状态值，可能是 power_state 或 state
     * @return 标准化的虚拟机状态
     */
    private static String convertVmStatus(String status) {
        if (status == null) return InstanceStatus.UNKNOWN.value();

        switch (status.toUpperCase()) {
            // 电源状态映射
            case "ON":
                return InstanceStatus.RUNNING.value();
            case "OFF":
                return InstanceStatus.STOPPED.value();
            case "PAUSED":
                return InstanceStatus.SUSPENDED.value();

            // 生命周期状态映射
            case "COMPLETE":
                // COMPLETE 表示虚拟机已完成创建并处于正常状态
                return InstanceStatus.RUNNING.value();
            case "PENDING":
                // PENDING 表示虚拟机正在创建或配置中
                return InstanceStatus.BUILDING.value();
            case "ERROR":
            case "FAILED":
                return InstanceStatus.ERROR.value();
            case "CREATING":
                return InstanceStatus.BUILDING.value();
            case "STARTING":
                return InstanceStatus.STARTING.value();
            case "STOPPING":
                return InstanceStatus.STOPPING.value();
            case "DELETING":
                return InstanceStatus.DELETING.value();
            case "MIGRATING":
                return InstanceStatus.MIGRATING.value();

            default:
                return InstanceStatus.UNKNOWN.value();
        }
    }

    private static String convertImageStatus(String imageType) {
        if (imageType == null) return "UNKNOWN";

        switch (imageType.toUpperCase()) {
            case "DISK_IMAGE":
            case "ISO_IMAGE":
                return "ACTIVE";
            default:
                return "INACTIVE";
        }
    }

    private static String convertClusterStatus(String serviceList) {
        if (serviceList == null || serviceList.isEmpty()) {
            return "INACTIVE";
        }
        return "ACTIVE";
    }

    /**
     * 转换虚拟机性能数据
     */
    public static Map<String, Object> convertVmPerfData(JSONObject nutanixVm, JSONObject perfData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 基本虚拟机信息
            result.put("instanceId", nutanixVm.getJSONObject("metadata").getString("uuid"));
            result.put("instanceName", nutanixVm.getJSONObject("spec").getString("name"));

            // 时间戳
            result.put("timestamp", System.currentTimeMillis());

            // 性能指标数据
            if (perfData != null && perfData.containsKey("stats")) {
                JSONObject stats = perfData.getJSONObject("stats");

                // CPU 使用率 (百万分之一转换为百分比)
                if (stats.containsKey("hypervisor_cpu_usage_ppm")) {
                    double cpuUsagePpm = stats.getDoubleValue("hypervisor_cpu_usage_ppm");
                    result.put("cpuUsagePercent", cpuUsagePpm / 10000.0); // ppm 转换为百分比
                }

                // 内存使用率
                if (stats.containsKey("hypervisor_memory_usage_ppm")) {
                    double memoryUsagePpm = stats.getDoubleValue("hypervisor_memory_usage_ppm");
                    result.put("memoryUsagePercent", memoryUsagePpm / 10000.0);
                }

                // 磁盘 IOPS
                if (stats.containsKey("controller_num_iops")) {
                    result.put("diskIops", stats.getIntValue("controller_num_iops"));
                }

                // 磁盘吞吐量 (字节/秒)
                if (stats.containsKey("controller_io_bandwidth_kBps")) {
                    long bandwidthKBps = stats.getLongValue("controller_io_bandwidth_kBps");
                    result.put("diskThroughputBps", bandwidthKBps * 1024); // KB/s 转换为 B/s
                }

                // 网络接收字节数
                if (stats.containsKey("hypervisor_num_received_bytes")) {
                    result.put("networkInBytes", stats.getLongValue("hypervisor_num_received_bytes"));
                }

                // 网络发送字节数
                if (stats.containsKey("hypervisor_num_transmitted_bytes")) {
                    result.put("networkOutBytes", stats.getLongValue("hypervisor_num_transmitted_bytes"));
                }
            }

            // 如果没有性能数据，设置默认值
            if (!result.containsKey("cpuUsagePercent")) {
                result.put("cpuUsagePercent", 0.0);
            }
            if (!result.containsKey("memoryUsagePercent")) {
                result.put("memoryUsagePercent", 0.0);
            }
            if (!result.containsKey("diskIops")) {
                result.put("diskIops", 0);
            }
            if (!result.containsKey("diskThroughputBps")) {
                result.put("diskThroughputBps", 0L);
            }
            if (!result.containsKey("networkInBytes")) {
                result.put("networkInBytes", 0L);
            }
            if (!result.containsKey("networkOutBytes")) {
                result.put("networkOutBytes", 0L);
            }

        } catch (Exception e) {
            log.error("转换虚拟机性能数据失败", e);
            // 返回基本信息，即使性能数据转换失败
            result.put("instanceId", nutanixVm.getJSONObject("metadata").getString("uuid"));
            result.put("timestamp", System.currentTimeMillis());
        }

        return result;
    }

    /**
     * 转换主机性能数据
     */
    public static Map<String, Object> convertHostPerfData(JSONObject nutanixHost, JSONObject perfData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 基本主机信息
            result.put("hostId", nutanixHost.getJSONObject("metadata").getString("uuid"));
            result.put("hostName", nutanixHost.getJSONObject("spec").getString("name"));

            // 时间戳
            result.put("timestamp", System.currentTimeMillis());

            // 性能指标数据
            if (perfData != null && perfData.containsKey("stats")) {
                JSONObject stats = perfData.getJSONObject("stats");

                // CPU 使用率
                if (stats.containsKey("hypervisor_cpu_usage_ppm")) {
                    double cpuUsagePpm = stats.getDoubleValue("hypervisor_cpu_usage_ppm");
                    result.put("cpuUsagePercent", cpuUsagePpm / 10000.0);
                }

                // 内存使用率
                if (stats.containsKey("hypervisor_memory_usage_ppm")) {
                    double memoryUsagePpm = stats.getDoubleValue("hypervisor_memory_usage_ppm");
                    result.put("memoryUsagePercent", memoryUsagePpm / 10000.0);
                }

                // 存储 IOPS
                if (stats.containsKey("controller_num_iops")) {
                    result.put("storageIops", stats.getIntValue("controller_num_iops"));
                }

                // 网络吞吐量
                if (stats.containsKey("hypervisor_num_received_bytes")) {
                    result.put("networkInBytes", stats.getLongValue("hypervisor_num_received_bytes"));
                }
                if (stats.containsKey("hypervisor_num_transmitted_bytes")) {
                    result.put("networkOutBytes", stats.getLongValue("hypervisor_num_transmitted_bytes"));
                }
            }

            // 设置默认值
            if (!result.containsKey("cpuUsagePercent")) {
                result.put("cpuUsagePercent", 0.0);
            }
            if (!result.containsKey("memoryUsagePercent")) {
                result.put("memoryUsagePercent", 0.0);
            }
            if (!result.containsKey("storageIops")) {
                result.put("storageIops", 0);
            }
            if (!result.containsKey("networkInBytes")) {
                result.put("networkInBytes", 0L);
            }
            if (!result.containsKey("networkOutBytes")) {
                result.put("networkOutBytes", 0L);
            }

        } catch (Exception e) {
            log.error("转换主机性能数据失败", e);
            result.put("hostId", nutanixHost.getJSONObject("metadata").getString("uuid"));
            result.put("timestamp", System.currentTimeMillis());
        }

        return result;
    }

    /**
     * 转换主机资源使用情况
     */
    public static JSONObject convertHostResourceUsage(JSONObject hostInfo, JSONObject perfData) {
        JSONObject result = new JSONObject();

        try {
            // 基本信息
            result.put("hostId", hostInfo.getJSONObject("metadata").getString("uuid"));
            result.put("hostName", hostInfo.getJSONObject("spec").getString("name"));
            result.put("timestamp", System.currentTimeMillis());

            // 硬件信息
            if (hostInfo.containsKey("status") && hostInfo.getJSONObject("status").containsKey("resources")) {
                JSONObject resources = hostInfo.getJSONObject("status").getJSONObject("resources");

                // CPU 信息
                if (resources.containsKey("cpu_capacity_hz")) {
                    result.put("cpuCapacityHz", resources.getLongValue("cpu_capacity_hz"));
                }
                if (resources.containsKey("num_cpu_cores")) {
                    result.put("cpuCores", resources.getIntValue("num_cpu_cores"));
                }

                // 内存信息
                if (resources.containsKey("memory_capacity_mib")) {
                    result.put("memoryCapacityMib", resources.getLongValue("memory_capacity_mib"));
                }
            }

            // 性能数据
            if (perfData != null && perfData.containsKey("stats")) {
                JSONObject stats = perfData.getJSONObject("stats");

                // CPU 使用情况
                if (stats.containsKey("hypervisor_cpu_usage_ppm")) {
                    double cpuUsagePpm = stats.getDoubleValue("hypervisor_cpu_usage_ppm");
                    result.put("cpuUsagePercent", cpuUsagePpm / 10000.0);
                }

                // 内存使用情况
                if (stats.containsKey("hypervisor_memory_usage_ppm")) {
                    double memoryUsagePpm = stats.getDoubleValue("hypervisor_memory_usage_ppm");
                    result.put("memoryUsagePercent", memoryUsagePpm / 10000.0);
                }
            }

        } catch (Exception e) {
            log.error("转换主机资源使用情况失败", e);
            result.put("hostId", hostInfo.getJSONObject("metadata").getString("uuid"));
            result.put("timestamp", System.currentTimeMillis());
        }

        return result;
    }

    /**
     * 转换集群资源使用情况
     */
    public static JSONObject convertClusterResourceUsage(JSONObject clusterInfo, JSONObject statsData) {
        JSONObject result = new JSONObject();

        try {
            // 基本信息
            result.put("clusterId", clusterInfo.getJSONObject("metadata").getString("uuid"));
            result.put("clusterName", clusterInfo.getJSONObject("spec").getString("name"));
            result.put("timestamp", System.currentTimeMillis());

            // 集群配置信息
            if (clusterInfo.containsKey("status") && clusterInfo.getJSONObject("status").containsKey("resources")) {
                JSONObject resources = clusterInfo.getJSONObject("status").getJSONObject("resources");

                // 节点数量
                if (resources.containsKey("nodes")) {
                    result.put("nodeCount", resources.getJSONArray("nodes").size());
                }

                // 集群版本
                if (resources.containsKey("config")) {
                    JSONObject config = resources.getJSONObject("config");
                    if (config.containsKey("software_map")) {
                        result.put("softwareVersion", config.getJSONObject("software_map"));
                    }
                }
            }

            // 性能统计数据
            if (statsData != null && statsData.containsKey("stats")) {
                JSONObject stats = statsData.getJSONObject("stats");

                // CPU 统计
                if (stats.containsKey("hypervisor_cpu_usage_ppm")) {
                    double cpuUsagePpm = stats.getDoubleValue("hypervisor_cpu_usage_ppm");
                    result.put("cpuUsagePercent", cpuUsagePpm / 10000.0);
                }

                // 内存统计
                if (stats.containsKey("hypervisor_memory_usage_ppm")) {
                    double memoryUsagePpm = stats.getDoubleValue("hypervisor_memory_usage_ppm");
                    result.put("memoryUsagePercent", memoryUsagePpm / 10000.0);
                }

                // 存储统计
                if (stats.containsKey("storage_capacity_bytes")) {
                    result.put("storageCapacityBytes", stats.getLongValue("storage_capacity_bytes"));
                }
                if (stats.containsKey("storage_usage_bytes")) {
                    result.put("storageUsageBytes", stats.getLongValue("storage_usage_bytes"));
                }

                // IOPS 统计
                if (stats.containsKey("controller_num_iops")) {
                    result.put("totalIops", stats.getIntValue("controller_num_iops"));
                }

                // 网络统计
                if (stats.containsKey("hypervisor_num_received_bytes")) {
                    result.put("networkInBytes", stats.getLongValue("hypervisor_num_received_bytes"));
                }
                if (stats.containsKey("hypervisor_num_transmitted_bytes")) {
                    result.put("networkOutBytes", stats.getLongValue("hypervisor_num_transmitted_bytes"));
                }
            }

            // 计算存储使用率
            if (result.containsKey("storageCapacityBytes") && result.containsKey("storageUsageBytes")) {
                long capacity = result.getLongValue("storageCapacityBytes");
                long usage = result.getLongValue("storageUsageBytes");
                if (capacity > 0) {
                    double usagePercent = (double) usage / capacity * 100;
                    result.put("storageUsagePercent", usagePercent);
                }
            }

        } catch (Exception e) {
            log.error("转换集群资源使用情况失败", e);
            result.put("clusterId", clusterInfo.getJSONObject("metadata").getString("uuid"));
            result.put("timestamp", System.currentTimeMillis());
        }

        return result;
    }

    /**
     * 转换存储容器使用情况
     */
    public static JSONObject convertStorageContainerUsage(JSONObject containerInfo, JSONObject statsData) {
        JSONObject result = new JSONObject();

        try {
            // 基本信息
            result.put("containerId", containerInfo.getString("storage_container_uuid"));
            result.put("containerName", containerInfo.getString("name"));
            result.put("timestamp", System.currentTimeMillis());

            // 容器配置信息
            if (containerInfo.containsKey("max_capacity")) {
                result.put("maxCapacityBytes", containerInfo.getLongValue("max_capacity"));
            }
            if (containerInfo.containsKey("advertised_capacity")) {
                result.put("advertisedCapacityBytes", containerInfo.getLongValue("advertised_capacity"));
            }
            if (containerInfo.containsKey("replication_factor")) {
                result.put("replicationFactor", containerInfo.getIntValue("replication_factor"));
            }

            // 压缩和去重配置
            if (containerInfo.containsKey("compression_enabled")) {
                result.put("compressionEnabled", containerInfo.getBooleanValue("compression_enabled"));
            }
            if (containerInfo.containsKey("finger_print_on_write")) {
                result.put("deduplicationEnabled", containerInfo.getBooleanValue("finger_print_on_write"));
            }

            // 统计数据
            if (statsData != null && statsData.containsKey("stats")) {
                JSONObject stats = statsData.getJSONObject("stats");

                // 容量使用情况
                if (stats.containsKey("storage.usage_bytes")) {
                    result.put("usageBytes", stats.getLongValue("storage.usage_bytes"));
                }
                if (stats.containsKey("storage.capacity_bytes")) {
                    result.put("capacityBytes", stats.getLongValue("storage.capacity_bytes"));
                }

                // IOPS 统计
                if (stats.containsKey("controller.num_iops")) {
                    result.put("iops", stats.getIntValue("controller.num_iops"));
                }
                if (stats.containsKey("controller.num_read_iops")) {
                    result.put("readIops", stats.getIntValue("controller.num_read_iops"));
                }
                if (stats.containsKey("controller.num_write_iops")) {
                    result.put("writeIops", stats.getIntValue("controller.num_write_iops"));
                }

                // 吞吐量统计
                if (stats.containsKey("controller.io_bandwidth_kBps")) {
                    long bandwidthKBps = stats.getLongValue("controller.io_bandwidth_kBps");
                    result.put("throughputBps", bandwidthKBps * 1024);
                }

                // 延迟统计
                if (stats.containsKey("controller.avg_io_latency_usecs")) {
                    result.put("avgLatencyUsecs", stats.getIntValue("controller.avg_io_latency_usecs"));
                }
            }

            // 计算使用率
            if (result.containsKey("capacityBytes") && result.containsKey("usageBytes")) {
                long capacity = result.getLongValue("capacityBytes");
                long usage = result.getLongValue("usageBytes");
                if (capacity > 0) {
                    double usagePercent = (double) usage / capacity * 100;
                    result.put("usagePercent", usagePercent);
                }
            }

            // 设置默认值
            if (!result.containsKey("usageBytes")) {
                result.put("usageBytes", 0L);
            }
            if (!result.containsKey("capacityBytes")) {
                result.put("capacityBytes", 0L);
            }
            if (!result.containsKey("iops")) {
                result.put("iops", 0);
            }
            if (!result.containsKey("throughputBps")) {
                result.put("throughputBps", 0L);
            }

        } catch (Exception e) {
            log.error("转换存储容器使用情况失败", e);
            result.put("containerId", containerInfo.getString("storage_container_uuid"));
            result.put("timestamp", System.currentTimeMillis());
        }

        return result;
    }

    /**
     * 转换虚拟交换机数据
     */
    public static Map<String, Object> convertVirtualSwitchData(JSONObject nutanixSwitch) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 基本信息
            result.put("switchId", nutanixSwitch.getString("uuid"));
            result.put("switchName", nutanixSwitch.getJSONObject("spec").getString("name"));

            // 描述信息
            if (nutanixSwitch.getJSONObject("spec").containsKey("description")) {
                result.put("description", nutanixSwitch.getJSONObject("spec").getString("description"));
            }

            // 状态信息
            JSONObject status = nutanixSwitch.getJSONObject("status");
            if (status != null) {
                // 尝试直接从 status 获取状态信息
                String state = status.getString("state");
                if (state != null) {
                    result.put("status", convertSwitchStatus(state));
                }

                // 资源信息
                JSONObject resources = status.getJSONObject("resources");
                if (resources != null) {
                    // 网络类型
                    if (resources.containsKey("subnet_type")) {
                        result.put("networkType", resources.getString("subnet_type"));
                    }

                    // VLAN ID
                    if (resources.containsKey("vlan_id")) {
                        result.put("vlanId", resources.getInteger("vlan_id"));
                    }

                    // IP 配置
                    if (resources.containsKey("ip_config")) {
                        JSONObject ipConfig = resources.getJSONObject("ip_config");
                        result.put("ipConfig", ipConfig);

                        // 子网信息
                        if (ipConfig.containsKey("subnet_ip")) {
                            result.put("subnetIp", ipConfig.getString("subnet_ip"));
                        }
                        if (ipConfig.containsKey("prefix_length")) {
                            result.put("prefixLength", ipConfig.getInteger("prefix_length"));
                        }
                        if (ipConfig.containsKey("default_gateway_ip")) {
                            result.put("gatewayIp", ipConfig.getString("default_gateway_ip"));
                        }
                    }

                    // 集群引用
                    if (resources.containsKey("cluster_reference")) {
                        JSONObject clusterRef = resources.getJSONObject("cluster_reference");
                        result.put("clusterUuid", clusterRef.getString("uuid"));
                    }
                }
            }

            // 创建时间
            if (nutanixSwitch.containsKey("metadata")) {
                JSONObject metadata = nutanixSwitch.getJSONObject("metadata");
                if (metadata.containsKey("creation_time")) {
                    result.put("createdTime", metadata.getString("creation_time"));
                }
            }

            // 设置默认值
            if (!result.containsKey("status")) {
                result.put("status", "UNKNOWN");
            }
            if (!result.containsKey("networkType")) {
                result.put("networkType", "VLAN");
            }

        } catch (Exception e) {
            log.error("转换虚拟交换机数据失败", e);
            // 返回基本信息，即使转换失败
            if (nutanixSwitch.containsKey("uuid")) {
                result.put("switchId", nutanixSwitch.getString("uuid"));
            }
            if (nutanixSwitch.containsKey("spec") &&
                nutanixSwitch.getJSONObject("spec").containsKey("name")) {
                result.put("switchName", nutanixSwitch.getJSONObject("spec").getString("name"));
            }
            result.put("status", "UNKNOWN");
        }

        return result;
    }

    /**
     * 转换虚拟交换机状态
     */
    private static String convertSwitchStatus(String state) {
        if (state == null) return "UNKNOWN";

        switch (state.toUpperCase()) {
            case "COMPLETE":
            case "ACTIVE":
                return "ACTIVE";
            case "PENDING":
                return "PENDING";
            case "ERROR":
            case "FAILED":
                return "ERROR";
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 转换磁盘数据为 CI 模型
     * 完整提取磁盘的配置、状态、容量和时间信息
     */
    public static Map<Class, List> convertDiskData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();

        // 处理不同 API 版本的响应格式
        JSONArray disks = null;
        if (response != null) {
            if (response.containsKey("entities")) {
                // Prism Central v3 或 Prism Element v2.0 格式
                disks = response.getJSONArray("entities");
            } else if (response.containsKey("virtual_disks")) {
                // 某些 v2.0 API 可能使用不同的字段名
                disks = response.getJSONArray("virtual_disks");
            }
        }
        log.info("原始虚拟磁盘数据: {}", JSON.toJSONString(response));

        if (disks == null || disks.isEmpty()) {
            result.put(CmdbDiskRes.class, new ArrayList<>());
            return result;
        }

        List<CmdbDiskRes> data = new ArrayList<>();

        for (int i = 0; i < disks.size(); i++) {
            JSONObject nutanixDisk = disks.getJSONObject(i);
            CmdbDiskRes disk = new CmdbDiskRes();

            try {
                // 基本信息 - 从 metadata.uuid 获取磁盘 UUID
                String diskUuid = null;
                if (nutanixDisk.containsKey("metadata")) {
                    JSONObject metadata = nutanixDisk.getJSONObject("metadata");
                    if (metadata != null && metadata.containsKey("uuid")) {
                        diskUuid = metadata.getString("uuid");
                    }
                }

                // 如果 metadata.uuid 不存在，尝试从根级别获取（兼容性处理）
                if (diskUuid == null && nutanixDisk.containsKey("uuid")) {
                    diskUuid = nutanixDisk.getString("uuid");
                }

                // 最后尝试从 id 字段获取
                if (diskUuid == null && nutanixDisk.containsKey("id")) {
                    diskUuid = nutanixDisk.getString("id");
                }

                if (diskUuid != null) {
                    disk.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), diskUuid));
                    disk.setOpen_id(diskUuid);
                }

                // 磁盘名称
                String diskName = null;
                if (nutanixDisk.containsKey("spec") && nutanixDisk.getJSONObject("spec").containsKey("name")) {
                    diskName = nutanixDisk.getJSONObject("spec").getString("name");
                } else if (nutanixDisk.containsKey("name")) {
                    diskName = nutanixDisk.getString("name");
                } else if (nutanixDisk.containsKey("disk_label")) {
                    diskName = nutanixDisk.getString("disk_label");
                }
                disk.setOpen_name(diskName != null ? diskName : "Unknown Disk");

                // 状态信息
                if (nutanixDisk.containsKey("status")) {
                    JSONObject status = nutanixDisk.getJSONObject("status");

                    // 磁盘状态
                    if (status.containsKey("state")) {
                        String state = status.getString("state");
                        disk.setStatus(convertDiskStatus(state));
                        disk.setOpen_status(state);
                    }

                    // 磁盘资源信息
                    if (status.containsKey("resources")) {
                        JSONObject resources = status.getJSONObject("resources");

                        // 磁盘大小 (转换为 GB) - 优先使用 size_bytes
                        if (resources.containsKey("size_bytes")) {
                            Long diskSizeBytes = resources.getLong("size_bytes");
                            disk.setSize(diskSizeBytes / (1024.0f * 1024.0f * 1024.0f));
                        } else if (resources.containsKey("disk_size_bytes")) {
                            Long diskSizeBytes = resources.getLong("disk_size_bytes");
                            disk.setSize(diskSizeBytes / (1024.0f * 1024.0f * 1024.0f));
                        } else if (resources.containsKey("disk_size_mib")) {
                            Integer diskSizeMib = resources.getInteger("disk_size_mib");
                            disk.setSize(diskSizeMib / 1024.0f);
                        }

                        // 存储层级类型
                        if (resources.containsKey("storage_tier_type")) {
                            String tierType = resources.getString("storage_tier_type");
                            disk.setType(tierType); // HDD, SSD 等
                        }

                        // 序列号
                        if (resources.containsKey("serial_number")) {
                            disk.setExtend1(resources.getString("serial_number"));
                        }

                        // 型号
                        if (resources.containsKey("model")) {
                            disk.setExtend2(resources.getString("model"));
                        }

                        // 固件版本
                        if (resources.containsKey("firmware_version")) {
                            disk.setExtend3(resources.getString("firmware_version"));
                        }

                        // 插槽号
                        if (resources.containsKey("slot_number")) {
                            disk.setLun_id(resources.getString("slot_number"));
                        }

                        // 厂商信息
                        if (resources.containsKey("vendor")) {
                            String vendor = resources.getString("vendor");
                            if (disk.getDesc() != null) {
                                disk.setDesc(disk.getDesc() + " | Vendor: " + vendor);
                            } else {
                                disk.setDesc("Vendor: " + vendor);
                            }
                        }

                        // 挂载路径
                        if (resources.containsKey("mount_path")) {
                            disk.setPath(resources.getString("mount_path"));
                        }

                        // 存储池 UUID
                        if (resources.containsKey("storage_pool_uuid")) {
                            disk.setStore_name(resources.getString("storage_pool_uuid"));
                        }

                        // 状态列表 (如 ["ONLINE", "MOUNTED"])
                        if (resources.containsKey("state_list")) {
                            JSONArray stateList = resources.getJSONArray("state_list");
                            if (stateList != null && !stateList.isEmpty()) {
                                StringBuilder states = new StringBuilder();
                                for (int j = 0; j < stateList.size(); j++) {
                                    if (j > 0) states.append(", ");
                                    states.append(stateList.getString(j));
                                }
                                String currentDesc = disk.getDesc();
                                if (currentDesc != null) {
                                    disk.setDesc(currentDesc + " | States: " + states.toString());
                                } else {
                                    disk.setDesc("States: " + states.toString());
                                }
                            }
                        }

                        // 启用的功能列表
                        if (resources.containsKey("enabled_features_list")) {
                            JSONArray featuresList = resources.getJSONArray("enabled_features_list");
                            if (featuresList != null && !featuresList.isEmpty()) {
                                StringBuilder features = new StringBuilder();
                                for (int j = 0; j < featuresList.size(); j++) {
                                    if (j > 0) features.append(", ");
                                    features.append(featuresList.getString(j));
                                }
                                String currentDesc = disk.getDesc();
                                if (currentDesc != null) {
                                    disk.setDesc(currentDesc + " | Features: " + features.toString());
                                } else {
                                    disk.setDesc("Features: " + features.toString());
                                }
                            }
                        }

                        // 主机关联
                        if (resources.containsKey("host_reference")) {
                            JSONObject hostRef = resources.getJSONObject("host_reference");
                            if (hostRef != null && hostRef.containsKey("uuid")) {
                                // 可以用于后续的关联关系建立
                                String hostUuid = hostRef.getString("uuid");
                                // 这里可以存储到扩展字段或用于关联关系
                            }
                        }

                        // 磁盘类型
                        if (resources.containsKey("device_properties")) {
                            JSONObject deviceProps = resources.getJSONObject("device_properties");
                            if (deviceProps.containsKey("device_type")) {
                                disk.setType(deviceProps.getString("device_type"));
                            }
                            if (deviceProps.containsKey("disk_address")) {
                                JSONObject diskAddress = deviceProps.getJSONObject("disk_address");
                                if (diskAddress.containsKey("device_index")) {
                                    disk.setLun_id(diskAddress.getString("device_index"));
                                }
                            }
                        }

                        // 存储容器信息
                        if (resources.containsKey("storage_container_reference")) {
                            JSONObject containerRef = resources.getJSONObject("storage_container_reference");
                            if (containerRef.containsKey("name")) {
                                disk.setStore_name(containerRef.getString("name"));
                            }
                        }
                    }
                }

                // 磁盘规格信息
                if (nutanixDisk.containsKey("spec")) {
                    JSONObject spec = nutanixDisk.getJSONObject("spec");

                    // 描述信息
                    if (spec.containsKey("description")) {
                        disk.setDesc(spec.getString("description"));
                    }

                    // 磁盘资源配置
                    if (spec.containsKey("resources")) {
                        JSONObject specResources = spec.getJSONObject("resources");

                        // 磁盘大小 (如果 status 中没有)
                        if (disk.getSize() == null && specResources.containsKey("disk_size_bytes")) {
                            Long diskSizeBytes = specResources.getLong("disk_size_bytes");
                            disk.setSize(diskSizeBytes / (1024.0f * 1024.0f * 1024.0f));
                        }

                        // 磁盘类型和分类
                        if (specResources.containsKey("device_properties")) {
                            JSONObject deviceProps = specResources.getJSONObject("device_properties");
                            if (deviceProps.containsKey("device_type")) {
                                disk.setType(deviceProps.getString("device_type"));
                            }
                        }

                        // 存储策略
                        if (specResources.containsKey("storage_config")) {
                            JSONObject storageConfig = specResources.getJSONObject("storage_config");
                            if (storageConfig.containsKey("storage_container_reference")) {
                                JSONObject containerRef = storageConfig.getJSONObject("storage_container_reference");
                                if (containerRef.containsKey("name")) {
                                    disk.setStore_name(containerRef.getString("name"));
                                }
                            }
                        }
                    }
                }

                // 磁盘分类和性能分类
                if (nutanixDisk.containsKey("categories")) {
                    JSONObject categories = nutanixDisk.getJSONObject("categories");
                    // 可以根据实际的 categories 结构来设置 category 和 perf_category
                    disk.setCategory("VIRTUAL");
                }

                // 共享磁盘判断 (Nutanix 中大部分磁盘是非共享的)
                disk.setShare_disk("NO");

                // 置备类型 (默认为精简置备)
                disk.setProvision_type("THIN");

                // 配置模式
                disk.setConfig_mode("AUTO");

                // 时间信息
                if (nutanixDisk.containsKey("metadata")) {
                    JSONObject metadata = nutanixDisk.getJSONObject("metadata");

                    // 创建时间
                    if (metadata.containsKey("creation_time")) {
                        String creationTime = metadata.getString("creation_time");
                        try {
                            long createTimeMillis = parseNutanixTime(creationTime);
                            disk.setCreate_time(createTimeMillis);
                            disk.setOpen_create_time(createTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析磁盘创建时间: {}", creationTime);
                        }
                    }

                    // 更新时间
                    if (metadata.containsKey("last_update_time")) {
                        String updateTime = metadata.getString("last_update_time");
                        try {
                            long updateTimeMillis = parseNutanixTime(updateTime);
                            disk.setUpdate_time(updateTimeMillis);
                            disk.setOpen_update_time(updateTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析磁盘更新时间: {}", updateTime);
                        }
                    }
                }

                // 设置云类型和账号信息
                toCiResCloud(request, disk);
                data.add(disk);

            } catch (Exception e) {
                // 获取磁盘 UUID 用于错误日志
                String errorDiskUuid = "Unknown";
                try {
                    if (nutanixDisk.containsKey("metadata")) {
                        JSONObject metadata = nutanixDisk.getJSONObject("metadata");
                        if (metadata != null && metadata.containsKey("uuid")) {
                            errorDiskUuid = metadata.getString("uuid");
                        }
                    }
                    if ("Unknown".equals(errorDiskUuid) && nutanixDisk.containsKey("uuid")) {
                        errorDiskUuid = nutanixDisk.getString("uuid");
                    }
                    if ("Unknown".equals(errorDiskUuid) && nutanixDisk.containsKey("id")) {
                        errorDiskUuid = nutanixDisk.getString("id");
                    }
                } catch (Exception ex) {
                    // 忽略获取 UUID 时的异常，使用默认值
                }
                log.error("转换磁盘数据失败，磁盘 UUID: {}", errorDiskUuid, e);
            }
        }

        log.info("成功转换 {} 个磁盘数据", data.size());
        result.put(CmdbDiskRes.class, data);
        return result;
    }

    /**
     * 转换磁盘数据为 CI 模型
     * 完整提取磁盘的配置、状态、容量和时间信息
     */
    public static Map<Class, List> convertVirtualDiskData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();

        // 处理不同 API 版本的响应格式
        JSONArray disks = null;
        if (response != null) {
            if (response.containsKey("entities")) {
                // Prism Central v3 或 Prism Element v2.0 格式
                disks = response.getJSONArray("entities");
            } else if (response.containsKey("virtual_disks")) {
                // 某些 v2.0 API 可能使用不同的字段名
                disks = response.getJSONArray("virtual_disks");
            }
        }
        log.info("原始虚拟磁盘数据: {}", JSON.toJSONString(response));

        if (disks == null || disks.isEmpty()) {
            result.put(CmdbDiskRes.class, new ArrayList<>());
            return result;
        }

        DecimalFormat dec = new DecimalFormat("0.00");
        virtualDiskMap.clear();
        for (int i = 0; i < disks.size(); i++) {
            JSONObject nutanixDisk = disks.getJSONObject(i);
            try {
                JSONObject info = new JSONObject();
                info.put("resourceId",nutanixDisk.getString("uuid"));
                info.put("deviceId",nutanixDisk.getString("device_uuid"));
                info.put("cloudType",request.getPlugin().getRealm());
                info.put("diskPath",nutanixDisk.getString("nutanix_nfsfile_path"));
                info.put("clusterId",nutanixDisk.getString("cluster_uuid"));
                info.put("resourceName",nutanixDisk.getString("disk_address"));
                info.put("diskSize",Double.parseDouble(dec.format(nutanixDisk.getLong("disk_capacity_in_bytes")/1024/1024/1024.0)));
                info.put("StorageContainerId",nutanixDisk.getString("storage_container_uuid"));
                    JSONObject statObj =nutanixDisk.getJSONObject("stats");
                    if(statObj!=null){
                        info.put("diskUsed",Double.parseDouble(dec.format(statObj.getLong("controller_user_bytes")/1024/1024/1024.0)));
                    }
                    info.put("vmId",nutanixDisk.getString("attached_vm_uuid"));
                    info.put("vmName",nutanixDisk.getString("attached_vmname"));
                    info.put("cmpId",IdUtils.encryptId(request.getBody().getAccess().getCmpId(), nutanixDisk.getString("uuid")));

                virtualDiskMap.put(info.getString("cmpId"), info);

                // 设置云类型和账号信息

            } catch (Exception e) {
                // 获取磁盘 UUID 用于错误日志
                String errorDiskUuid = "Unknown";
                try {
                    if (nutanixDisk.containsKey("metadata")) {
                        JSONObject metadata = nutanixDisk.getJSONObject("metadata");
                        if (metadata != null && metadata.containsKey("uuid")) {
                            errorDiskUuid = metadata.getString("uuid");
                        }
                    }
                    if ("Unknown".equals(errorDiskUuid) && nutanixDisk.containsKey("uuid")) {
                        errorDiskUuid = nutanixDisk.getString("uuid");
                    }
                    if ("Unknown".equals(errorDiskUuid) && nutanixDisk.containsKey("id")) {
                        errorDiskUuid = nutanixDisk.getString("id");
                    }
                } catch (Exception ex) {
                    // 忽略获取 UUID 时的异常，使用默认值
                }
                log.error("转换磁盘数据失败，磁盘 UUID: {}", errorDiskUuid, e);
            }
        }

        return result;
    }
    /**
     * 转换快照数据为 CI 模型
     */
    public static Map<Class, List> convertSnapshotData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (response == null || !response.containsKey("entities")) {
            log.warn("快照数据响应为空或不包含entities字段");
            result.put(CmdbSnapshotRes.class, new ArrayList<>());
            return result;
        }

        List<CmdbSnapshotRes> data = new ArrayList<>();
        JSONArray entities = response.getJSONArray("entities");

        for (int i = 0; i < entities.size(); i++) {
            JSONObject nutanixSnapshot = entities.getJSONObject(i);
            CmdbSnapshotRes ci = new CmdbSnapshotRes();

            try {
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), nutanixSnapshot.getString("uuid")));
                ci.setOpen_id(nutanixSnapshot.getString("uuid"));
                ci.setOpen_name(nutanixSnapshot.getJSONObject("spec").getString("name"));

                toCiResCloud(request, ci);
                data.add(ci);

            } catch (Exception e) {
                log.error("转换快照数据异常: {}", e.getMessage(), e);
            }
        }

        result.put(CmdbSnapshotRes.class, data);
        return result;
    }

    /**
     * 转换存储容器数据为 CI 模型
     * 处理包含 entities 数组的 API 响应，根据实际字段结构进行数据映射
     */
    public static Map<Class, List> convertStorageData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        if (response == null) {
            result.put(CmdbStorageContainerRes.class, new ArrayList<>());
            return result;
        }

        List<CmdbStorageContainerRes> data = new ArrayList<>();

        // 处理 entities 数组格式的响应
        if (response.containsKey("entities")) {
            JSONArray entities = response.getJSONArray("entities");
            for (int i = 0; i < entities.size(); i++) {
                JSONObject nutanixStorage = entities.getJSONObject(i);
                CmdbStorageContainerRes ci = convertSingleStorageContainer(request, nutanixStorage);
                if (ci != null) {
                    data.add(ci);
                }
            }
        }

        result.put(CmdbStorageContainerRes.class, data);
        return result;
    }

    /**
     * 转换单个存储容器对象
     */
    private static CmdbStorageContainerRes convertSingleStorageContainer(BaseCloudRequest request, JSONObject nutanixStorage) {
        if (nutanixStorage == null) {
            return null;
        }

        CmdbStorageContainerRes ci = new CmdbStorageContainerRes();

        try {
            // 基本信息 - 优先使用 storage_container_uuid，然后是 id，最后是 uuid
            String containerId = null;
            if (nutanixStorage.containsKey("storage_container_uuid")) {
                containerId = nutanixStorage.getString("storage_container_uuid");
            } else if (nutanixStorage.containsKey("id")) {
                containerId = nutanixStorage.getString("id");
            } else if (nutanixStorage.containsKey("uuid")) {
                containerId = nutanixStorage.getString("uuid");
            }

            if (containerId != null) {
                ci.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), containerId));
                ci.setOpen_id(containerId);
            }

            // 存储容器名称
            String containerName = null;
            if (nutanixStorage.containsKey("name")) {
                containerName = nutanixStorage.getString("name");
            } else if (nutanixStorage.containsKey("spec") && nutanixStorage.getJSONObject("spec").containsKey("name")) {
                containerName = nutanixStorage.getJSONObject("spec").getString("name");
            }
            ci.setOpen_name(containerName != null ? containerName : "Unknown Storage Container");

            // 存储容器容量信息 - 从 usage_stats 中获取
            if (nutanixStorage.containsKey("usage_stats")) {
                JSONObject usageStats = nutanixStorage.getJSONObject("usage_stats");

                // 总容量 - 使用 storage.capacity_bytes
                if (usageStats.containsKey("storage.capacity_bytes")) {
                    String capacityStr = usageStats.getString("storage.capacity_bytes");
                    try {
                        Long totalCapacity = Long.parseLong(capacityStr);
                        ci.setTotal_size(totalCapacity / (1024.0f * 1024.0f * 1024.0f)); // 转换为 GB
                    } catch (NumberFormatException e) {
                        log.warn("解析总容量失败: {}", capacityStr);
                    }
                }

                // 已使用容量 - 使用 storage.usage_bytes
                if (usageStats.containsKey("storage.usage_bytes")) {
                    String usageStr = usageStats.getString("storage.usage_bytes");
                    try {
                        Long usedCapacity = Long.parseLong(usageStr);
                        ci.setUsed_size(usedCapacity / (1024.0f * 1024.0f * 1024.0f)); // 转换为 GB
                    } catch (NumberFormatException e) {
                        log.warn("解析已使用容量失败: {}", usageStr);
                    }
                }

                // 剩余容量 - 使用 storage.free_bytes
                if (usageStats.containsKey("storage.free_bytes")) {
                    String freeStr = usageStats.getString("storage.free_bytes");
                    try {
                        Long freeCapacity = Long.parseLong(freeStr);
                        ci.setFree_size(freeCapacity / (1024.0f * 1024.0f * 1024.0f)); // 转换为 GB
                    } catch (NumberFormatException e) {
                        log.warn("解析剩余容量失败: {}", freeStr);
                    }
                }

                // 如果没有直接的剩余容量，通过计算得出
                if (ci.getFree_size() == null && ci.getTotal_size() != null && ci.getUsed_size() != null) {
                    Float freeSize = ci.getTotal_size() - ci.getUsed_size();
                    ci.setFree_size(freeSize);
                }

                // 计算使用率
                if (ci.getTotal_size() != null && ci.getUsed_size() != null && ci.getTotal_size() > 0) {
                    Float usage = (ci.getUsed_size() / ci.getTotal_size()) * 100;
                    ci.setUseUsage(usage);
                }

                // 逻辑使用量 - storage.logical_usage_bytes
                if (usageStats.containsKey("storage.logical_usage_bytes")) {
                    String logicalUsageStr = usageStats.getString("storage.logical_usage_bytes");
                    try {
                        Long logicalUsage = Long.parseLong(logicalUsageStr);
                        // 将逻辑使用量添加到配置信息中（extend2）
                        String existingExtend2 = ci.getExtend2();
                        String logicalUsageInfo = "logical_usage_gb:" + String.format("%.2f", logicalUsage / (1024.0f * 1024.0f * 1024.0f));
                        if (existingExtend2 != null && !existingExtend2.isEmpty()) {
                            ci.setExtend2(existingExtend2 + ";" + logicalUsageInfo);
                        } else {
                            ci.setExtend2(logicalUsageInfo);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("解析逻辑使用量失败: {}", logicalUsageStr);
                    }
                }
            }

            // 备用容量信息获取方式 - 从 max_capacity 字段
            if (ci.getTotal_size() == null && nutanixStorage.containsKey("max_capacity")) {
                try {
                    Long maxCapacity = nutanixStorage.getLong("max_capacity");
                    ci.setTotal_size(maxCapacity / (1024.0f * 1024.0f * 1024.0f)); // 转换为 GB
                } catch (Exception e) {
                    log.warn("解析 max_capacity 失败: {}", e.getMessage());
                }
            }

            // 存储容器配置信息
            StringBuilder configInfo = new StringBuilder();

            // 复制因子
            if (nutanixStorage.containsKey("replication_factor")) {
                int replicationFactor = nutanixStorage.getIntValue("replication_factor");
                configInfo.append("replication_factor:").append(replicationFactor).append(";");
            }

            // 压缩启用状态
            if (nutanixStorage.containsKey("compression_enabled")) {
                boolean compressionEnabled = nutanixStorage.getBooleanValue("compression_enabled");
                configInfo.append("compression:").append(compressionEnabled ? "enabled" : "disabled").append(";");
            }

            // 重复数据删除
            if (nutanixStorage.containsKey("on_disk_dedup")) {
                String dedupStatus = nutanixStorage.getString("on_disk_dedup");
                configInfo.append("dedup:").append(dedupStatus).append(";");
            }

            // 纠删码
            if (nutanixStorage.containsKey("erasure_code")) {
                String erasureCode = nutanixStorage.getString("erasure_code");
                configInfo.append("erasure_code:").append(erasureCode).append(";");
            }

            // 加密状态
            if (nutanixStorage.containsKey("enable_software_encryption")) {
                boolean encryptionEnabled = nutanixStorage.getBooleanValue("enable_software_encryption");
                configInfo.append("encryption:").append(encryptionEnabled ? "enabled" : "disabled").append(";");
            }

            // 存储配置信息存储在扩展字段2中
            if (configInfo.length() > 0) {
                ci.setExtend2(configInfo.toString());
            }

            // 存储层级偏好设置 - 存储在扩展字段3中
            StringBuilder tierInfo = new StringBuilder();
            if (nutanixStorage.containsKey("random_io_preference")) {
                JSONArray randomIoPreference = nutanixStorage.getJSONArray("random_io_preference");
                tierInfo.append("random_io:").append(randomIoPreference.toString()).append(";");
            }
            if (nutanixStorage.containsKey("seq_io_preference")) {
                JSONArray seqIoPreference = nutanixStorage.getJSONArray("seq_io_preference");
                tierInfo.append("seq_io:").append(seqIoPreference.toString()).append(";");
            }
            if (tierInfo.length() > 0) {
                ci.setExtend3(tierInfo.toString());
            }

            // NFS 白名单信息 - 添加到存储层级信息中（extend3）
            if (nutanixStorage.containsKey("nfs_whitelist")) {
                JSONArray nfsWhitelist = nutanixStorage.getJSONArray("nfs_whitelist");
                String existingExtend3 = ci.getExtend3();
                String nfsInfo = "nfs_whitelist:" + nfsWhitelist.toString();
                if (existingExtend3 != null && !existingExtend3.isEmpty()) {
                    ci.setExtend3(existingExtend3 + ";" + nfsInfo);
                } else {
                    ci.setExtend3(nfsInfo);
                }
            }

            // 集群 UUID - 存储在扩展字段1中
            if (nutanixStorage.containsKey("cluster_uuid")) {
                String clusterUuid = nutanixStorage.getString("cluster_uuid");
                ci.setExtend1("cluster_uuid:" + clusterUuid);
            }

            // 时间信息处理
            if (nutanixStorage.containsKey("metadata")) {
                JSONObject metadata = nutanixStorage.getJSONObject("metadata");
                if (metadata != null) {
                    // 创建时间
                    if (metadata.containsKey("creation_time")) {
                        String creationTime = metadata.getString("creation_time");
                        try {
                            long createTimeMillis = parseNutanixTime(creationTime);
                            ci.setCreate_time(createTimeMillis);
                            ci.setOpen_create_time(createTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析存储容器创建时间: {}", creationTime);
                        }
                    }

                    // 更新时间
                    if (metadata.containsKey("last_update_time")) {
                        String updateTime = metadata.getString("last_update_time");
                        try {
                            long updateTimeMillis = parseNutanixTime(updateTime);
                            ci.setUpdate_time(updateTimeMillis);
                            ci.setOpen_update_time(updateTimeMillis);
                        } catch (Exception e) {
                            log.warn("无法解析存储容器更新时间: {}", updateTime);
                        }
                    }
                }
            }

            // 数据缩减统计信息
            if (nutanixStorage.containsKey("usage_stats")) {
                JSONObject usageStats = nutanixStorage.getJSONObject("usage_stats");
                StringBuilder dataReductionInfo = new StringBuilder();

                // 数据缩减比率
                if (usageStats.containsKey("data_reduction.saving_ratio_ppm")) {
                    String savingRatioPpm = usageStats.getString("data_reduction.saving_ratio_ppm");
                    try {
                        long ratio = Long.parseLong(savingRatioPpm);
                        float savingRatio = ratio / 1000000.0f; // 转换为倍数
                        dataReductionInfo.append("saving_ratio:").append(String.format("%.2f", savingRatio)).append(";");
                    } catch (NumberFormatException e) {
                        log.warn("解析数据缩减比率失败: {}", savingRatioPpm);
                    }
                }

                // 节省的字节数
                if (usageStats.containsKey("data_reduction.saved_bytes")) {
                    String savedBytesStr = usageStats.getString("data_reduction.saved_bytes");
                    try {
                        long savedBytes = Long.parseLong(savedBytesStr);
                        float savedGB = savedBytes / (1024.0f * 1024.0f * 1024.0f);
                        dataReductionInfo.append("saved_gb:").append(String.format("%.2f", savedGB)).append(";");
                    } catch (NumberFormatException e) {
                        log.warn("解析节省字节数失败: {}", savedBytesStr);
                    }
                }

                if (dataReductionInfo.length() > 0) {
                    // 将数据缩减信息添加到配置信息中（extend2）
                    String existingExtend2 = ci.getExtend2();
                    if (existingExtend2 != null && !existingExtend2.isEmpty()) {
                        ci.setExtend2(existingExtend2 + ";" + dataReductionInfo.toString());
                    } else {
                        ci.setExtend2(dataReductionInfo.toString());
                    }
                }
            }

            toCiResCloud(request, ci);
            return ci;

        } catch (Exception e) {
            log.error("转换存储容器数据失败，容器 ID: {}",
                nutanixStorage.containsKey("storage_container_uuid") ?
                nutanixStorage.getString("storage_container_uuid") : "Unknown", e);
            return null;
        }
    }

//    /**
//     * 转换网络数据为 CI 模型
//     * 完整提取虚拟交换机的配置、状态和时间信息
//     */
//    public static Map<Class, List> convertNetworkData(BaseCloudRequest request, JSONObject response) {
//        Map<Class, List> result = new HashMap<>();
//
//        // 处理不同 API 版本的响应格式
//        JSONArray networks = null;
//        if (response != null) {
//            if (response.containsKey("entities")) {
//                // Prism Central v3 或 Prism Element v2.0 格式
//                networks = response.getJSONArray("entities");
//            } else if (response.containsKey("virtual_switches")) {
//                // 某些 v2.0 API 可能使用不同的字段名
//                networks = response.getJSONArray("virtual_switches");
//            } else if (response.containsKey("subnets")) {
//                // v3 API 中的子网信息
//                networks = response.getJSONArray("subnets");
//            }
//        }
//
//        if (networks == null || networks.isEmpty()) {
//            result.put(CmdbVswitchRes.class, new ArrayList<>());
//            return result;
//        }
//
//        List<CmdbVswitchRes> data = new ArrayList<>();
//
//        for (int i = 0; i < networks.size(); i++) {
//            JSONObject nutanixNetwork = networks.getJSONObject(i);
//            CmdbVswitchRes vswitch = new CmdbVswitchRes();
//
//            try {
//                // 基本信息 - 从 metadata.uuid 获取网络 UUID
//                String networkUuid = null;
//                if (nutanixNetwork.containsKey("metadata")) {
//                    JSONObject metadata = nutanixNetwork.getJSONObject("metadata");
//                    if (metadata != null && metadata.containsKey("uuid")) {
//                        networkUuid = metadata.getString("uuid");
//                    }
//                }
//
//                // 如果 metadata.uuid 不存在，尝试从根级别获取（兼容性处理）
//                if (networkUuid == null && nutanixNetwork.containsKey("uuid")) {
//                    networkUuid = nutanixNetwork.getString("uuid");
//                }
//
//                // 最后尝试从 id 字段获取
//                if (networkUuid == null && nutanixNetwork.containsKey("id")) {
//                    networkUuid = nutanixNetwork.getString("id");
//                }
//
//                if (networkUuid != null) {
//                    vswitch.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), networkUuid));
//                    vswitch.setOpen_id(networkUuid);
//                }
//
//                // 虚拟交换机名称
//                String networkName = null;
//                if (nutanixNetwork.containsKey("spec") && nutanixNetwork.getJSONObject("spec").containsKey("name")) {
//                    networkName = nutanixNetwork.getJSONObject("spec").getString("name");
//                } else if (nutanixNetwork.containsKey("name")) {
//                    networkName = nutanixNetwork.getString("name");
//                }
//                vswitch.setOpen_name(networkName != null ? networkName : "Unknown Network");
//
//                // 状态信息
//                if (nutanixNetwork.containsKey("status")) {
//                    JSONObject status = nutanixNetwork.getJSONObject("status");
//
//                    // 网络状态
//                    if (status.containsKey("state")) {
//                        String state = status.getString("state");
//                        vswitch.setStatus(convertNetworkStatus(state));
//                        vswitch.setOpen_status(state);
//                    }
//
//                    // 网络资源信息
//                    if (status.containsKey("resources")) {
//                        JSONObject resources = status.getJSONObject("resources");
//
//                        // 网络类型
//                        if (resources.containsKey("subnet_type")) {
//                            vswitch.setType(resources.getString("subnet_type"));
//                        } else {
//                            vswitch.setType("VLAN"); // 默认类型
//                        }
//
//                        // VLAN ID 和网络配置
//                        if (resources.containsKey("vlan_id")) {
//                            Integer vlanId = resources.getInteger("vlan_id");
//                            if (vlanId != null && vlanId > 0) {
//                                vswitch.setExtend1("vlan_id:" + vlanId);
//                            }
//                        }
//
//                        // MTU 配置
//                        if (resources.containsKey("mtu")) {
//                            vswitch.setMtu(resources.getString("mtu"));
//                        } else {
//                            vswitch.setMtu("1500"); // 默认 MTU
//                        }
//
//                        // IP 配置信息
//                        if (resources.containsKey("ip_config")) {
//                            JSONObject ipConfig = resources.getJSONObject("ip_config");
//                            StringBuilder networkInfo = new StringBuilder();
//
//                            if (ipConfig.containsKey("subnet_ip")) {
//                                networkInfo.append("subnet:").append(ipConfig.getString("subnet_ip"));
//                            }
//                            if (ipConfig.containsKey("prefix_length")) {
//                                networkInfo.append("/").append(ipConfig.getInteger("prefix_length"));
//                            }
//                            if (ipConfig.containsKey("default_gateway_ip")) {
//                                networkInfo.append(",gateway:").append(ipConfig.getString("default_gateway_ip"));
//                            }
//
//                            if (networkInfo.length() > 0) {
//                                vswitch.setExtend2(networkInfo.toString());
//                            }
//                        }
//                    }
//                }
//
//                // 网络规格信息
//                if (nutanixNetwork.containsKey("spec")) {
//                    JSONObject spec = nutanixNetwork.getJSONObject("spec");
//
//                    // 描述信息
//                    if (spec.containsKey("description")) {
//                        vswitch.setDesc(spec.getString("description"));
//                    }
//
//                    // 网络资源配置
//                    if (spec.containsKey("resources")) {
//                        JSONObject specResources = spec.getJSONObject("resources");
//
//                        // 网络类型
//                        if (vswitch.getType() == null && specResources.containsKey("subnet_type")) {
//                            vswitch.setType(specResources.getString("subnet_type"));
//                        }
//
//                        // 是否为默认网络
//                        if (specResources.containsKey("is_external")) {
//                            Boolean isExternal = specResources.getBoolean("is_external");
//                            vswitch.setIs_default(isExternal != null && isExternal ? "YES" : "NO");
//                        } else {
//                            vswitch.setIs_default("NO");
//                        }
//
//                        // 集群配置
//                        if (specResources.containsKey("cluster_reference")) {
//                            JSONObject clusterRef = specResources.getJSONObject("cluster_reference");
//                            if (clusterRef.containsKey("name")) {
//                                vswitch.setExtend3("cluster:" + clusterRef.getString("name"));
//                            }
//                        }
//                    }
//                }
//
//                // 绑定模式 (Nutanix 默认使用负载均衡模式)
//                vswitch.setBond_mode("LOAD_BALANCE");
//
//                // 共享设置 (Nutanix 网络通常是共享的)
//                vswitch.setShare("YES");
//
//                // 时间信息
//                if (nutanixNetwork.containsKey("metadata")) {
//                    JSONObject metadata = nutanixNetwork.getJSONObject("metadata");
//
//                    // 创建时间
//                    if (metadata.containsKey("creation_time")) {
//                        String creationTime = metadata.getString("creation_time");
//                        try {
//                            long createTimeMillis = parseNutanixTime(creationTime);
//                            vswitch.setCreate_time(createTimeMillis);
//                            vswitch.setOpen_create_time(createTimeMillis);
//                        } catch (Exception e) {
//                            log.warn("无法解析网络创建时间: {}", creationTime);
//                        }
//                    }
//
//                    // 更新时间
//                    if (metadata.containsKey("last_update_time")) {
//                        String updateTime = metadata.getString("last_update_time");
//                        try {
//                            long updateTimeMillis = parseNutanixTime(updateTime);
//                            vswitch.setUpdate_time(updateTimeMillis);
//                            vswitch.setOpen_update_time(updateTimeMillis);
//                        } catch (Exception e) {
//                            log.warn("无法解析网络更新时间: {}", updateTime);
//                        }
//                    }
//                }
//
//                // 设置云类型和账号信息
//                toCiResCloud(request, vswitch);
//                data.add(vswitch);
//
//            } catch (Exception e) {
//                // 获取网络 UUID 用于错误日志
//                String errorNetworkUuid = "Unknown";
//                try {
//                    if (nutanixNetwork.containsKey("metadata")) {
//                        JSONObject metadata = nutanixNetwork.getJSONObject("metadata");
//                        if (metadata != null && metadata.containsKey("uuid")) {
//                            errorNetworkUuid = metadata.getString("uuid");
//                        }
//                    }
//                    if ("Unknown".equals(errorNetworkUuid) && nutanixNetwork.containsKey("uuid")) {
//                        errorNetworkUuid = nutanixNetwork.getString("uuid");
//                    }
//                    if ("Unknown".equals(errorNetworkUuid) && nutanixNetwork.containsKey("id")) {
//                        errorNetworkUuid = nutanixNetwork.getString("id");
//                    }
//                } catch (Exception ex) {
//                    // 忽略获取 UUID 时的异常，使用默认值
//                }
//                log.error("转换网络数据失败，网络 UUID: {}", errorNetworkUuid, e);
//            }
//        }
//
//        log.info("成功转换 {} 个虚拟交换机数据", data.size());
//        result.put(CmdbVswitchRes.class, data);
//        return result;
//    }

    /**
     * 转换网络数据为 CI 模型
     * 完整提取虚拟交换机的配置、状态和时间信息
     */
    public static Map<Class, List> convertNetworkData(BaseCloudRequest request, String response) {
        Map<Class, List> result = new HashMap<>();

        // 处理不同 API 版本的响应格式
        JSONArray networks = null;
        if (response != null) {
            networks = JSON.parseArray(response);
        }

        if (networks == null || networks.isEmpty()) {
            result.put(CmdbVswitchRes.class, new ArrayList<>());
            return result;
        }

        List<CmdbVswitchRes> data = new ArrayList<>();

        for (int i = 0; i < networks.size(); i++) {
//            JSONObject nutanixNetwork = networks.getJSONObject(i);
            JSONObject nutanixNetwork = ((JSONObject) networks.get(i)).getJSONObject("data");
            CmdbVswitchRes vswitch = new CmdbVswitchRes();

            try {
                // 基本信息 - 从 metadata.uuid 获取网络 UUID
                String networkUuid = nutanixNetwork.getString("extId");

                if (networkUuid != null) {
                    vswitch.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), networkUuid));
                    vswitch.setOpen_id(networkUuid);
                }

                // 虚拟交换机名称
                String networkName = nutanixNetwork.getString("name");
                vswitch.setOpen_name(networkName != null ? networkName : "Unknown Network");
                vswitch.setMtu(nutanixNetwork.getInteger("mtu")+"");
                vswitch.setType(nutanixNetwork.getString("bondMode"));
                vswitch.setBond_mode(nutanixNetwork.getString("bondMode"));
                vswitch.setDesc(nutanixNetwork.getString("description"));
                vswitch.setIs_default(nutanixNetwork.getBoolean("isDefault")+"");
//                // 状态信息
//                if (nutanixNetwork.containsKey("status")) {
//                    JSONObject status = nutanixNetwork.getJSONObject("status");
//
//                    // 网络状态
//                    if (status.containsKey("state")) {
//                        String state = status.getString("state");
//                        vswitch.setStatus(convertNetworkStatus(state));
//                        vswitch.setOpen_status(state);
//                    }
//
//                    // 网络资源信息
//                    if (status.containsKey("resources")) {
//                        JSONObject resources = status.getJSONObject("resources");
//
//                        // 网络类型
//                        if (resources.containsKey("subnet_type")) {
//                            vswitch.setType(resources.getString("subnet_type"));
//                        } else {
//                            vswitch.setType("VLAN"); // 默认类型
//                        }
//
//                        // VLAN ID 和网络配置
//                        if (resources.containsKey("vlan_id")) {
//                            Integer vlanId = resources.getInteger("vlan_id");
//                            if (vlanId != null && vlanId > 0) {
//                                vswitch.setExtend1("vlan_id:" + vlanId);
//                            }
//                        }
//
//                        // MTU 配置
//                        if (resources.containsKey("mtu")) {
//                            vswitch.setMtu(resources.getString("mtu"));
//                        } else {
//                            vswitch.setMtu("1500"); // 默认 MTU
//                        }
//
//                        // IP 配置信息
//                        if (resources.containsKey("ip_config")) {
//                            JSONObject ipConfig = resources.getJSONObject("ip_config");
//                            StringBuilder networkInfo = new StringBuilder();
//
//                            if (ipConfig.containsKey("subnet_ip")) {
//                                networkInfo.append("subnet:").append(ipConfig.getString("subnet_ip"));
//                            }
//                            if (ipConfig.containsKey("prefix_length")) {
//                                networkInfo.append("/").append(ipConfig.getInteger("prefix_length"));
//                            }
//                            if (ipConfig.containsKey("default_gateway_ip")) {
//                                networkInfo.append(",gateway:").append(ipConfig.getString("default_gateway_ip"));
//                            }
//
//                            if (networkInfo.length() > 0) {
//                                vswitch.setExtend2(networkInfo.toString());
//                            }
//                        }
//                    }
//                }

//                // 网络规格信息
//                if (nutanixNetwork.containsKey("spec")) {
//                    JSONObject spec = nutanixNetwork.getJSONObject("spec");
//
//                    // 描述信息
//                    if (spec.containsKey("description")) {
//                        vswitch.setDesc(spec.getString("description"));
//                    }
//
//                    // 网络资源配置
//                    if (spec.containsKey("resources")) {
//                        JSONObject specResources = spec.getJSONObject("resources");
//
//                        // 网络类型
//                        if (vswitch.getType() == null && specResources.containsKey("subnet_type")) {
//                            vswitch.setType(specResources.getString("subnet_type"));
//                        }
//
//                        // 是否为默认网络
//                        if (specResources.containsKey("is_external")) {
//                            Boolean isExternal = specResources.getBoolean("is_external");
//                            vswitch.setIs_default(isExternal != null && isExternal ? "YES" : "NO");
//                        } else {
//                            vswitch.setIs_default("NO");
//                        }
//
//                        // 集群配置
//                        if (specResources.containsKey("cluster_reference")) {
//                            JSONObject clusterRef = specResources.getJSONObject("cluster_reference");
//                            if (clusterRef.containsKey("name")) {
//                                vswitch.setExtend3("cluster:" + clusterRef.getString("name"));
//                            }
//                        }
//                    }
//                }

                // 绑定模式 (Nutanix 默认使用负载均衡模式)
//                vswitch.setBond_mode("LOAD_BALANCE");

                // 共享设置 (Nutanix 网络通常是共享的)
                vswitch.setShare("YES");

                // 时间信息
//                if (nutanixNetwork.containsKey("metadata")) {
//                    JSONObject metadata = nutanixNetwork.getJSONObject("metadata");
//
//                    // 创建时间
//                    if (metadata.containsKey("creation_time")) {
//                        String creationTime = metadata.getString("creation_time");
//                        try {
//                            long createTimeMillis = parseNutanixTime(creationTime);
//                            vswitch.setCreate_time(createTimeMillis);
//                            vswitch.setOpen_create_time(createTimeMillis);
//                        } catch (Exception e) {
//                            log.warn("无法解析网络创建时间: {}", creationTime);
//                        }
//                    }
//
//                    // 更新时间
//                    if (metadata.containsKey("last_update_time")) {
//                        String updateTime = metadata.getString("last_update_time");
//                        try {
//                            long updateTimeMillis = parseNutanixTime(updateTime);
//                            vswitch.setUpdate_time(updateTimeMillis);
//                            vswitch.setOpen_update_time(updateTimeMillis);
//                        } catch (Exception e) {
//                            log.warn("无法解析网络更新时间: {}", updateTime);
//                        }
//                    }
//                }

                // 设置云类型和账号信息
                toCiResCloud(request, vswitch);
                data.add(vswitch);

            } catch (Exception e) {
                // 获取网络 UUID 用于错误日志
                String errorNetworkUuid = "Unknown";
                try {
                    if (nutanixNetwork.containsKey("metadata")) {
                        JSONObject metadata = nutanixNetwork.getJSONObject("metadata");
                        if (metadata != null && metadata.containsKey("uuid")) {
                            errorNetworkUuid = metadata.getString("uuid");
                        }
                    }
                    if ("Unknown".equals(errorNetworkUuid) && nutanixNetwork.containsKey("uuid")) {
                        errorNetworkUuid = nutanixNetwork.getString("uuid");
                    }
                    if ("Unknown".equals(errorNetworkUuid) && nutanixNetwork.containsKey("id")) {
                        errorNetworkUuid = nutanixNetwork.getString("id");
                    }
                } catch (Exception ex) {
                    // 忽略获取 UUID 时的异常，使用默认值
                }
                log.error("转换网络数据失败，网络 UUID: {}", errorNetworkUuid, e);
            }
        }

        log.info("成功转换 {} 个虚拟交换机数据", data.size());
        result.put(CmdbVswitchRes.class, data);
        return result;
    }
    /**
     * 转换虚拟机性能数据为 CI 模型
     */
    public static Map<String, PerfInfoBean> convertVmPerformanceData(NutanixClient client,BaseCloudRequest request, JSONObject response) {
        Map<String, PerfInfoBean> perfMap = new HashMap<>();
        if (response == null || !response.containsKey("entities")) {
            log.warn("虚拟机性能数据响应为空或不包含entities字段");
            return null;
        }
        DecimalFormat dec = new DecimalFormat("0.00");
//        List<PerfInfoBean> data = new ArrayList<>();
        JSONArray entities = response.getJSONArray("entities");

        for (int i = 0; i < entities.size(); i++) {
            JSONObject nutanixVm = entities.getJSONObject(i);
            PerfInfoBean perfData = new PerfInfoBean();

            try {

                // 性能数据提取
                if (nutanixVm.containsKey("status")) {
                    JSONObject status = nutanixVm.getJSONObject("status");
                    // CPU 性能数据
                    if (status.containsKey("resources")) {
                        JSONObject resources = status.getJSONObject("resources");
                        // 电源状态
                        if (resources.containsKey("power_state")) {
                            String powerState = resources.getString("power_state");
                            if(!"ON".equals(powerState))
                                continue;
                        }
                    }
                }


                        // 基本信息 - 从 metadata.uuid 获取虚拟机 UUID
                String vmId = null;
                if (nutanixVm.containsKey("metadata")) {
                    JSONObject metadata = nutanixVm.getJSONObject("metadata");
                    if (metadata != null && metadata.containsKey("uuid")) {
                        vmId = metadata.getString("uuid");
                    }
                }

                // 如果 metadata.uuid 不存在，尝试从根级别获取（兼容性处理）
                if (vmId == null && nutanixVm.containsKey("uuid")) {
                    vmId = nutanixVm.getString("uuid");
                }

                // 最后尝试从 id 字段获取
                if (vmId == null && nutanixVm.containsKey("id")) {
                    vmId = nutanixVm.getString("id");
                }

                if (vmId != null) {
                    // 设置时间戳
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Long timestamp = System.currentTimeMillis();
                    // 将时间戳转换为Date对象
                    Date date = new Date(timestamp);
// 计算当前分钟的起始时间（秒数清零）
                    long minutes = date.getTime() / (60 * 1000); // 转换为分钟数
                    long zeroSecondTime = minutes * 60 * 1000;   // 重新计算为毫秒（秒数为0）
                    perfData.setCreateTime(sdf.format(new Date(zeroSecondTime)));
//                    log.info("性能数据时间，虚拟机 性能数据时间: {}", perfData.getCreateTime());

                    perfData.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), vmId));
                    perfData.setId(vmId + "_" + perfData.getResId()+ "_" + timestamp);
                    perfData.setOpenId(vmId);
                } else {
                    log.warn("虚拟机UUID为空，跳过该虚拟机性能数据");
                    continue;
                }


                // 虚拟机名称
                String vmName = null;
                if (nutanixVm.containsKey("spec") && nutanixVm.getJSONObject("spec").containsKey("name")) {
                    vmName = nutanixVm.getJSONObject("spec").getString("name");
                } else if (nutanixVm.containsKey("name")) {
                    vmName = nutanixVm.getString("name");
                }
                perfData.setOpenName(vmName != null ? vmName : "Unknown VM");

                // 设置资源类型和云平台类型
                perfData.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                perfData.setCloudType(request.getPlugin().getRealm());
                perfData.setAccountId(request.getBody().getAccess().getCmpId());

                // 性能数据提取
                if (nutanixVm.containsKey("status")) {
                    JSONObject status = nutanixVm.getJSONObject("status");

                    // CPU 性能数据
                    if (status.containsKey("resources")) {
                        JSONObject resources = status.getJSONObject("resources");

                        // CPU 核数
                        if (resources.containsKey("num_vcpus_per_socket")) {
                            Integer cpuSockets = resources.getInteger("num_vcpus_per_socket");
                            Integer numSockets = resources.getInteger("num_sockets");
                            if (cpuSockets != null && numSockets != null) {
                                perfData.setCpuSize((double)(cpuSockets * numSockets));
                            }
                        }

                        // 内存大小 (转换为 GB)
                        if (resources.containsKey("memory_size_mib")) {
                            Integer memoryMib = resources.getInteger("memory_size_mib");
                            if (memoryMib != null) {
                                perfData.setMemSize((double)(memoryMib / 1024.0));
                            }
                        }

                        Double diskUsed =0.0;
                        Integer diskMib = 0;
                        if (resources.containsKey("disk_list")) {
                            JSONArray diskList = resources.getJSONArray("disk_list");
                            for (int m = 0; m < diskList.size(); m++) {
                                JSONObject o = (JSONObject) diskList.get(m);
                                if(o.containsKey("device_properties") && o.getJSONObject("device_properties").containsKey("device_type")){
                                    if(!"DISK".equals(o.getJSONObject("device_properties").getString("device_type"))){
                                        continue;
                                    }
                                     diskMib = diskMib+o.getInteger("disk_size_mib");

                                    if(virtualDiskMap.get(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), o.getString("uuid")))!=null){
                                        diskUsed = diskUsed+virtualDiskMap.get(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), o.getString("uuid"))).getDouble("diskUsed");

                                    }

                                }

                            }
                                perfData.setDiskSize((double)(diskMib / 1024.0));
                            perfData.setDiskUsage(Double.parseDouble(dec.format(100.0*diskUsed/perfData.getDiskSize())));
                        }

                    }
                }
                // 设置默认性能指标
                perfData.setCpuUsage(0.0);
                perfData.setMemUsage(0.0);
                perfData.setDiskRead(0.0);
                perfData.setDiskWrite(0.0);
                perfData.setNetIn(0.0);
                perfData.setNetOut(0.0);
                //查询使用率性能数据
                Long createTime = new Date().getTime();
                Integer interval = 300;
                Long startTime = (createTime - 5*60*1000l)*1000;
                Long endTime = createTime*1000;
                String url ="/PrismGateway/services/rest/v1/vms" +"/"+perfData.getOpenId()+ "/stats?metrics=hypervisor_cpu_usage_ppm,memory_usage_ppm,controller_num_iops,controller_io_bandwidth_kBps,controller_avg_io_latency_usecs&intervalInSecs="+interval+"&startTimeInUsecs="+startTime+"&endTimeInUsecs"+endTime;
                JSONObject obj = client.doGetDataJSONWithFullPath(url);
                JSONArray  array = obj.getJSONArray("statsSpecificResponses");
                if(array!=null){
                    for(int n=0;n<array.size();n++){
                        JSONObject perfObj = ((JSONObject) array.get(n));
//                        perf.put("writeTime",perfObj.getLong("startTimeInUsecs")/1000);
                        JSONArray valueArray =perfObj.getJSONArray("values");
                        if(valueArray!=null&&valueArray.size()>0){
                            if("hypervisor_cpu_usage_ppm".equals(perfObj.getString("metric"))){
                                perfData.setCpuUsage(Double.parseDouble(dec.format(valueArray.getLong(0)/10000.0)));
                            }else if("hypervisor_memory_usage_ppm".equals(perfObj.getString("metric"))||"memory_usage_ppm".equals(perfObj.getString("metric"))){
                                perfData.setMemUsage(Double.parseDouble(dec.format(valueArray.getLong(0)/10000.0)));
                            }
//                            else if("controller_num_iops".equals(perfObj.getString("metric"))||"num_iops".equals(perfObj.getString("metric"))){
//                                perf.put("IOPS",valueArray.getLong(0));
//                            }else if("controller_io_bandwidth_kBps".equals(perfObj.getString("metric"))||"io_bandwidth_kBps".equals(perfObj.getString("metric"))){
//                                perf.put("ioBandwidth",valueArray.getLong(0));
//                            }else if("controller_avg_io_latency_usecs".equals(perfObj.getString("metric"))||"avg_io_latency_usecs".equals(perfObj.getString("metric"))){
//                                perf.put("latency",Float.parseFloat(dec.format(valueArray.getLong(0)/1000.0)));
//                            }
                        }
                    }
                }

                perfMap.put(perfData.getId(), perfData);

            } catch (Exception e) {
                // 获取虚拟机 UUID 用于错误日志
                String errorVmUuid = "Unknown";
                try {
                    if (nutanixVm.containsKey("metadata")) {
                        JSONObject metadata = nutanixVm.getJSONObject("metadata");
                        if (metadata != null && metadata.containsKey("uuid")) {
                            errorVmUuid = metadata.getString("uuid");
                        }
                    }
                    if ("Unknown".equals(errorVmUuid) && nutanixVm.containsKey("uuid")) {
                        errorVmUuid = nutanixVm.getString("uuid");
                    }
                } catch (Exception ex) {
                    // 忽略获取 UUID 时的异常，使用默认值
                }
                log.error("转换虚拟机性能数据失败，虚拟机 UUID: {}", errorVmUuid, e);
                // 继续处理下一个虚拟机，不中断整个转换过程
            }
        }

        log.info("成功转换 {} 个虚拟机性能数据", perfMap.values().size());
//        result.put(PerfInfoBean.class, data);
        return perfMap;
    }


    public static BasePageSortSearchRequest toBasePageSortSearchRequest(BaseCloudRequest request) {
        BasePageSortSearchRequest searchRequest = new BasePageSortSearchRequest();
        CloudAccessBean access = request.getBody().getAccess();
        searchRequest.setCurrent(1);
        searchRequest.setSize(50);
        searchRequest.setSortField(BaseConstant.RES_ID);
        searchRequest.setSort(0);
        List<BaseSearchApiModel> searchList = new ArrayList<>();
        BaseSearchApiModel searchApiModel = new BaseSearchApiModel();
        searchApiModel.setKey(BaseConstant.ACCOUNT_ID);
        searchApiModel.setValue(access.getCmpId());
        searchApiModel.setSearchClassiy("0");
        searchList.add(searchApiModel);
        BaseSearchApiModel searchApiModel2 = new BaseSearchApiModel();
        searchApiModel2.setKey("status");
        searchApiModel2.setValue("running");
        searchApiModel2.setSearchClassiy("0");
        searchList.add(searchApiModel2);
        searchRequest.setSearchList(searchList);
        return searchRequest;
    }


    /**
     * 转换告警数据
     */
    public static Map<Class, List> convertAlarmData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();
        List<CmdbInstanceRes> data = new ArrayList<>();

        JSONArray alarms = response.getJSONArray("entities");
        for (int i = 0; i < alarms.size(); i++) {
            try {
                JSONObject nutanixAlarm = alarms.getJSONObject(i);
                CmdbInstanceRes ci = new CmdbInstanceRes();

                // 基本信息
                ci.setOpen_id(nutanixAlarm.getString("id"));
                ci.setOpen_name(nutanixAlarm.getString("message"));
                ci.setExtend3("alarm");

                // 告警详情
                if (nutanixAlarm.containsKey("severity")) {
                    ci.setStatus(nutanixAlarm.getString("severity"));
                }
                if (nutanixAlarm.containsKey("created_time_stamp_in_usecs")) {
                    ci.setCreate_time(nutanixAlarm.getLong("created_time_stamp_in_usecs") / 1000);
                }
                if (nutanixAlarm.containsKey("resolved")) {
                    ci.setOpen_status(nutanixAlarm.getBoolean("resolved") ? "resolved" : "active");
                }

                toCiResCloud(request, ci);
                data.add(ci);

            } catch (Exception e) {
                log.error("转换告警数据异常: {}", e.getMessage(), e);
            }
        }

        result.put(CmdbInstanceRes.class, data);
        return result;
    }

    /**
     * 转换任务数据为 CI 模型
     * 支持 Prism Element v2.0 和 Prism Central v3 API 的任务数据格式
     */
    public static Map<Class, List> convertTaskData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();

        if (response == null) {
            log.warn("任务数据响应为空");
            result.put(TaskInfoBean.class, new ArrayList<>());
            return result;
        }

        List<TaskInfoBean> data = new ArrayList<>();

        // 处理不同 API 版本的响应格式
        JSONArray tasks = null;
        if (response.containsKey("entities")) {
            // Prism Central v3 格式
            tasks = response.getJSONArray("entities");
        } else if (response.containsKey("tasks")) {
            // Prism Element v2.0 格式
            tasks = response.getJSONArray("tasks");
        }

        if (tasks == null || tasks.isEmpty()) {
            result.put(TaskInfoBean.class, data);
            return result;
        }

        for (int i = 0; i < tasks.size(); i++) {
            JSONObject nutanixTask = tasks.getJSONObject(i);
            TaskInfoBean task = new TaskInfoBean();

            try {
                // 基本信息
                String taskUuid = nutanixTask.getString("uuid");
                if (taskUuid == null) {
                    taskUuid = nutanixTask.getString("task_uuid"); // v2.0 格式
                }

                if (taskUuid != null) {
                    task.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), taskUuid));
                    task.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), taskUuid));
//                    task.setOpenId(taskUuid);
                    task.setTaskId(taskUuid);
                } else {
                    log.warn("任务UUID为空，跳过该任务");
                    continue;
                }

                // 任务名称和类型
                String operationType = nutanixTask.getString("operation_type");
                if (operationType == null) {
                    operationType = nutanixTask.getString("operation"); // v2.0 格式
                }
//                String display_name = nutanixTask.getString("display_name");
                task.setOpenName(operationType != null ? operationType : "Unknown Task");
                task.setTaskName(operationType != null ? operationType : "Unknown Task");
                task.setOpenTaskType(operationType);

                // 任务状态
                String status = nutanixTask.getString("status");
                if (status != null) {
                    task.setOpenStatus(status);
                    task.setTaskStatus(status);
                }else{
                    task.setOpenStatus(nutanixTask.getString("progress_status"));
                    task.setTaskStatus(nutanixTask.getString("progress_status"));
                }



                // 时间信息
                String startTime = nutanixTask.getString("start_time");
                if (startTime == null) {
                    startTime = nutanixTask.getString("create_time"); // v2.0 格式
                }
                if (startTime == null && nutanixTask.getString("start_time_usecs")!=null) {
                    startTime = nutanixTask.getString("start_time_usecs"); // v2.0 格式
                }
                if (startTime != null) {
                    long startTimeMillis = parseNutanixTime(startTime);
                    task.setBeginTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(startTimeMillis)));
                }

                String endTime = nutanixTask.getString("end_time");
                if (endTime == null) {
                    endTime = nutanixTask.getString("complete_time"); // v2.0 格式
                }
                if (endTime == null && nutanixTask.getString("complete_time_usecs")!=null) {
                    endTime = nutanixTask.getString("complete_time_usecs"); // v2.0 格式
                }
                if (endTime != null) {
                    long endTimeMillis = parseNutanixTime(endTime);
                    task.setEndTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(endTimeMillis)));
                }

                // 任务描述/详情
                String description = nutanixTask.getString("message");
                if (description == null) {
                    description = nutanixTask.getString("description"); // v2.0 格式
                }
                task.setDetail(description);



                // 关联资源类型
                String entityType = nutanixTask.getString("entity_type");
                if (entityType == null) {
                    entityType = nutanixTask.getString("target_entity_type"); // v2.0 格式
                }
                JSONArray entityArray = nutanixTask.getJSONArray("entity_list");
                if(entityType == null && entityArray!=null && StrUtil.isNotEmpty(((JSONObject)entityArray.get(0)).getString("entity_type"))){
                    entityType = ((JSONObject)entityArray.get(0)).getString("entity_type");
                }
                // 根据Nutanix资源类型映射到标准资源类型
                if ("vm".equalsIgnoreCase(entityType) || "virtual_machine".equalsIgnoreCase(entityType)) {
                    task.setResourceType("resource_vm");
                } else if ("host".equalsIgnoreCase(entityType) || "node".equalsIgnoreCase(entityType)) {
                    task.setResourceType("resource_host");
                } else if ("cluster".equalsIgnoreCase(entityType)) {
                    task.setResourceType("resource_cluster");
                } else {
                    task.setResourceType("resource_other");
                }

                // 计算任务持续时间
                if (startTime != null && endTime != null) {
                    try {
                        long startTimeMillis = parseNutanixTime(startTime);
                        long endTimeMillis = parseNutanixTime(endTime);
                        double durationSeconds = (endTimeMillis - startTimeMillis) / 1000.0;
                        task.setDuration(durationSeconds);
                    } catch (Exception e) {
                        log.warn("计算任务持续时间失败: {}", e.getMessage());
                        task.setDuration(0.0);
                    }
                } else {
                    task.setDuration(0.0);
                }

                // 任务消息（错误信息或状态消息）
                String errorDetail = nutanixTask.getString("error_detail");
                String message = nutanixTask.getString("message");
                String taskMessage = errorDetail != null ? errorDetail : message;
                task.setTaskMessage(taskMessage);

                // 设置云类型和账号信息
                task.setAccountId(request.getBody().getAccess().getCmpId());
                task.setCloudType(request.getPlugin().getRealm());

                // 设置数据来源
                task.setDataSource("API");

                // 设置原始JSON
                task.setJsonInfo(nutanixTask.toJSONString());

                data.add(task);

            } catch (Exception e) {
                String errorTaskUuid = "Unknown";
                try {
                    errorTaskUuid = nutanixTask.getString("uuid");
                    if (errorTaskUuid == null) {
                        errorTaskUuid = nutanixTask.getString("task_uuid");
                    }
                } catch (Exception ex) {
                    // 忽略获取UUID时的异常
                }
                log.error("转换任务数据失败，任务 UUID: {}", errorTaskUuid, e);
                // 继续处理下一个任务，不中断整个转换过程
            }
        }

        log.info("成功转换 {} 个任务数据", data.size());
        result.put(TaskInfoBean.class, data);
        return result;
    }

    /**
     * 转换存储池数据为 CI 模型
     * 支持多种 API 版本的存储池数据格式，包括单个对象和数组格式
     */
    public static Map<Class, List> convertStoragePoolData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();

        if (response == null) {
            result.put(CmdbStoragePoolRes.class, new ArrayList<>());
            return result;
        }

        List<CmdbStoragePoolRes> data = new ArrayList<>();

        // 处理不同的响应格式
        if (response.containsKey("entities")) {
            // v3 API 格式：包含 entities 数组
            JSONArray entities = response.getJSONArray("entities");
            for (int i = 0; i < entities.size(); i++) {
                JSONObject nutanixPool = entities.getJSONObject(i);
                CmdbStoragePoolRes pool = convertSingleStoragePool(request, nutanixPool);
                if (pool != null) {
                    data.add(pool);
                }
            }
        } else if (response.containsKey("storage_pools")) {
            // v2.0/v1 API 格式：包含 storage_pools 数组
            JSONArray storagePools = response.getJSONArray("storage_pools");
            for (int i = 0; i < storagePools.size(); i++) {
                JSONObject nutanixPool = storagePools.getJSONObject(i);
                CmdbStoragePoolRes pool = convertSingleStoragePool(request, nutanixPool);
                if (pool != null) {
                    data.add(pool);
                }
            }
        } else if (response.containsKey("storagePoolUuid") || response.containsKey("id") || response.containsKey("name")) {
            // 单个存储池对象格式
            CmdbStoragePoolRes pool = convertSingleStoragePool(request, response);
            if (pool != null) {
                data.add(pool);
            }
        }

        log.info("成功转换 {} 个存储池数据", data.size());
        result.put(CmdbStoragePoolRes.class, data);
        return result;
    }

    /**
     * 转换单个存储池对象
     */
    private static CmdbStoragePoolRes convertSingleStoragePool(BaseCloudRequest request, JSONObject nutanixPool) {
        if (nutanixPool == null) {
            return null;
        }

        CmdbStoragePoolRes pool = new CmdbStoragePoolRes();

        try {
            // 1. 基本信息映射
            mapBasicStoragePoolInfo(request, nutanixPool, pool);

            // 2. 容量信息映射
            mapStoragePoolCapacity(nutanixPool, pool);

            // 3. 存储层级信息映射
            mapStoragePoolTiers(nutanixPool, pool);

            // 4. 性能统计信息映射
            mapStoragePoolPerformance(nutanixPool, pool);

            // 5. 配置详情映射
            mapStoragePoolConfiguration(nutanixPool, pool);

            // 6. 磁盘信息映射
            mapStoragePoolDisks(nutanixPool, pool);

            // 7. 时间信息映射
            mapStoragePoolTimeInfo(nutanixPool, pool);

            // 设置云类型和账号信息
            toCiResCloud(request, pool);

            return pool;

        } catch (Exception e) {
            String poolId = getStoragePoolId(nutanixPool);
            log.error("转换存储池数据失败，存储池 ID: {}", poolId, e);
            return null;
        }
    }

    /**
     * 映射存储池基本信息
     */
    private static void mapBasicStoragePoolInfo(BaseCloudRequest request, JSONObject nutanixPool, CmdbStoragePoolRes pool) {
        // UUID 映射：优先使用 storagePoolUuid，然后是 id
        String poolId = null;
        if (nutanixPool.containsKey("storagePoolUuid")) {
            poolId = nutanixPool.getString("storagePoolUuid");
        } else if (nutanixPool.containsKey("id")) {
            poolId = nutanixPool.getString("id");
        } else if (nutanixPool.containsKey("uuid")) {
            poolId = nutanixPool.getString("uuid");
        }

        if (poolId != null) {
            pool.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), poolId));
            pool.setOpen_id(poolId);
        }

        // 名称映射
        String poolName = nutanixPool.getString("name");
        pool.setOpen_name(poolName != null ? poolName : "Unknown Storage Pool");

        // 集群 UUID 存储在 extend1 中
        if (nutanixPool.containsKey("clusterUuid")) {
            String clusterUuid = nutanixPool.getString("clusterUuid");
            pool.setExtend1("cluster_uuid:" + clusterUuid);
        }

        // 状态信息
        if (nutanixPool.containsKey("markedForRemoval")) {
            boolean markedForRemoval = nutanixPool.getBooleanValue("markedForRemoval");
            String status = markedForRemoval ? "marked_for_removal" : "active";
            pool.setStatus(status);
            pool.setOpen_status(status);
        }
    }

    /**
     * 映射存储池容量信息
     */
    private static void mapStoragePoolCapacity(JSONObject nutanixPool, CmdbStoragePoolRes pool) {
        StringBuilder capacityInfo = new StringBuilder();

        if (nutanixPool.containsKey("usageStats")) {
            JSONObject usageStats = nutanixPool.getJSONObject("usageStats");

            // 总容量
            if (usageStats.containsKey("storage.capacity_bytes")) {
                String capacityStr = usageStats.getString("storage.capacity_bytes");
                try {
                    Long totalCapacity = Long.parseLong(capacityStr);
                    float totalSizeGB = totalCapacity / (1024.0f * 1024.0f * 1024.0f);
                    pool.setTotal_size(Float.parseFloat(String.format("%.2f", totalSizeGB)));
                } catch (NumberFormatException e) {
                    log.warn("解析存储池总容量失败: {}", capacityStr);
                }
            }

            // 已使用容量
            if (usageStats.containsKey("storage.usage_bytes")) {
                String usageStr = usageStats.getString("storage.usage_bytes");
                try {
                    Long usedCapacity = Long.parseLong(usageStr);
                    float usedSizeGB = usedCapacity / (1024.0f * 1024.0f * 1024.0f);
                    pool.setUsed_size(Float.parseFloat(String.format("%.2f", usedSizeGB)));
                } catch (NumberFormatException e) {
                    log.warn("解析存储池已使用容量失败: {}", usageStr);
                }
            }

            // 剩余容量 - 存储在扩展字段中，因为类中没有 free_size 字段
            if (usageStats.containsKey("storage.free_bytes")) {
                String freeStr = usageStats.getString("storage.free_bytes");
                try {
                    Long freeCapacity = Long.parseLong(freeStr);
                    float freeSizeGB = freeCapacity / (1024.0f * 1024.0f * 1024.0f);
                    capacityInfo.append("free_size:").append(String.format("%.2f", freeSizeGB)).append("GB;");
                } catch (NumberFormatException e) {
                    log.warn("解析存储池剩余容量失败: {}", freeStr);
                }
            }

            // 计算使用率 - 存储在扩展字段中，因为类中没有 useUsage 字段
            if (pool.getTotal_size() != null && pool.getUsed_size() != null && pool.getTotal_size() > 0) {
                float usagePercentage = (pool.getUsed_size() / pool.getTotal_size()) * 100;
                capacityInfo.append("usage_percentage:").append(String.format("%.2f", usagePercentage)).append("%;");
            }

            // 已分配容量和置备容量
            if (usageStats.containsKey("storage.allocated_bytes")) {
                String allocatedStr = usageStats.getString("storage.allocated_bytes");
                try {
                    Long allocatedCapacity = Long.parseLong(allocatedStr);
                    float allocatedSizeGB = allocatedCapacity / (1024.0f * 1024.0f * 1024.0f);
                    pool.setAllocation_size(Float.parseFloat(String.format("%.2f", allocatedSizeGB)));
                } catch (NumberFormatException e) {
                    log.warn("解析存储池已分配容量失败: {}", allocatedStr);
                }
            }

            if (usageStats.containsKey("storage.provisioned_bytes")) {
                String provisionedStr = usageStats.getString("storage.provisioned_bytes");
                try {
                    Long provisionedCapacity = Long.parseLong(provisionedStr);
                    float provisionedSizeGB = provisionedCapacity / (1024.0f * 1024.0f * 1024.0f);
                    pool.setProvision_size(Float.parseFloat(String.format("%.2f", provisionedSizeGB)));
                } catch (NumberFormatException e) {
                    log.warn("解析存储池置备容量失败: {}", provisionedStr);
                }
            }
        }

        // 备用容量信息获取方式
        if (pool.getTotal_size() == null && nutanixPool.containsKey("capacity_bytes")) {
            try {
                Long capacity = nutanixPool.getLong("capacity_bytes");
                float capacityGB = capacity / (1024.0f * 1024.0f * 1024.0f);
                pool.setTotal_size(Float.parseFloat(String.format("%.2f", capacityGB)));
            } catch (Exception e) {
                log.warn("解析备用容量信息失败: {}", e.getMessage());
            }
        }

        // 将额外的容量信息添加到 extend1 中
        if (capacityInfo.length() > 0) {
            String existingExtend1 = pool.getExtend1();
            if (existingExtend1 != null && !existingExtend1.isEmpty()) {
                pool.setExtend1(existingExtend1 + ";" + capacityInfo.toString());
            } else {
                pool.setExtend1(capacityInfo.toString());
            }
        }
    }

    /**
     * 获取存储池 ID（用于错误日志）
     */
    private static String getStoragePoolId(JSONObject nutanixPool) {
        if (nutanixPool.containsKey("storagePoolUuid")) {
            return nutanixPool.getString("storagePoolUuid");
        } else if (nutanixPool.containsKey("id")) {
            return nutanixPool.getString("id");
        } else if (nutanixPool.containsKey("uuid")) {
            return nutanixPool.getString("uuid");
        }
        return "Unknown";
    }

    /**
     * 映射存储池存储层级信息（存储在 extend2 中）
     */
    private static void mapStoragePoolTiers(JSONObject nutanixPool, CmdbStoragePoolRes pool) {
        if (nutanixPool.containsKey("usageStats")) {
            JSONObject usageStats = nutanixPool.getJSONObject("usageStats");
            StringBuilder tierInfo = new StringBuilder();

            // DAS-SATA 层级信息
            if (usageStats.containsKey("storage_tier.das-sata.capacity_bytes")) {
                String dasCapacityStr = usageStats.getString("storage_tier.das-sata.capacity_bytes");
                String dasUsageStr = usageStats.getString("storage_tier.das-sata.usage_bytes");
                try {
                    long dasCapacity = Long.parseLong(dasCapacityStr);
                    long dasUsage = Long.parseLong(dasUsageStr);
                    float dasCapacityGB = dasCapacity / (1024.0f * 1024.0f * 1024.0f);
                    float dasUsageGB = dasUsage / (1024.0f * 1024.0f * 1024.0f);
                    tierInfo.append("das_sata_capacity:").append(String.format("%.2f", dasCapacityGB)).append("GB;");
                    tierInfo.append("das_sata_usage:").append(String.format("%.2f", dasUsageGB)).append("GB;");
                } catch (NumberFormatException e) {
                    log.warn("解析 DAS-SATA 层级信息失败");
                }
            }

            // SSD 层级信息
            if (usageStats.containsKey("storage_tier.ssd.capacity_bytes")) {
                String ssdCapacityStr = usageStats.getString("storage_tier.ssd.capacity_bytes");
                String ssdUsageStr = usageStats.getString("storage_tier.ssd.usage_bytes");
                try {
                    long ssdCapacity = Long.parseLong(ssdCapacityStr);
                    long ssdUsage = Long.parseLong(ssdUsageStr);
                    float ssdCapacityGB = ssdCapacity / (1024.0f * 1024.0f * 1024.0f);
                    float ssdUsageGB = ssdUsage / (1024.0f * 1024.0f * 1024.0f);
                    tierInfo.append("ssd_capacity:").append(String.format("%.2f", ssdCapacityGB)).append("GB;");
                    tierInfo.append("ssd_usage:").append(String.format("%.2f", ssdUsageGB)).append("GB;");
                } catch (NumberFormatException e) {
                    log.warn("解析 SSD 层级信息失败");
                }
            }

            if (tierInfo.length() > 0) {
                pool.setExtend2(tierInfo.toString());
            }
        }
    }

    /**
     * 映射存储池性能统计信息（存储在 extend3 中）
     */
    private static void mapStoragePoolPerformance(JSONObject nutanixPool, CmdbStoragePoolRes pool) {
        if (nutanixPool.containsKey("stats")) {
            JSONObject stats = nutanixPool.getJSONObject("stats");
            StringBuilder perfInfo = new StringBuilder();

            // IOPS 信息
            if (stats.containsKey("num_iops")) {
                String iops = stats.getString("num_iops");
                if (!"-1".equals(iops)) {
                    perfInfo.append("iops:").append(iops).append(";");
                }
            }

            if (stats.containsKey("num_read_iops")) {
                String readIops = stats.getString("num_read_iops");
                if (!"-1".equals(readIops)) {
                    perfInfo.append("read_iops:").append(readIops).append(";");
                }
            }

            if (stats.containsKey("num_write_iops")) {
                String writeIops = stats.getString("num_write_iops");
                if (!"-1".equals(writeIops)) {
                    perfInfo.append("write_iops:").append(writeIops).append(";");
                }
            }

            // 延迟信息
            if (stats.containsKey("avg_io_latency_usecs")) {
                String latency = stats.getString("avg_io_latency_usecs");
                if (!"-1".equals(latency)) {
                    perfInfo.append("avg_latency_us:").append(latency).append(";");
                }
            }

            // 带宽信息
            if (stats.containsKey("io_bandwidth_kBps")) {
                String bandwidth = stats.getString("io_bandwidth_kBps");
                if (!"-1".equals(bandwidth)) {
                    perfInfo.append("bandwidth_kbps:").append(bandwidth).append(";");
                }
            }

            if (stats.containsKey("read_io_bandwidth_kBps")) {
                String readBandwidth = stats.getString("read_io_bandwidth_kBps");
                if (!"-1".equals(readBandwidth)) {
                    perfInfo.append("read_bandwidth_kbps:").append(readBandwidth).append(";");
                }
            }

            if (stats.containsKey("write_io_bandwidth_kBps")) {
                String writeBandwidth = stats.getString("write_io_bandwidth_kBps");
                if (!"-1".equals(writeBandwidth)) {
                    perfInfo.append("write_bandwidth_kbps:").append(writeBandwidth).append(";");
                }
            }

            if (perfInfo.length() > 0) {
                pool.setExtend3(perfInfo.toString());
            }
        }
    }

    /**
     * 映射存储池配置详情
     */
    private static void mapStoragePoolConfiguration(JSONObject nutanixPool, CmdbStoragePoolRes pool) {
        StringBuilder configInfo = new StringBuilder();

        // 预留容量
        if (nutanixPool.containsKey("reservedCapacity")) {
            Long reservedCapacity = nutanixPool.getLong("reservedCapacity");
            if (reservedCapacity != null) {
                float reservedGB = reservedCapacity / (1024.0f * 1024.0f * 1024.0f);
                configInfo.append("reserved_capacity:").append(String.format("%.2f", reservedGB)).append("GB;");
            }
        }

        // ILM 下迁百分比阈值
        if (nutanixPool.containsKey("ilmDownMigratePctThreshold")) {
            Integer threshold = nutanixPool.getInteger("ilmDownMigratePctThreshold");
            if (threshold != null) {
                configInfo.append("ilm_threshold:").append(threshold).append("%;");
            }
        }

        // 存储池类型
        if (nutanixPool.containsKey("type")) {
            String poolType = nutanixPool.getString("type");
            if (poolType != null) {
                pool.setType(poolType);
                configInfo.append("pool_type:").append(poolType).append(";");
            }
        }

        // 将配置信息追加到现有的 extend2 中
        if (configInfo.length() > 0) {
            String existingExtend2 = pool.getExtend2();
            if (existingExtend2 != null && !existingExtend2.isEmpty()) {
                pool.setExtend2(existingExtend2 + configInfo.toString());
            } else {
                pool.setExtend2(configInfo.toString());
            }
        }
    }

    /**
     * 映射存储池磁盘信息
     */
    private static void mapStoragePoolDisks(JSONObject nutanixPool, CmdbStoragePoolRes pool) {
        StringBuilder diskInfo = new StringBuilder();

        // 磁盘数量
        if (nutanixPool.containsKey("disks")) {
            JSONArray disks = nutanixPool.getJSONArray("disks");
            if (disks != null) {
                diskInfo.append("disk_count:").append(disks.size()).append(";");
            }
        }

        // 磁盘 UUID 列表
        if (nutanixPool.containsKey("diskUuids")) {
            JSONArray diskUuids = nutanixPool.getJSONArray("diskUuids");
            if (diskUuids != null && diskUuids.size() > 0) {
                diskInfo.append("disk_uuids:").append(diskUuids.toString()).append(";");
            }
        }

        // 将磁盘信息追加到现有的 extend3 中
        if (diskInfo.length() > 0) {
            String existingExtend3 = pool.getExtend3();
            if (existingExtend3 != null && !existingExtend3.isEmpty()) {
                pool.setExtend3(existingExtend3 + diskInfo.toString());
            } else {
                pool.setExtend3(diskInfo.toString());
            }
        }
    }

    /**
     * 映射存储池时间信息
     */
    private static void mapStoragePoolTimeInfo(JSONObject nutanixPool, CmdbStoragePoolRes pool) {
        if (nutanixPool.containsKey("metadata")) {
            JSONObject metadata = nutanixPool.getJSONObject("metadata");
            if (metadata != null) {
                // 创建时间
                if (metadata.containsKey("creation_time")) {
                    String creationTime = metadata.getString("creation_time");
                    try {
                        long createTimeMillis = parseNutanixTime(creationTime);
                        pool.setCreate_time(createTimeMillis);
                        pool.setOpen_create_time(createTimeMillis);
                    } catch (Exception e) {
                        log.warn("无法解析存储池创建时间: {}", creationTime);
                    }
                }

                // 更新时间
                if (metadata.containsKey("last_update_time")) {
                    String updateTime = metadata.getString("last_update_time");
                    try {
                        long updateTimeMillis = parseNutanixTime(updateTime);
                        pool.setUpdate_time(updateTimeMillis);
                        pool.setOpen_update_time(updateTimeMillis);
                    } catch (Exception e) {
                        log.warn("无法解析存储池更新时间: {}", updateTime);
                    }
                }
            }
        }
    }

    /**
     * 转换告警数据为告警模型
     * 支持 Prism Element v2.0 和 Prism Central v3 API 的告警数据格式
     */
    public static List<AlarmInfoBean> convertAlertData(BaseCloudRequest request, JSONObject response) {
//        Map<Class, List> result = new HashMap<>();

        if (response == null) {
//            result.put(AlarmInfoBean.class, new ArrayList<>());
            return null;
        }

        List<AlarmInfoBean> data = new ArrayList<>();

        // 处理不同 API 版本的响应格式
        JSONArray alerts = null;
        if (response.containsKey("entities")) {
            // Prism Central v3 格式
            alerts = response.getJSONArray("entities");
        } else if (response.containsKey("alerts")) {
            // Prism Element v2.0 格式
            alerts = response.getJSONArray("alerts");
        }

        if (alerts == null || alerts.isEmpty()) {
//            result.put(AlarmInfoBean.class, data);
            return null;
        }

        for (int i = 0; i < alerts.size(); i++) {
            JSONObject nutanixAlert = alerts.getJSONObject(i);
            AlarmInfoBean alert = convertSingleAlert(request, nutanixAlert);
            if (alert != null) {
                data.add(alert);
            }
        }

        log.info("成功转换 {} 个告警数据", data.size());
//        result.put(AlarmInfoBean.class, data);
        return data;
    }

    /**
     * 转换单个告警对象
     */
    private static AlarmInfoBean convertSingleAlert(BaseCloudRequest request, JSONObject nutanixAlert) {
        if (nutanixAlert == null) {
            return null;
        }

        AlarmInfoBean alert = new AlarmInfoBean();

        try {
            // 基本信息 - 从 metadata.uuid 获取
            String alertId = null;
            if (nutanixAlert.containsKey("metadata")) {
                JSONObject metadata = nutanixAlert.getJSONObject("metadata");
                if (metadata != null && metadata.containsKey("uuid")) {
                    alertId = metadata.getString("uuid");
                }
            }



            // 云账号和云类型信息
            alert.setAccountId(request.getBody().getAccess().getCmpId());
            alert.setCloudType(request.getPlugin().getRealm());

            // 从 status.resources 获取告警详细信息
            if (nutanixAlert.containsKey("status") && nutanixAlert.getJSONObject("status").containsKey("resources")) {
                JSONObject resources = nutanixAlert.getJSONObject("status").getJSONObject("resources");

                // 告警标题
                if (resources.containsKey("title")) {
                    String title = resources.getString("title");
                    alert.setAlarmName(title);
                    alert.setOpenName(title);
                }

                // 告警类型
                if (resources.containsKey("type")) {
                    String alertType = resources.getString("type");
                    alert.setResourceType(alertType);
                }

                // 告警严重性
                if (resources.containsKey("severity")) {
                    String severity = resources.getString("severity");
                    alert.setOpenLevel(severity);
                    alert.setAlarmLevel(mapNutanixSeverityToCmpLevel(severity));
                }

                // 告警详情 - 使用 default_message
                if (resources.containsKey("default_message")) {
                    String message = resources.getString("default_message");
                    alert.setDetail(message);
                }

                // 分类信息
                if (resources.containsKey("classification_list")) {
                    JSONArray classifications = resources.getJSONArray("classification_list");
                    if (classifications != null && classifications.size() > 0) {
                        StringBuilder classificationStr = new StringBuilder();
                        for (int i = 0; i < classifications.size(); i++) {
                            if (i > 0) classificationStr.append(", ");
                            classificationStr.append(classifications.getString(i));
                        }
                        // 如果没有设置 resourceType，使用分类信息
                        if (alert.getResourceType() == null) {
                            alert.setResourceType(classificationStr.toString());
                        }
                    }
                }

                // 时间信息处理
                mapAlertTimeInfoFromResources(resources, alert);

                if (alertId != null) {
                    alert.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alertId,alert.getAlarmName(),alert.getResourceType()));
                    alert.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), alertId));
                    alert.setAlarmId(alertId);
                    alert.setOpenId(alertId);
                }

                // 状态信息处理
                mapAlertStatusFromResources(resources, alert);

                // 受影响的实体信息
                if (resources.containsKey("affected_entity_list")) {
                    JSONArray affectedEntities = resources.getJSONArray("affected_entity_list");
                    if (affectedEntities != null && affectedEntities.size() > 0) {
                        StringBuilder entityInfo = new StringBuilder();
                        for (int i = 0; i < affectedEntities.size(); i++) {
                            JSONObject entity = affectedEntities.getJSONObject(i);
                            if (i > 0) entityInfo.append(", ");
                            entityInfo.append(entity.getString("name")).append("(").append(entity.getString("type")).append(")");
                        }
                        // 将受影响实体信息添加到详情中
                        String existingDetail = alert.getDetail();
                        if (existingDetail != null) {
                            alert.setDetail(existingDetail + " | 受影响实体: " + entityInfo.toString());
                        } else {
                            alert.setDetail("受影响实体: " + entityInfo.toString());
                        }
                    }
                }

                // 可能原因和解决方案
                if (resources.containsKey("possible_cause_list")) {
                    JSONArray possibleCauses = resources.getJSONArray("possible_cause_list");
                    if (possibleCauses != null && possibleCauses.size() > 0) {
                        StringBuilder causeInfo = new StringBuilder();
                        for (int i = 0; i < possibleCauses.size(); i++) {
                            JSONObject cause = possibleCauses.getJSONObject(i);
                            if (cause.containsKey("cause_list")) {
                                JSONArray causes = cause.getJSONArray("cause_list");
                                for (int j = 0; j < causes.size(); j++) {
                                    if (causeInfo.length() > 0) causeInfo.append("; ");
                                    causeInfo.append(causes.getString(j));
                                }
                            }
                        }
                        // 将原因信息添加到详情中
                        if (causeInfo.length() > 0) {
                            String existingDetail = alert.getDetail();
                            if (existingDetail != null) {
                                alert.setDetail(existingDetail + " | 可能原因: " + causeInfo.toString());
                            } else {
                                alert.setDetail("可能原因: " + causeInfo.toString());
                            }
                        }
                    }
                }
            }

            // 其他信息存储为 JSON
            alert.setJsonInfo(nutanixAlert.toJSONString());
            alert.setSource("api");
//            log.info("转换告警数据,AlarmLevel: {},resourceType: {}", alert.getAlarmLevel(), alert.getResourceType());
            return alert;

        } catch (Exception e) {
            String alertId = nutanixAlert.containsKey("id") ? nutanixAlert.getString("id") : "Unknown";
            log.error("转换告警数据失败，告警 ID: {}", alertId, e);
            return null;
        }
    }

    /**
     * 从 resources 对象映射告警时间信息
     */
    private static void mapAlertTimeInfoFromResources(JSONObject resources, AlarmInfoBean alert) {
        try {
            // 告警创建时间 - status.resources.creation_time
            if (resources.containsKey("creation_time")) {
                String creationTime = resources.getString("creation_time");
                String timeStr = formatNutanixTime(creationTime);
                if (timeStr != null) {
                    alert.setFirstTime(timeStr);
                    alert.setCreateTime(timeStr);
                }
            }

            // 最后更新时间 - status.resources.last_update_time
            if (resources.containsKey("last_update_time")) {
                String lastUpdateTime = resources.getString("last_update_time");
                String timeStr = formatNutanixTime(lastUpdateTime);
                if (timeStr != null) {
                    alert.setLastTime(timeStr);
                }
            }

            // 最新发生时间 - status.resources.latest_occurrence_time
            if (resources.containsKey("latest_occurrence_time")) {
                String latestOccurrenceTime = resources.getString("latest_occurrence_time");
                String timeStr = formatNutanixTime(latestOccurrenceTime);
                if (timeStr != null) {
                    alert.setLastTime(timeStr);
                }
            }
        } catch (Exception e) {
            log.warn("解析告警时间信息失败: {}", e.getMessage());
        }
    }

    /**
     * 格式化 Nutanix 时间为标准格式
     */
    private static String formatNutanixTime(String nutanixTime) {
        if (nutanixTime == null) {
            return null;
        }

        try {
            long timeMillis;
            if (nutanixTime.contains("T")) {
                // ISO 8601 格式
                timeMillis = parseNutanixTime(nutanixTime);
            } else {
                // 微秒时间戳格式
                timeMillis = Long.parseLong(nutanixTime) / 1000; // 微秒转毫秒
            }

            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(timeMillis));
        } catch (Exception e) {
            log.warn("无法解析时间格式: {}", nutanixTime);
            return null;
        }
    }

    /**
     * 从 resources 对象映射告警状态信息
     */
    private static void mapAlertStatusFromResources(JSONObject resources, AlarmInfoBean alert) {
        boolean isClosed = false;

        // 检查解决状态 - status.resources.resolution_status
        if (resources.containsKey("resolution_status")) {
            JSONObject resolutionStatus = resources.getJSONObject("resolution_status");
            if (resolutionStatus != null && resolutionStatus.containsKey("is_true")) {
                isClosed = resolutionStatus.getBooleanValue("is_true");
            }
        }

        // 检查确认状态 - status.resources.acknowledged_status
        boolean isAcknowledged = false;
        if (resources.containsKey("acknowledged_status")) {
            JSONObject acknowledgedStatus = resources.getJSONObject("acknowledged_status");
            if (acknowledgedStatus != null && acknowledgedStatus.containsKey("is_true")) {
                isAcknowledged = acknowledgedStatus.getBooleanValue("is_true");
            }
        }

        alert.setClosedStatus(isClosed);
        alert.setCount(1); // 默认告警次数为1

        // 如果告警已确认但未解决，可以在详情中添加说明
        if (isAcknowledged && !isClosed) {
            String existingDetail = alert.getDetail();
            if (existingDetail != null) {
                alert.setDetail(existingDetail + " | 状态: 已确认");
            } else {
                alert.setDetail("状态: 已确认");
            }
        }
    }

    /**
     * 映射 Nutanix 严重性级别到 CMP 告警级别
     */
    private static String mapNutanixSeverityToCmpLevel(String nutanixSeverity) {
        if (nutanixSeverity == null) {
            return "INFO";
        }

        switch (nutanixSeverity.toUpperCase()) {
            case "CRITICAL":
                return "紧急";
            case "WARNING":
                return "次要";
            case "INFO":
                return "信息";
            case "INFORMATION":
                return "信息";
            default:
                return "INFO";
        }
    }

    /**
     * 转换事件数据为 CI 模型
     * 支持 Prism Element v2.0 和 Prism Central v3 API 的事件数据格式
     */
    public static List<EventInfoBean> convertEventData(BaseCloudRequest request, JSONObject response) {
//        Map<Class, List> result = new HashMap<>();
        List<EventInfoBean> listCI =  new ArrayList<>();

        if (response == null) {
            log.warn("事件数据响应为空");
//            result.put(CmdbInstanceRes.class, new ArrayList<>());
            return null;
        }

        List<CmdbInstanceRes> data = new ArrayList<>();

        // 处理不同 API 版本的响应格式
        JSONArray events = null;
        if (response.containsKey("entities")) {
            // Prism Central v3 格式
            events = response.getJSONArray("entities");
        } else if (response.containsKey("events")) {
            // Prism Element v2.0 格式
            events = response.getJSONArray("events");
        }

        if (events == null || events.isEmpty()) {
//            result.put(CmdbInstanceRes.class, data);
            return null;
        }

        for (int i = 0; i < events.size(); i++) {
            JSONObject nutanixEvent = events.getJSONObject(i);
//            CmdbInstanceRes event = new CmdbInstanceRes();
            EventInfoBean bean = new EventInfoBean();
            try {
                // 基本信息
                String eventId = nutanixEvent.getString("id");
                if (eventId == null) {
                    eventId = nutanixEvent.getString("uuid");
                }

                if (eventId != null) {
                    bean.setResId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eventId));
                    bean.setOpenId(eventId);

                }

                // 事件标题和描述
                String title = nutanixEvent.getString("title");
                if (title == null) {
                    title = nutanixEvent.getString("event_type");
                }

                bean.setOpenName(title != null ? title : "Unknown Event");
//                event.setExtend3("event"); // 标识为事件类型

                // 事件分类
                JSONArray typeArray = nutanixEvent.getJSONArray("classifications");
                if(typeArray!=null&&typeArray.size()>0){
                    bean.setEventType((String)typeArray.get(0));
                    bean.setEventType("system");
                }
                JSONObject detail = new JSONObject();
                detail.put("title",title);
                // 事件消息
                String message = nutanixEvent.getString("message");
                if (message == null) {
                    message = nutanixEvent.getString("description");
                }
                if (message != null) {
//                    event.setExtend1(message); // 事件消息
                    detail.put("message",message);
                }
                bean.setDetail(JSONObject.toJSONString(detail));
                // 时间信息
                String createTime = nutanixEvent.getString("created_time_stamp_in_usecs");
                if (createTime == null) {
                    createTime = nutanixEvent.getString("created_time");
                }
                if (createTime != null) {
                    try {
                        long createTimeMillis = Long.parseLong(createTime) / 1000; // 微秒转毫秒
                        bean.setBeginTime(DateUtil.formatDateTime(DateUtil.date(createTimeMillis)));
                        bean.setEndTime(bean.getBeginTime());
                    } catch (NumberFormatException e) {
                        log.warn("无法解析事件创建时间: {}", createTime);
                    }
                }
//                bean.setEventLevel(nutanixEvent.getString("severity"));
//                /**
//                 * 事件级别。取值：
//                 *
//                 * CRITICAL：严重。
//                 * WARN：警告。
//                 * INFO：信息
//                 */
//                switch (nutanixEvent.getString("severity")) {
//                    case "CRITICAL":
//                        bean.setEventLevel(AlarmLevel.CRITICAL.value());
//                        break;
//                    case "WARN":
//                        bean.setEventLevel(AlarmLevel.MAJOR.value());
//                        break;
//                    case "INFO":
//                        bean.setEventLevel(AlarmLevel.INFORMATION.value());
//                        break;
//                }
                bean.setEventLevel(AlarmLevel.INFORMATION.value());
                bean.setId(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), eventId, bean.getOpenName(), "" + Long.parseLong(createTime)));
                // 设置云类型和账号信息
                bean.setCloudType(request.getPlugin().getRealm()); // 动态获取云类型
                bean.setAccountId(request.getBody().getAccess().getCmpId());
                bean.setJsonInfo(JSONObject.toJSONString(nutanixEvent));
                if(nutanixEvent.containsKey("affected_entities")){
                    JSONArray affected_entities = nutanixEvent.getJSONArray("affected_entities");
                    if(affected_entities != null && 0!= affected_entities.size()
                            && ObjUtil.isNotEmpty(affected_entities.getJSONObject(0))
                            && affected_entities.getJSONObject(0).containsKey("entity_type")){
                        String resourceType = affected_entities.getJSONObject(0).getString("entity_type");
                        if (StrUtil.isNotEmpty(resourceType)) {
                            if ("VM".equalsIgnoreCase(resourceType)
                            ) {
                                bean.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
                            } else if ("DISK".equalsIgnoreCase(resourceType)) {
                                bean.setResourceType(ResourceType.CMDB_DISK_RES.value());
                            } else if ("SNAPSHOT".equalsIgnoreCase(resourceType)) {
                                bean.setResourceType(ResourceType.CMDB_SNAPSHOT_RES.value());
                            } else if ("IMAGE".equalsIgnoreCase(resourceType)) {
                                bean.setResourceType(ResourceType.CMDB_IMAGE_RES.value());
                            } else if ("VIRTUAL_NETWORK".equalsIgnoreCase(resourceType)) {
                                bean.setResourceType(ResourceType.CMDB_VPC_RES.value());
                            } else if ("NETWORK_SWITCH".equalsIgnoreCase(resourceType)) {
                                bean.setResourceType(ResourceType.CMDB_SUBNET_RES.value());
                            }
                        }
                    }

                }

                listCI.add(bean);
//                data.add(event);

            } catch (Exception e) {
                log.error("转换事件数据失败，事件 ID: {}", nutanixEvent.getString("id"), e);
            }
        }

        log.info("成功转换 {} 个事件数据", listCI.size());
//        result.put(CmdbInstanceRes.class, data);
        return listCI;
    }

    /**
     * 转换主机状态为标准状态
     */
    private static String convertHostStatus(String nutanixStatus) {
        if (nutanixStatus == null) {
            return "UNKNOWN";
        }

        switch (nutanixStatus.toUpperCase()) {
            case "COMPLETE":
            case "NORMAL":
            case "READY":
                return "ONLINE";
            case "MAINTENANCE":
            case "ENTERING_MAINTENANCE_MODE":
                return "MAINTENANCE";
            case "FAILED":
            case "ERROR":
                return "ERROR";
            case "OFFLINE":
            case "DISCONNECTED":
                return "OFFLINE";
            case "PENDING":
            case "INITIALIZING":
                return "PENDING";
            default:
                return nutanixStatus.toUpperCase();
        }
    }

    /**
     * 解析 Nutanix 时间格式
     * 支持 ISO 8601 格式和微秒时间戳
     */
    private static long parseNutanixTime(String timeString) {
        if (timeString == null || timeString.isEmpty()) {
            return System.currentTimeMillis();
        }

        try {
            // 尝试解析为微秒时间戳
            if (timeString.matches("\\d+")) {
                long microseconds = Long.parseLong(timeString);
                // 如果是微秒时间戳，转换为毫秒
                if (microseconds > 1000000000000000L) { // 大于某个阈值，认为是微秒
                    return microseconds / 1000;
                } else {
                    return microseconds;
                }
            }

            // 尝试解析 ISO 8601 格式
            // 简单的 ISO 8601 解析，可以根据实际格式调整
            if (timeString.contains("T")) {
                // 移除时区信息进行简单解析
                String cleanTime = timeString.replaceAll("[TZ]", " ").trim();
                // 这里可以使用更复杂的日期解析逻辑
                return System.currentTimeMillis(); // 临时返回当前时间
            }

            return System.currentTimeMillis();
        } catch (Exception e) {
            log.warn("解析时间失败: {}", timeString, e);
            return System.currentTimeMillis();
        }
    }

    /**
     * 转换网络状态为标准状态
     */
    private static String convertNetworkStatus(String nutanixStatus) {
        if (nutanixStatus == null) {
            return "UNKNOWN";
        }

        switch (nutanixStatus.toUpperCase()) {
            case "COMPLETE":
            case "READY":
            case "AVAILABLE":
                return "ACTIVE";
            case "CREATING":
            case "PENDING":
                return "CREATING";
            case "DELETING":
                return "DELETING";
            case "ERROR":
            case "FAILED":
                return "ERROR";
            case "UPDATING":
                return "UPDATING";
            default:
                return nutanixStatus.toUpperCase();
        }
    }

    /**
     * 转换磁盘状态为标准状态
     * 处理 Nutanix 磁盘的各种状态，包括生命周期状态和挂载状态
     *
     * @param nutanixStatus Nutanix 磁盘状态
     * @return 标准化的磁盘状态
     */
    private static String convertDiskStatus(String nutanixStatus) {
        if (nutanixStatus == null) {
            return "unknown";
        }

        switch (nutanixStatus.toUpperCase()) {
            // 可用状态
            case "COMPLETE":
            case "READY":
            case "AVAILABLE":
                return DiskStatus.AVAILABLE.value();

            // 使用中状态
            case "ATTACHED":
            case "IN_USE":
            case "MOUNTED":
                return DiskStatus.IN_USE.value();

            // 创建中状态
            case "CREATING":
            case "PENDING":
            case "BUILDING":
                return DiskStatus.CREATING.value();

            // 删除中状态
            case "DELETING":
            case "REMOVING":
                return "deleting";

            // 错误状态
            case "ERROR":
            case "FAILED":
                return "error";

            // 挂载/卸载操作中
            case "DETACHING":
                return DiskStatus.DETACHING.value();
            case "ATTACHING":
                return DiskStatus.ATTACHING.value();

            // 扩容中 - 使用字符串常量，因为 EXTENDING 枚举可能不存在
            case "EXTENDING":
            case "RESIZING":
                return "extending";

            // 备份中 - 使用字符串常量，因为 BACKING_UP 枚举可能不存在
            case "BACKING_UP":
                return "backing-up";

            // 恢复中 - 使用字符串常量，因为 RESTORING 枚举可能不存在
            case "RESTORING":
                return "restoring";

            default:
                return "unknown";
        }
    }

    /**
     * 转换任务状态为标准状态
     */
    private static String convertTaskStatus(String nutanixStatus) {
        if (nutanixStatus == null) {
            return "UNKNOWN";
        }

        switch (nutanixStatus.toUpperCase()) {
            case "QUEUED":
                return "PENDING";
            case "RUNNING":
                return "RUNNING";
            case "SUCCEEDED":
                return "SUCCESS";
            case "FAILED":
                return "FAILED";
            case "ABORTED":
            case "CANCELLED":
                return "CANCELLED";
            default:
                return nutanixStatus.toUpperCase();
        }
    }

    /**
     * 转换子网数据为 CI 模型
     * 支持多种 API 版本的子网数据格式，处理数组格式的响应
     * 优先使用 status 部分的数据，因为它代表当前状态
     */
    public static Map<Class, List> convertSubnetData(BaseCloudRequest request, JSONObject response) {
        Map<Class, List> result = new HashMap<>();

        if (response == null) {
            result.put(CmdbSubnetRes.class, new ArrayList<>());
            return result;
        }

        List<CmdbSubnetRes> data = new ArrayList<>();

        // 处理不同的响应格式 - Nutanix API 总是返回数组格式
        if (response.containsKey("entities")) {
            // v3 API 格式：包含 entities 数组
            JSONArray entities = response.getJSONArray("entities");
            for (int i = 0; i < entities.size(); i++) {
                JSONObject nutanixSubnet = entities.getJSONObject(i);
                CmdbSubnetRes subnet = convertSingleSubnet(request, nutanixSubnet);
                if (subnet != null) {
                    data.add(subnet);
                }
            }
        } else if (response.containsKey("networks")) {
            // v2.0 API 格式：包含 networks 数组
            JSONArray networks = response.getJSONArray("networks");
            for (int i = 0; i < networks.size(); i++) {
                JSONObject nutanixSubnet = networks.getJSONObject(i);
                CmdbSubnetRes subnet = convertSingleSubnet(request, nutanixSubnet);
                if (subnet != null) {
                    data.add(subnet);
                }
            }
        } else if (response.containsKey("subnets")) {
            // 其他格式：包含 subnets 数组
            JSONArray subnets = response.getJSONArray("subnets");
            for (int i = 0; i < subnets.size(); i++) {
                JSONObject nutanixSubnet = subnets.getJSONObject(i);
                CmdbSubnetRes subnet = convertSingleSubnet(request, nutanixSubnet);
                if (subnet != null) {
                    data.add(subnet);
                }
            }
        }

        log.info("成功转换 {} 个子网数据", data.size());
        result.put(CmdbSubnetRes.class, data);
        return result;
    }

    /**
     * 转换单个子网对象
     */
    private static CmdbSubnetRes convertSingleSubnet(BaseCloudRequest request, JSONObject nutanixSubnet) {
        if (nutanixSubnet == null) {
            return null;
        }

        CmdbSubnetRes subnet = new CmdbSubnetRes();

        try {
            // 1. 基本信息映射
            mapBasicSubnetInfo(request, nutanixSubnet, subnet);

            // 2. 网络配置映射
            mapSubnetNetworkConfig(nutanixSubnet, subnet);

            // 3. 使用统计映射
            mapSubnetUsageStats(nutanixSubnet, subnet);

            // 4. 时间信息映射
            mapSubnetTimeInfo(nutanixSubnet, subnet);

            // 设置云类型和账号信息
            toCiResCloud(request, subnet);

            return subnet;

        } catch (Exception e) {
            String subnetId = getSubnetId(nutanixSubnet);
            log.error("转换子网数据失败，子网 ID: {}", subnetId, e);
            return null;
        }
    }

    /**
     * 映射子网基本信息
     */
    private static void mapBasicSubnetInfo(BaseCloudRequest request, JSONObject nutanixSubnet, CmdbSubnetRes subnet) {
        // UUID 映射：优先使用 metadata.uuid，然后是 uuid，最后是 id
        String subnetId = null;
        if (nutanixSubnet.containsKey("metadata")) {
            JSONObject metadata = nutanixSubnet.getJSONObject("metadata");
            if (metadata != null && metadata.containsKey("uuid")) {
                subnetId = metadata.getString("uuid");
            }
        }
        if (subnetId == null && nutanixSubnet.containsKey("uuid")) {
            subnetId = nutanixSubnet.getString("uuid");
        }
        if (subnetId == null && nutanixSubnet.containsKey("id")) {
            subnetId = nutanixSubnet.getString("id");
        }

        if (subnetId != null) {
            subnet.setRes_id(IdUtils.encryptId(request.getBody().getAccess().getCmpId(), subnetId));
            subnet.setOpen_id(subnetId);
        }

        // 名称映射：优先使用 status.name，然后是 spec.name
        String subnetName = null;
        if (nutanixSubnet.containsKey("status")) {
            JSONObject status = nutanixSubnet.getJSONObject("status");
            if (status != null && status.containsKey("name")) {
                subnetName = status.getString("name");
            }
        }
        if (subnetName == null && nutanixSubnet.containsKey("spec")) {
            JSONObject spec = nutanixSubnet.getJSONObject("spec");
            if (spec != null && spec.containsKey("name")) {
                subnetName = spec.getString("name");
            }
        }
        if (subnetName == null && nutanixSubnet.containsKey("name")) {
            subnetName = nutanixSubnet.getString("name");
        }
        subnet.setOpen_name(subnetName != null ? subnetName : "Unknown Subnet");

        // 状态映射：优先使用 status.state
        String state = "ACTIVE"; // 默认状态
        if (nutanixSubnet.containsKey("status")) {
            JSONObject status = nutanixSubnet.getJSONObject("status");
            if (status != null && status.containsKey("state")) {
                state = status.getString("state");
            }
        }
        subnet.setStatus(state);
        subnet.setOpen_status(state);

        // 集群引用存储在 extend1 中
        StringBuilder clusterInfo = new StringBuilder();
        if (nutanixSubnet.containsKey("spec")) {
            JSONObject spec = nutanixSubnet.getJSONObject("spec");
            if (spec != null && spec.containsKey("cluster_reference")) {
                JSONObject clusterRef = spec.getJSONObject("cluster_reference");
                if (clusterRef != null) {
                    if (clusterRef.containsKey("uuid")) {
                        String clusterUuid = clusterRef.getString("uuid");
                        clusterInfo.append("cluster_uuid:").append(clusterUuid).append(";");
                    }
                    if (clusterRef.containsKey("name")) {
                        String clusterName = clusterRef.getString("name");
                        clusterInfo.append("cluster_name:").append(clusterName).append(";");
                    }
                }
            }
        }

        // 资源类型信息
        if (nutanixSubnet.containsKey("metadata")) {
            JSONObject metadata = nutanixSubnet.getJSONObject("metadata");
            if (metadata != null && metadata.containsKey("kind")) {
                String kind = metadata.getString("kind");
                clusterInfo.append("resource_kind:").append(kind).append(";");
            }
        }

        if (clusterInfo.length() > 0) {
            subnet.setExtend1(clusterInfo.toString());
        }
    }

    /**
     * 获取子网 ID（用于错误日志）
     */
    private static String getSubnetId(JSONObject nutanixSubnet) {
        if (nutanixSubnet.containsKey("metadata")) {
            JSONObject metadata = nutanixSubnet.getJSONObject("metadata");
            if (metadata != null && metadata.containsKey("uuid")) {
                return metadata.getString("uuid");
            }
        }
        if (nutanixSubnet.containsKey("uuid")) {
            return nutanixSubnet.getString("uuid");
        }
        if (nutanixSubnet.containsKey("id")) {
            return nutanixSubnet.getString("id");
        }
        return "Unknown";
    }

    /**
     * 映射子网网络配置信息（存储在 extend2 中）
     */
    private static void mapSubnetNetworkConfig(JSONObject nutanixSubnet, CmdbSubnetRes subnet) {
        StringBuilder networkConfig = new StringBuilder();

        // 从 spec.resources 获取网络配置
        if (nutanixSubnet.containsKey("spec")) {
            JSONObject spec = nutanixSubnet.getJSONObject("spec");
            if (spec != null && spec.containsKey("resources")) {
                JSONObject resources = spec.getJSONObject("resources");
                if (resources != null) {
                    // VLAN ID
                    if (resources.containsKey("vlan_id")) {
                        Integer vlanId = resources.getInteger("vlan_id");
                        if (vlanId != null) {
                            subnet.setVlan_id(vlanId.toString());
                            networkConfig.append("vlan_id:").append(vlanId).append(";");
                        }
                    }

                    // 子网类型
                    if (resources.containsKey("subnet_type")) {
                        String subnetType = resources.getString("subnet_type");
                        if (subnetType != null) {
                            subnet.setType(subnetType);
                            networkConfig.append("subnet_type:").append(subnetType).append(";");
                        }
                    }

                    // 虚拟交换机名称
                    if (resources.containsKey("vswitch_name")) {
                        String vswitchName = resources.getString("vswitch_name");
                        if (vswitchName != null) {
                            networkConfig.append("vswitch_name:").append(vswitchName).append(";");
                        }
                    }

                    // IP 配置信息
                    if (resources.containsKey("ip_config")) {
                        JSONObject ipConfig = resources.getJSONObject("ip_config");
                        if (ipConfig != null) {
                            if (ipConfig.containsKey("subnet_ip")) {
                                String subnetIp = ipConfig.getString("subnet_ip");
                                if (subnetIp != null) {
                                    subnet.setCidr_ipv4(subnetIp);
                                    networkConfig.append("subnet_ip:").append(subnetIp).append(";");
                                }
                            }
                            if (ipConfig.containsKey("prefix_length")) {
                                Integer prefixLength = ipConfig.getInteger("prefix_length");
                                if (prefixLength != null) {
                                    networkConfig.append("prefix_length:").append(prefixLength).append(";");
                                    // 更新 CIDR 格式
                                    if (subnet.getCidr_ipv4() != null) {
                                        subnet.setCidr_ipv4(subnet.getCidr_ipv4() + "/" + prefixLength);
                                    }
                                }
                            }
                            if (ipConfig.containsKey("default_gateway_ip")) {
                                String gatewayIp = ipConfig.getString("default_gateway_ip");
                                if (gatewayIp != null) {
                                    subnet.setGateway_ipv4(gatewayIp);
                                    networkConfig.append("gateway_ip:").append(gatewayIp).append(";");
                                }
                            }
                        }
                    }
                }
            }
        }

        // 从 status.resources 获取当前状态的网络配置（优先级更高）
        if (nutanixSubnet.containsKey("status")) {
            JSONObject status = nutanixSubnet.getJSONObject("status");
            if (status != null && status.containsKey("resources")) {
                JSONObject resources = status.getJSONObject("resources");
                if (resources != null) {
                    // 覆盖 spec 中的配置
                    if (resources.containsKey("vlan_id")) {
                        Integer vlanId = resources.getInteger("vlan_id");
                        if (vlanId != null) {
                            subnet.setVlan_id(vlanId.toString());
                            // 更新网络配置字符串中的 VLAN ID
                            String configStr = networkConfig.toString();
                            if (configStr.contains("vlan_id:")) {
                                configStr = configStr.replaceAll("vlan_id:\\d+;", "vlan_id:" + vlanId + ";");
                                networkConfig = new StringBuilder(configStr);
                            } else {
                                networkConfig.append("vlan_id:").append(vlanId).append(";");
                            }
                        }
                    }

                    if (resources.containsKey("subnet_type")) {
                        String subnetType = resources.getString("subnet_type");
                        if (subnetType != null) {
                            subnet.setType(subnetType);
                        }
                    }
                }
            }
        }

        if (networkConfig.length() > 0) {
            subnet.setExtend2(networkConfig.toString());
        }
    }

    /**
     * 映射子网使用统计信息（存储在 extend3 中）
     */
    private static void mapSubnetUsageStats(JSONObject nutanixSubnet, CmdbSubnetRes subnet) {
        StringBuilder usageStats = new StringBuilder();

        if (nutanixSubnet.containsKey("status")) {
            JSONObject status = nutanixSubnet.getJSONObject("status");
            if (status != null && status.containsKey("resources")) {
                JSONObject resources = status.getJSONObject("resources");
                if (resources != null && resources.containsKey("ip_usage_stats")) {
                    JSONObject ipUsageStats = resources.getJSONObject("ip_usage_stats");
                    if (ipUsageStats != null) {
                        // MAC 地址数量
                        if (ipUsageStats.containsKey("num_macs")) {
                            Integer numMacs = ipUsageStats.getInteger("num_macs");
                            if (numMacs != null) {
                                usageStats.append("num_macs:").append(numMacs).append(";");
                            }
                        }

                        // 其他可能的使用统计
                        if (ipUsageStats.containsKey("total_ips")) {
                            Integer totalIps = ipUsageStats.getInteger("total_ips");
                            if (totalIps != null) {
                                usageStats.append("total_ips:").append(totalIps).append(";");
                            }
                        }

                        if (ipUsageStats.containsKey("used_ips")) {
                            Integer usedIps = ipUsageStats.getInteger("used_ips");
                            if (usedIps != null) {
                                usageStats.append("used_ips:").append(usedIps).append(";");
                            }
                        }

                        if (ipUsageStats.containsKey("free_ips")) {
                            Integer freeIps = ipUsageStats.getInteger("free_ips");
                            if (freeIps != null) {
                                usageStats.append("free_ips:").append(freeIps).append(";");
                            }
                        }
                    }
                }
            }
        }

        if (usageStats.length() > 0) {
            subnet.setExtend3(usageStats.toString());
        }
    }

    /**
     * 映射子网时间信息
     */
    private static void mapSubnetTimeInfo(JSONObject nutanixSubnet, CmdbSubnetRes subnet) {
        if (nutanixSubnet.containsKey("metadata")) {
            JSONObject metadata = nutanixSubnet.getJSONObject("metadata");
            if (metadata != null) {
                // 创建时间
                if (metadata.containsKey("creation_time")) {
                    String creationTime = metadata.getString("creation_time");
                    try {
                        long createTimeMillis = parseNutanixTime(creationTime);
                        subnet.setCreate_time(createTimeMillis);
                        subnet.setOpen_create_time(createTimeMillis);
                    } catch (Exception e) {
                        log.warn("无法解析子网创建时间: {}", creationTime);
                    }
                }

                // 更新时间 - 注意：CmdbSubnetRes 类中没有 open_update_time 字段
                if (metadata.containsKey("last_update_time")) {
                    String updateTime = metadata.getString("last_update_time");
                    try {
                        long updateTimeMillis = parseNutanixTime(updateTime);
                        subnet.setUpdate_time(updateTimeMillis);
                    } catch (Exception e) {
                        log.warn("无法解析子网更新时间: {}", updateTime);
                    }
                }
            }
        }
    }


}
