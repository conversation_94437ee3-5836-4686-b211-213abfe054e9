package com.futong.gemini.plugin.cloud.nutanix.service;

import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

/**
 * Nutanix 默认参数设置服务类
 * 参考华为云插件标准，确保方法签名符合规范
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class DefaultParamService {
    
    /**
     * 设置默认集群（Nutanix 使用集群概念，不存在传统公有云的区域概念）
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultCluster(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("cluster")) {
            request.getBody().getCloud().put("cluster", "default-cluster");
        }
        return true;
    }
    
    /**
     * 设置默认分页参数（50条）
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultPage50(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("offset")) {
            request.getBody().getCloud().put("offset", 0); // 默认从第0条开始
        }
        if (!request.getBody().getCloud().containsKey("length")) {
            request.getBody().getCloud().put("length", 50); // 默认50条
        }
        return true;
    }
    
    /**
     * 设置默认分页参数（100条）
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("offset")) {
            request.getBody().getCloud().put("offset", 0); // 默认从第0条开始
        }
        if (!request.getBody().getCloud().containsKey("length")) {
            request.getBody().getCloud().put("length", 100); // 默认100条
        }
        return true;
    }
    
    /**
     * 设置默认时间范围（一天）
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultStartEndTimeOneDay(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("startTime")) {
            request.getBody().getCloud().put("startTime", System.currentTimeMillis() - 24 * 60 * 60 * 1000); // 一天前
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", System.currentTimeMillis()); // 当前时间
        }
        return true;
    }
    
    /**
     * 设置默认时间范围（一周）
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultStartEndTimeOneWeek(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("startTime")) {
            request.getBody().getCloud().put("startTime", System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000); // 一周前
        }
        if (!request.getBody().getCloud().containsKey("endTime")) {
            request.getBody().getCloud().put("endTime", System.currentTimeMillis()); // 当前时间
        }
        return true;
    }
    
    /**
     * 设置默认虚拟机配置
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultVmConfig(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("vcpus")) {
            request.getBody().getCloud().put("vcpus", 1); // 默认1个vCPU
        }
        if (!request.getBody().getCloud().containsKey("sockets")) {
            request.getBody().getCloud().put("sockets", 1); // 默认1个socket
        }
        if (!request.getBody().getCloud().containsKey("memory_mb")) {
            request.getBody().getCloud().put("memory_mb", 1024); // 默认1GB内存
        }
        return true;
    }
    
    /**
     * 设置默认监控指标名称
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultInstanceMetricNames(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("metricNames")) {
            request.getBody().getCloud().put("metricNames", "cpu_usage_ppm,memory_usage_ppm,storage_usage_bytes");
        }
        return true;
    }
    
    /**
     * 设置默认监控维度
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultInstanceMetricDimensions(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("dimensions")) {
            request.getBody().getCloud().put("dimensions", "vm_uuid");
        }
        return true;
    }
    
    /**
     * 设置默认存储配置
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultStorageConfig(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("size_gb")) {
            request.getBody().getCloud().put("size_gb", 10); // 默认10GB
        }
        if (!request.getBody().getCloud().containsKey("storage_container")) {
            request.getBody().getCloud().put("storage_container", "default-container");
        }
        return true;
    }
    
    /**
     * 设置默认网络配置
     * 
     * @param request 请求对象
     * @return 是否设置成功
     */
    public static boolean defaultNetworkConfig(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("vlan_id")) {
            request.getBody().getCloud().put("vlan_id", 0); // 默认VLAN 0
        }
        if (!request.getBody().getCloud().containsKey("ip_config")) {
            request.getBody().getCloud().put("ip_config", "dhcp"); // 默认DHCP
        }
        return true;
    }
}
