# Nutanix API 集成需求文档和开发说明

## 1. 需求概述

### 1.1 项目背景
本项目旨在通过 Nutanix Prism Element (v1, v2.0) 和 Prism Central (v3) API 实现对 Nutanix 集群资源的管理，包括虚拟机、主机、存储、快照、告警、事件等功能。开发一个集成模块，支持资源的查询、创建、克隆、电源管理及性能监控等操作。

### 1.2 目标
- 实现对 Nutanix 集群资源的 REST API 调用，涵盖虚拟机管理、主机管理、存储管理、告警和事件查询等。
- 支持 Prism Element v1、v2.0 和 Prism Central v3 API，确保兼容性和可扩展性。
- 提供清晰的错误处理、认证机制和日志记录。
- 为未来迁移至 Nutanix v4 API 预留扩展空间。

### 1.3 对接范围
涵盖以下 API 端点：
- **虚拟机管理**: 查询、创建、克隆、电源管理。
- **主机管理**: 查询主机列表和性能数据。
- **存储管理**: 存储容器、存储池、虚拟磁盘、物理磁盘。
- **快照管理**: 查询快照。
- **告警和事件**: 查询告警和事件。
- **任务管理**: 查询任务状态。
- **网络管理**: 查询虚拟交换机。
- **集群管理**: 查询集群信息和性能数据。

## 2. API 端点说明

以下为 API 端点及其功能、请求方法和参考文档。

### 2.1 Prism Element v2.0 API
- **虚拟机列表** (`/PrismGateway/services/rest/v2.0/vms?include_vm_disk_config=true&include_vm_nic_config=true`)
  - **功能**: 获取集群中所有虚拟机的详细信息，包含磁盘和网卡配置。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - VMs](https://www.nutanix.dev/reference/prism_element/v2/api/vms/get-vms-getvms/)
- **创建虚拟机** (`/PrismGateway/services/rest/v2.0/vms?include_vm_disk_config=true&include_vm_nic_config=true`)
  - **功能**: 创建新的虚拟机，包含磁盘和网卡配置。
  - **方法**: POST
  - **参考文档**: [Prism Element v2.0 - Create VM](https://www.nutanix.dev/reference/prism_element/v2/api/vms/post-vms-postvm/)
- **克隆虚拟机** (`/PrismGateway/services/rest/v2.0/vms`)
  - **功能**: 克隆现有虚拟机。
  - **方法**: POST
  - **参考文档**: [Prism Element v2.0 - VMs](https://www.nutanix.dev/reference/prism_element/v2/api/vms/)
- **虚拟机电源管理** (`/PrismGateway/services/rest/v2.0/vms`)
  - **功能**: 控制虚拟机电源状态（开机、关机、重启）。
  - **方法**: POST
  - **参考文档**: [Prism Element v2.0 - Power Operations](https://www.nutanix.dev/reference/prism_element/v2/api/vms/post-vms-setpowerstate/)
- **主机列表** (`/PrismGateway/services/rest/v2.0/hosts`)
  - **功能**: 获取集群中主机列表。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - Hosts](https://www.nutanix.dev/reference/prism_element/v2/api/hosts/get-hosts-gethosts/)
- **虚拟磁盘** (`/PrismGateway/services/rest/v2.0/virtual_disks`)
  - **功能**: 获取虚拟磁盘列表。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - Virtual Disks](https://www.nutanix.dev/reference/prism_element/v2/api/virtual_disks/get-virtualdisks-getvirtualdisks/)
- **集群列表** (`/PrismGateway/services/rest/v2.0/clusters`)
  - **功能**: 获取集群信息。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - Cluster](https://www.nutanix.dev/reference/prism_element/v2/api/cluster/get-cluster-getclusterinfo/)
- **快照列表** (`/PrismGateway/services/rest/v2.0/snapshots`)
  - **功能**: 获取快照列表。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - Snapshots](https://www.nutanix.dev/reference/prism_element/v2/api/snapshots/get-snapshots-getsnapshots/)
- **告警列表** (`/PrismGateway/services/rest/v2.0/alerts`)
  - **功能**: 获取告警列表。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - Alerts](https://www.nutanix.dev/reference/prism_element/v2/api/alerts/get-alerts-getalerts/)
- **事件列表** (`/PrismGateway/services/rest/v2.0/events`)
  - **功能**: 获取事件列表。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - Events](https://www.nutanix.dev/reference/prism_element/v2/api/events/get-events-getevents/)
- **存储容器** (`/PrismGateway/services/rest/v2.0/storage_containers`)
  - **功能**: 获取存储容器列表。
  - **方法**: GET
  - **参考文档**: [Prism Element v2.0 - Storage Containers](https://www.nutanix.dev/reference/prism_element/v2/api/storage_containers/get-storagecontainers-getstoragecontainers/)
- **任务列表** (`/PrismGateway/services/rest/v2.0/tasks/list`)
  - **功能**: 获取任务列表。
  - **方法**: POST
  - **参考文档**: [Prism Element v2.0 - Tasks](https://www.nutanix.dev/reference/prism_element/v2/api/tasks/get-tasks-list-gettasks/)

### 2.2 Prism Central v3 API
- **虚拟机列表** (`/api/nutanix/v3/vms/list`)
  - **功能**: 获取多集群环境中所有虚拟机的列表。
  - **方法**: POST
  - **参考文档**: [Prism Central v3 - VMs List](https://www.nutanix.dev/reference/prism_central/v3/api/vms/post-vms-list/)
- **子网列表** (`/api/nutanix/v3/subnets/list`)
  - **功能**: 获取子网列表。
  - **方法**: POST
  - **参考文档**: [Prism Central v3 - Subnets List](https://www.nutanix.dev/reference/prism_central/v3/api/subnets/post-subnets-list/)
- **告警列表** (`/api/nutanix/v3/alerts`)
  - **功能**: 获取多集群环境的告警信息。
  - **方法**: GET
  - **参考文档**: [Prism Central v3 - Alerts](https://www.nutanix.dev/reference/prism_central/v3/api/alerts/get-alerts/)
- **任务列表** (`/api/nutanix/v3/tasks`)
  - **功能**: 获取多集群环境中的任务状态。
  - **方法**: GET
  - **参考文档**: [Prism Central v3 - Tasks](https://www.nutanix.dev/reference/prism_central/v3/api/tasks/get-tasks/)

### 2.3 Prism Element v1 API (不推荐)
- **集群性能** (`/PrismGateway/services/rest/v1/cluster/stats`)
  - **功能**: 获取集群性能统计数据。
  - **方法**: GET
  - **参考文档**: [Prism Element v1 - Cluster Stats](https://www.nutanix.dev/reference/prism_element/v1/api/cluster/get-cluster-stats/)
- **主机性能** (`/PrismGateway/services/rest/v1/hosts`)
  - **功能**: 获取主机性能数据。
  - **方法**: GET
  - **参考文档**: [Prism Element v1 - Hosts](https://www.nutanix.dev/reference/prism_element/v1/api/hosts/get-hosts-gethosts/)
- **虚拟机性能** (`/PrismGateway/services/rest/v1/vms`)
  - **功能**: 获取虚拟机性能数据。
  - **方法**: GET
  - **参考文档**: [Prism Element v1 - VMs](https://www.nutanix.dev/reference/prism_element/v1/api/vms/get-vms-getvms/)
- **存储池** (`/PrismGateway/services/rest/v1/storage_pools`)
  - **功能**: 获取存储池列表。
  - **方法**: GET
  - **参考文档**: [Prism Element v1 - Storage Pools](https://www.nutanix.dev/reference/prism_element/v1/api/storage_pools/get-storagepools/)
- **磁盘** (`/PrismGateway/services/rest/v1/disks`)
  - **功能**: 获取物理磁盘信息。
  - **方法**: GET
  - **参考文档**: [Prism Element v1 - Disks](https://www.nutanix.dev/reference/prism_element/v1/api/disks/get-disks-getdisks/)

### 2.4 其他 API
- **虚拟交换机** (`/api/networking/v2.a1/dvs/virtual-switches`)
  - **功能**: 获取虚拟交换机信息（可能与 Nutanix Flow Networking 相关）。
  - **方法**: GET
  - **参考文档**: [Nutanix Flow Networking API](https://www.nutanix.dev/api-reference/?namespace=networking)（可能需要联系 Nutanix 支持获取完整文档）。

## 3. 开发要求

### 3.1 技术栈
- **编程语言**: Java（基于现有 `NutanixURL` 类）。
- **HTTP 客户端**: 推荐使用 Apache HttpClient 或 OkHttp，支持 HTTPS 请求和基本认证。
- **JSON 处理**: 使用 Jackson 或 Gson 处理 API 请求和响应的 JSON 数据。
- **日志记录**: 使用 SLF4J/Logback 记录 API 调用和错误信息。
- **错误处理**: 实现统一的异常处理机制，捕获 HTTP 状态码（401、403、404、500 等）并提供用户友好的错误提示。

### 3.2 认证
- **方式**: 使用 HTTP 基本认证（用户名和密码）或 IAM API 密钥。
- **要求**:
  - 所有请求通过 HTTPS 端口 9440 发送。
  - 确保用户名和密码安全存储（建议使用环境变量或密钥管理服务）。
  - 支持动态切换 Prism Element 和 Prism Central 的认证凭据。

### 3.3 API 调用规范
- **请求格式**:
  - GET 请求：用于查询资源（如虚拟机、主机、告警等），支持查询参数（如 `include_vm_disk_config`）。
  - POST 请求：用于创建、克隆或更新资源，请求体需为 JSON 格式，遵循 API 文档中的 schema。
- **响应处理**:
  - 解析 JSON 响应，提取关键字段（如 `entities`、`metadata`）。
  - 处理分页（对于 `/vms/list`、`/subnets/list` 等 API，需支持 `offset` 和 `limit` 参数）。
- **超时和重试**:
  - 设置合理的请求超时（建议 30 秒）。
  - 实现重试机制（针对 429 Too Many Requests 或 503 Service Unavailable 错误）。

### 3.4 功能实现
- **虚拟机管理**:
  - 查询虚拟机列表，解析磁盘和网卡配置。
  - 支持创建虚拟机，包含 CPU、内存、磁盘和网络配置。
  - 实现虚拟机克隆功能，支持指定目标存储容器。
  - 实现电源管理（开机、关机、重启）。
- **主机和集群**:
  - 查询主机和集群信息，提取状态和性能指标。
- **存储管理**:
  - 查询存储容器、存储池、虚拟磁盘和物理磁盘信息。
- **快照和任务**:
  - 查询快照和任务状态，跟踪异步操作（如虚拟机创建）。
- **告警和事件**:
  - 查询告警和事件，支持按时间范围和严重性过滤。
- **网络管理**:
  - 查询虚拟交换机信息，验证与 Nutanix Flow 的兼容性。

### 3.5 性能优化
- **缓存**: 对频繁查询的静态数据（如集群信息）实现本地缓存，减少 API 调用。
- **批量操作**: 对于列表查询（如 `/vms/list`），支持批量获取以减少请求次数。
- **异步处理**: 对耗时操作（如任务状态检查）使用异步调用。

## 4. 注意事项

### 4.1 版本兼容性
- 确保 Nutanix AOS 和 Prism Central 版本支持所需 API：
  - Prism Element v2.0 需要 AOS 5.0 或更高版本。
  - Prism Central v3 需要 Prism Central pc.2021.9 或更高版本。
  - 虚拟交换机 API（`/api/networking/v2.a1`）可能需要特定 Nutanix Flow 模块支持。

### 4.2 API 迁移
- Nutanix v0.8、v1、v2 和 v3 API 将于 2026 年 Q4 停止支持，建议为 v4 API 迁移预留扩展点。
- 参考 [Nutanix v4 API Migration Guide](https://www.nutanix.dev/api-reference-v4/)，计划使用 v4 API 的命名空间（如 `vmm` 用于虚拟机管理）。

### 4.3 错误处理
- **常见错误**:
  - 401 Unauthorized：检查认证凭据。
  - 404 Not Found：验证 URL 和资源 UUID。
  - 429 Too Many Requests：实现指数退避重试。
- **日志**: 记录所有 API 请求和响应的详细信息，包括 HTTP 状态码和错误消息。

### 4.4 文档和支持
- **官方文档**: 参考 [www.nutanix.dev](https://www.nutanix.dev) 获取最新 API 规范和 OpenAPI 文件。
- **REST API Explorer**: 通过 Prism Central/Element 的用户界面测试 API 请求。
- **社区支持**: 使用 [Nutanix Community](https://next.nutanix.com) 或联系 Nutanix 支持获取帮助。

### 4.5 开发问题
- **依赖引用问题**: ActionType缺少的时候询问之后进行处理。
## 5. 开发任务分解

### 5.1 任务清单
1. **初始化**:
   - 配置 HTTP 客户端和认证模块。
   - 实现 `NutanixURL` 类中的 URL 动态加载。
2. **虚拟机管理模块**:
   - 实现虚拟机查询、创建、克隆和电源管理。
   - 解析磁盘和网卡配置。
3. **主机和集群模块**:
   - 实现主机和集群信息查询。
   - 处理性能统计数据。
4. **存储管理模块**:
   - 实现存储容器、存储池、虚拟磁盘和物理磁盘查询。
5. **快照和任务模块**:
   - 实现快照查询和任务状态跟踪。
6. **告警和事件模块**:
   - 实现告警和事件查询，支持过滤。
7. **网络管理模块**:
   - 实现虚拟交换机查询，验证兼容性。
8. **测试和验证**:
   - 编写单元测试，覆盖所有 API 端点。
   - 在 Nutanix 环境中验证功能（需要测试集群）。


## 6. 交付物
- **代码库**: 包含 Java 实现的 API 客户端代码
- **文档**: API 使用说明、错误处理指南和测试用例。
- **测试报告**: 覆盖所有 API 端点的功能测试和性能测试结果。
- **迁移计划**: v4 API 迁移的可行性分析和初步方案。