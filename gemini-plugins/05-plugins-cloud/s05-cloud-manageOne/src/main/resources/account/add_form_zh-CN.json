{"description": "初始纳管账号仅支持主账号纳管", "model": [{"type": "main", "name": "云平台运营主账号", "description": "云平台运营主账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "云账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入云账号"}, {"field": "username", "label": "运维管理员-用户名", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入运维管理员用户名"}, {"field": "password", "label": "运维管理员-密码", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入运维管理员密钥"}, {"field": "serverIp", "label": "运维API-服务地址", "type": "input", "value": "", "required": true, "isUpdate": false, "tips": "请输入运维API服务地址"}, {"field": "serverPort", "label": "运维API-端口", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入运维API服务端口"}, {"field": "protocol", "label": "运维API-协议类型", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "HTTP"}, {"label": "HTTPS", "value": "HTTPS"}], "required": true, "isUpdate": true, "tips": "请选择运维API协议类型"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}, {"scope": "resource", "type": "sub", "name": "云平台运营子账号", "description": "云平台运营子账号,可用于云资源获取!", "form": [{"field": "cloudAccount", "label": "租户账号", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入租户账号"}, {"field": "username", "label": "租户账号-用户名", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入租户账号用户名"}, {"field": "password", "label": "租户账号-密码", "type": "password", "value": "", "required": true, "isUpdate": true, "tips": "请输入租户账号密码"}, {"field": "serverIp", "label": "运营IAM-服务地址", "type": "input", "value": "", "required": true, "isUpdate": false, "tips": "请输入运营IAM服务地址"}, {"field": "serverPort", "label": "运营IAM-端口", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入运营IAM服务端口"}, {"field": "protocol", "label": "运营IAM-协议类型", "type": "select", "value": "", "items": [{"label": "HTTP", "value": "HTTP"}, {"label": "HTTPS", "value": "HTTPS"}], "required": true, "isUpdate": true, "tips": "请选择运营IAM协议类型"}, {"field": "projectId", "label": "项目", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入项目"}, {"field": "domain", "label": "区域", "type": "input", "value": "", "required": true, "isUpdate": true, "tips": "请输入区域"}, {"field": "jsonStr.AK", "label": "代理地址", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入对象存储访问密钥ID"}, {"field": "jsonStr.SK", "label": "代理地址", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入对象存储密钥"}, {"field": "jsonStr.endpoint", "label": "代理地址", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入对象存储访问地址"}, {"field": "description", "label": "描述", "type": "input", "value": "", "required": false, "isUpdate": true, "tips": "请输入描述"}]}]}