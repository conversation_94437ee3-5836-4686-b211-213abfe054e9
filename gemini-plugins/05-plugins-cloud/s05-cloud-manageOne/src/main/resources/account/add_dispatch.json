{"data": [{"required": true, "unique": true, "dispatcher_info": {"jobName": "同步地域", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/30 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步地域", "jobInfo": "{\"action\":\"FetchPlatformRegion\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步可用区", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/30 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步云主机", "jobInfo": "{\"action\":\"FetchPlatformAzone\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步租户", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/30 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步租户", "jobInfo": "{\"action\":\"FetchPlatformTenant\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步vdc下资源空间", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/30 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步vdc下资源空间", "jobInfo": "{\"action\":\"FetchPlatformVdcProject\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步云主机", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步云主机", "jobInfo": "{\"action\":\"FetchComputeInstance\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步云主机云盘关联关系", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步云主机云盘关联关系", "jobInfo": "{\"action\":\"FetchComputeInstanceRelation\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步云主机性能信息", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步云主机性能信息", "jobInfo": "{\"action\":\"FetchComputeInstancePerf\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步规格", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/45 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步规格", "jobInfo": "{\"action\":\"FetchComputeFlavor\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步镜像", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/45 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步镜像", "jobInfo": "{\"action\":\"FetchStorageImage\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取云盘", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取云盘", "jobInfo": "{\"action\":\"FetchStorageDisk\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取经典网络", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/50 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取经典网络", "jobInfo": "{\"action\":\"FetchNeutronSwitch\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取vpc", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/50 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取vpc", "jobInfo": "{\"action\":\"FetchNeutronVpc\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取子网", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/35 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取子网", "jobInfo": "{\"action\":\"FetchNeutronSubnet\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取密钥对", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/35 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取密钥对", "jobInfo": "{\"action\":\"FetchPlatformKeypair\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取安全组", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/10 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取安全组", "jobInfo": "{\"action\":\"FetchComputeSecuritygroup\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取快照", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取快照", "jobInfo": "{\"action\":\"FetchStorageSnapshot\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步物理机节点", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/15 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步物理机节点", "jobInfo": "{\"action\":\"FetchComputeHost\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取物理机性能", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource_perf", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取物理机性能", "jobInfo": "{\"action\":\"FetchComputeHostPerf\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "获取网卡", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/50 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "获取网卡", "jobInfo": "{\"action\":\"FetchNeutronNic\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步密钥对", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步密钥对", "jobInfo": "{\"action\":\"FetchPlatformKeypair\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步告警", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步告警", "jobInfo": "{\"action\":\"FetchPlatformAlarm\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}, {"required": true, "unique": true, "dispatcher_info": {"jobName": "同步路由", "jobDrive": "timer", "jobLevel": "${cmpId}", "jobPriority": "高", "triggerType": "cron", "triggerTime": "0 0/5 * * * ?", "triggerStart": null, "triggerEnd": null, "geminiRealm": "#{plugin_realm}", "geminiVersion": "#{plugin_version}", "mqExchange": null, "mqRoutingKey": "cmp_resource,cmp_resource_relation,bxc_resource", "versionSpan": 0, "ignoreBatchFail": 0, "tscTimeout": 0, "description": "同步路由", "jobInfo": "{\"action\":\"FetchNeutronRoute\",\"body\":{\"auth\": {\"cmpId\": \"${cmpId}\"}}}", "triggerInfo": "{\"schedueStartTimeInTime\":true,\"schedueEndTimeLongTerm\":true,\"strategyType\":\"周期性\",\"strategyRate\":\"每5分钟\",\"strategyPeriod\":5,\"strategyPeriodUnit\":\"分钟\",\"periodicFrequency22Val\":\"22\",\"periodicFrequency1_22Val\":\"1\",\"periodicFrequency1_22Val2\":\"22\",\"periodicFrequency22_23_20Val\":\"22\",\"periodicFrequency22_23_20Val2\":\"23\",\"periodicFrequency22_23_20Val3\":\"20\",\"frequencyRules\":\"特定时间执行一次\"}"}}]}