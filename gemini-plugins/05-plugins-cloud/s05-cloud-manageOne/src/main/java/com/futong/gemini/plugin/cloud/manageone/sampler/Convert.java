package com.futong.gemini.plugin.cloud.manageone.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.constant.dict.*;
import com.futong.gemini.model.api.entity.BaseSearchApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.common.BuilderResourceSet;
import com.futong.gemini.model.otc.bxc.entity.*;
import com.futong.gemini.model.otc.common.utils.IdUtils;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.nxc.common.Association;
import com.futong.gemini.model.otc.nxc.common.AssociationUtils;
import com.futong.gemini.model.otc.nxc.entity.*;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.sdk.constant.BaseConstant;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.DiskCategory;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.IpType;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.OsType;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.obs.services.ObsClient;
import com.obs.services.exception.ObsException;
import com.obs.services.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class Convert {
    public static Map<Class, List> convertRegion(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbTenant> regionInfos = new ArrayList<>();
        List<TmdbTenantLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            TmdbTenant tenant;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                tenant = new TmdbTenant();
                tenant.setCloud_type(request.getPlugin().getRealm());
                tenant.setOpen_id(infoObj.getString("id"));
                tenant.setFull_name(infoObj.getString("name"));
                tenant.setSimpl_name(infoObj.getString("nativeId"));
                tenant.setAccount_id(accessBean.getCmpId());
                tenant.setDict_code(DevopsSide.DEVOPS_REGION.value());
                tenant.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), DevopsSide.DEVOPS_REGION.value(), infoObj.getString("id")));
                tenant.setInfo_json(infoObj.toJSONString());
                tenant.setStatus("normal".equals(infoObj.getString("status"))?1:0);
                regionInfos.add(tenant);

                TmdbTenantLink link = new TmdbTenantLink();
                link.setParent_tenant_id(null);
                link.setTenant_id(tenant.getBiz_id());
                link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_tenant_id(), link.getTenant_id()}));
                links.add(link);
            }
        }
        Map<Class, List> result = new HashMap<>();
        log.info("发送tenant数量:{}",regionInfos.size());
        result.put(TmdbTenant.class, regionInfos);
        result.put(TmdbTenantLink.class, links);
        return result;
    }

    public static Map<Class, List> convertZone(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbTenant> zoneInfos = new ArrayList<>();
        List<TmdbTenantLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                TmdbTenant tenant = new TmdbTenant();
                tenant.setCloud_type(request.getPlugin().getRealm());
                tenant.setOpen_id(infoObj.getString("id"));
                tenant.setFull_name(infoObj.getString("name"));
                tenant.setSimpl_name(infoObj.getString("globalName"));
                tenant.setAccount_id(accessBean.getCmpId());
                tenant.setDict_code(DevopsSide.DEVOPS_ZONE.value());
                tenant.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), DevopsSide.DEVOPS_ZONE.value(), infoObj.getString("id")));
                tenant.setInfo_json(infoObj.toJSONString());
                TmdbTenantLink link = new TmdbTenantLink();
                link.setTenant_id(tenant.getBiz_id());
                link.setParent_tenant_id(IdUtils.encryptId(accessBean.getCmpId(), DevopsSide.DEVOPS_REGION.value(), infoObj.getString("bizRegionId")));
                link.setBiz_id(IdUtils.encryptId(link.getParent_tenant_id(), link.getTenant_id()));
                zoneInfos.add(tenant);
                links.add(link);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbTenant.class, zoneInfos);
        result.put(TmdbTenantLink.class, links);
        return result;
    }

    public static Map<Class, List> convertTenant(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbDevops> tenantInfos = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                TmdbDevops tenant = new TmdbDevops();
                tenant.setCloud_type(request.getPlugin().getRealm());
                tenant.setDevops_value(infoObj.getString("nativeId"));
                tenant.setDevops_name(infoObj.getString("name"));
                tenant.setAccount_id(accessBean.getCmpId());
                tenant.setDict_code("tenant");
                tenant.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "tenant", infoObj.getString("nativeId")));
                tenant.setInfo_json(infoObj.toJSONString());
                tenantInfos.add(tenant);

                TmdbDevopsLink link = new TmdbDevopsLink();
                link.setDevops_id(tenant.getBiz_id());
                link.setParent_devops_id(null);
                link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_devops_id(), link.getDevops_id()}));
                links.add(link);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbDevops.class, tenantInfos);
        result.put(TmdbDevopsLink.class, links);
        return result;
    }

    public static Map<Class, List> convertVDC(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbTenant> vdcInfos = new ArrayList<>();
        List<TmdbTenantLink> links = new ArrayList<>();
        List<TmdbDevopsTenant> devopsTenants = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObj = array.getJSONObject(i);
                TmdbTenant vdc = new TmdbTenant();
                vdc.setCloud_type(request.getPlugin().getRealm());
                vdc.setOpen_id(jsonObj.getString("nativeId"));
                vdc.setFull_name(jsonObj.getString("name"));
                vdc.setAccount_id(accessBean.getCmpId());
                vdc.setDict_code("vdc");
                vdc.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "vdc", jsonObj.getString("nativeId")));
                vdc.setInfo_json(jsonObj.toJSONString());
                vdcInfos.add(vdc);
                //三层关联表
                TmdbTenantLink link = new TmdbTenantLink();
                link.setTenant_id(vdc.getBiz_id());
                String level = jsonObj.getString("level");
                if ("1".equals(level)) {//1级VDC
                    link.setBiz_id(IdUtils.encryptId("top", link.getTenant_id()));
                }else{
                    link.setParent_tenant_id(IdUtils.encryptId(accessBean.getCmpId(), "vdc", jsonObj.getString("upperVdcId")));
                    link.setBiz_id(IdUtils.encryptId(link.getParent_tenant_id(), link.getTenant_id()));
                }
                links.add(link);
                //关联租户
                TmdbDevopsTenant devopsTenant = new TmdbDevopsTenant();
                devopsTenant.setDevops_id(vdc.getBiz_id());
                devopsTenant.setTenant_id(IdUtils.encryptId(accessBean.getCmpId(), "tenant", jsonObj.getString("domainId")));
                devopsTenant.setDirection("ALL");
                devopsTenant.setBiz_id(IdUtils.encryptId(devopsTenant.getDevops_id(), devopsTenant.getTenant_id()));
                devopsTenants.add(devopsTenant);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbTenant.class, vdcInfos);
        result.put(TmdbTenantLink.class, links);
        result.put(TmdbDevopsTenant.class, devopsTenants);
        return result;
    }

    public static Map<Class, List> convertTenantProject(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        log.info("采集tenant_project_vdc---size=======",array.size());
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbDevops> projectInfos = new ArrayList<>();
        List<TmdbTenant> projectTenantInfos = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject vdcObj = array.getJSONObject(i);

                TmdbDevops project = new TmdbDevops();
                project.setCloud_type(request.getPlugin().getRealm());
                project.setDevops_value(vdcObj.getString("nativeId"));
                project.setDevops_name(vdcObj.getString("projectName"));
                project.setAccount_id(accessBean.getCmpId());
                project.setDict_code("project");
                project.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "project", vdcObj.getString("nativeId")));
                project.setInfo_json(vdcObj.toJSONString());
                projectInfos.add(project);

                TmdbTenant tenant = new TmdbTenant();
                tenant.setCloud_type(request.getPlugin().getRealm());
                tenant.setOpen_id(vdcObj.getString("nativeId"));
                tenant.setFull_name(vdcObj.getString("projectName"));
                tenant.setAccount_id(accessBean.getCmpId());
                tenant.setDict_code("region_project");
                tenant.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(), "region_project", vdcObj.getString("nativeId")));
                tenant.setInfo_json(vdcObj.toJSONString());
                tenant.setStatus(1);
                projectTenantInfos.add(tenant);

                TmdbDevopsLink link = new TmdbDevopsLink();
                link.setDevops_id(IdUtils.encryptId(accessBean.getCmpId(),  "project", vdcObj.getString("nativeId")));
                link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), "tenant", vdcObj.getString("domainNativeId")));
                link.setBiz_id(IdUtils.encryptId(link.getParent_devops_id(), link.getDevops_id()));
                links.add(link);
            }
        }
        Map<Class, List> result = new HashMap<>();
        log.info("采集project条数{}",projectInfos.size());
        result.put(TmdbDevops.class, projectInfos);
        log.info("采集project关联条数{}",links.size());
        result.put(TmdbDevopsLink.class, links);
        log.info("采集projecttenant条数{}",projectTenantInfos.size());
        result.put(TmdbTenant.class, projectTenantInfos);
        return result;
    }

    public static Map<Class, List> convertVDCProject(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbDevops> vdcInfos = new ArrayList<>();
        List<TmdbDevopsLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject vdcObj = array.getJSONObject(i);
                TmdbDevops vdc = new TmdbDevops();
                vdc.setCloud_type(request.getPlugin().getRealm());
                vdc.setDevops_value(vdcObj.getString("nativeId"));
                vdc.setDevops_name(vdcObj.getString("projectName"));
                vdc.setAccount_id(accessBean.getCmpId());
                vdc.setDict_code("project");
                vdc.setBiz_id(IdUtils.encryptId(accessBean.getCmpId(),  "project", vdcObj.getString("nativeId")));
                vdc.setInfo_json(vdcObj.toJSONString());
                vdcInfos.add(vdc);
                TmdbDevopsLink link = new TmdbDevopsLink();
                link.setDevops_id(vdc.getBiz_id());
                link.setParent_devops_id(IdUtils.encryptId(accessBean.getCmpId(), "vdc", vdcObj.getString("vdcId")));
                link.setBiz_id(IdUtils.encryptId(link.getParent_devops_id(), link.getDevops_id()));
                links.add(link);
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(TmdbDevops.class, vdcInfos);
        result.put(TmdbDevopsLink.class, links);
        return result;
    }
    public static List<TmdbTenantLink> convertProjectRegion(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        log.info("采集tenant_project_region--list====="+array.size());
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbTenantLink> links = new ArrayList<>();
        if (CollUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                TmdbTenantLink link = new TmdbTenantLink();
                link.setParent_tenant_id(IdUtils.encryptId(accessBean.getCmpId(), DevopsSide.DEVOPS_REGION.value(), infoObj.getString("logicalRegionId")));
                link.setTenant_id(IdUtils.encryptId(accessBean.getCmpId(), "region_project", infoObj.getString("projectNativeId")));
                link.setBiz_id(IdUtils.encryptId(new String[]{link.getParent_tenant_id(), link.getTenant_id()}));
                links.add(link);
            }
        }
        return links;
    }

    public static Map<Class, List> convertEcs(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbInstanceRes> vmInfos = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        List<CmdbIpRes> ips = new ArrayList<>();
        List<CmdbOsRes> osList = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbInstanceRes instance = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject vmObj = array.getJSONObject(i);
                instance = new CmdbInstanceRes();
                instance.setCloud_type(request.getPlugin().getRealm());
                switch (vmObj.getString("status")) {
                    case "active":
                        instance.setStatus(InstanceStatus.RUNNING.value());
                        break;
                    case "stopped":
                        instance.setStatus(InstanceStatus.STOPPED.value());
                        break;
                    default:
                        instance.setStatus(InstanceStatus.UNKNOWN.value());
                        break;
                }
                instance.setOpen_id(vmObj.getString("id"));
                instance.setOpen_name(vmObj.getString("name"));
                instance.setIs_template(0);
                instance.setMem_size(Integer.parseInt(vmObj.getString("flavorRamSize")==null?"0":vmObj.getString("flavorRamSize")));
                instance.setCpu_size(Integer.parseInt(vmObj.getString("cpuCoreNum")));
                instance.setOpen_status(vmObj.getString("status"));
                instance.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), instance.getOpen_id()));
                instance.setAccount_id(accessBean.getCmpId());
                vmInfos.add(instance);

                CmdbOsRes os = new CmdbOsRes();
                os.setFull_name(vmObj.getString("osVersion"));
                if (ObjectUtil.isNotNull(vmObj.getString("osType")) && vmObj.getString("osType").contains("indows")) {
                    os.setType(OsType.WINDOWS.getValue());
                } else {
                    os.setType(OsType.LINUX.getValue());
                }
                os.setOpen_id(vmObj.getString("imageId"));
                os.setOpen_name(vmObj.getString("osVersion"));
                os.setCloud_type(accessBean.getCloudType());
                os.setAccount_id(accessBean.getCmpId());
                os.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_OS_RES.value(), os.getOpen_id()));
                osList.add(os);
                associations.add(AssociationUtils.toAssociation(instance, os));

                CmdbIpRes ip = new CmdbIpRes();
                ip.setAddress(vmObj.getString("ipAddress"));
                ip.setAccount_id(accessBean.getCmpId());
                ip.setCloud_type(accessBean.getCloudType());
                ip.setType(IpType.PRIVATE_IP.getValue());
                ip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_IP_RES.value(), vmObj.getString("ipAddress")));
                ips.add(ip);
                //云主机和IP的关系数据
                associations.add(AssociationUtils.toAssociation(toCiResCloud(accessBean, instance.getOpen_id(), new CmdbInstanceRes()), ip));

                CmdbHostRes host = new CmdbHostRes();
                host.setAccount_id(accessBean.getCmpId());
                host.setCloud_type(accessBean.getCloudType());
                host.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), vmObj.getString("hostId")));
                associations.add(AssociationUtils.toAssociation(host, instance));

                CmdbImageRes image = new CmdbImageRes();
                image.setAccount_id(accessBean.getCmpId());
                image.setCloud_type(request.getPlugin().getRealm());
                image.setOpen_id(vmObj.getString("imageId"));
                image.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), image.getOpen_id()));
                //云主机和镜像的关系数据
                associations.add(AssociationUtils.toAssociation(image, instance));

                CmdbFlavor flavor = new CmdbFlavor();
                flavor.setAccount_id(accessBean.getCmpId());
                flavor.setCloud_type(request.getPlugin().getRealm());
                flavor.setOpen_id(vmObj.getString("flavorId"));
                flavor.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), flavor.getOpen_id()));
                //云主机和规格的关系数据
                associations.add(AssociationUtils.toAssociation(flavor, instance));
                if(ObjectUtil.isNotEmpty(vmObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, vmObj.getString("tenantId"), ResourceType.CMDB_INSTANCE_RES.value(), instance.getOpen_id()));
                if(ObjectUtil.isNotEmpty(vmObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, vmObj.getString("projectId"), ResourceType.CMDB_INSTANCE_RES.value(), instance.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbInstanceRes.class, vmInfos);
        result.put(Association.class, associations);
        result.put(CmdbIpRes.class, ips);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertVmRelation(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<Association> associations = new ArrayList<>();
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject vmObj = array.getJSONObject(i);
                CmdbInstanceRes instance = new CmdbInstanceRes();
                instance.setAccount_id(accessBean.getCmpId());
                instance.setCloud_type(request.getPlugin().getRealm());
                instance.setOpen_id(vmObj.getString("vmId"));
                instance.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), vmObj.getString("vmId")));
                CmdbDiskRes diskRes = new CmdbDiskRes();
                diskRes.setAccount_id(accessBean.getCmpId());
                diskRes.setCloud_type(request.getPlugin().getRealm());
                diskRes.setOpen_id(vmObj.getString("volumeId"));
                diskRes.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), vmObj.getString("volumeId")));
                //云主机和云盘的关系数据
                associations.add(AssociationUtils.toAssociation(diskRes, instance));
            }
        }
        Map<Class, List> result = new HashMap<>();
        log.info("采集云主机关联关系数量:{}",associations.size());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertAclRelation(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<Association> associations = new ArrayList<>();
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject vmObj = array.getJSONObject(i);
                CmdbVfwRes vfw = new CmdbVfwRes();
                vfw.setAccount_id(accessBean.getCmpId());
                vfw.setCloud_type(request.getPlugin().getRealm());
                vfw.setOpen_id(vmObj.getString("vfwId"));
                vfw.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), "cmdb_vfw_res", vmObj.getString("vfwId")));
                //防火墙和策略的关系数据
                associations.add(AssociationUtils.toAssociation(vfw, CmdbVfwPolicy.class,vmObj.getString("policyId"), vmObj.getString("ruleType"),""));
            }
        }
        Map<Class, List> result = new HashMap<>();
        log.info("采集虚拟防火墙关系数量:{}",associations.size());
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertAclPolicyRelation(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<Association> associations = new ArrayList<>();
        if (array != null && array.size() > 0) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject vmObj = array.getJSONObject(i);
                CmdbVfwPolicy policy = new CmdbVfwPolicy();
                policy.setAccount_id(accessBean.getCmpId());
                policy.setCloud_type(request.getPlugin().getRealm());
                policy.setOpen_id(vmObj.getString("policyId"));
                policy.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), "cmdb_vfw_policy", vmObj.getString("policyId")));
                CmdbVfwRes vfw = new CmdbVfwRes();
                vfw.setAccount_id(accessBean.getCmpId());
                vfw.setCloud_type(request.getPlugin().getRealm());
                vfw.setOpen_id(vmObj.getString("ruleId"));
                vfw.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), "cmdb_vfw_rule", vmObj.getString("ruleId")));
                //防火墙和策略的关系数据
                associations.add(AssociationUtils.toAssociation(vfw, policy));
            }
        }
        Map<Class, List> result = new HashMap<>();
        log.info("采集虚拟防火墙策略关系数量:{}",associations.size());
        result.put(Association.class, associations);
        return result;
    }

    public static void main(String[] args) {

        System.out.println(IdUtils.encryptId("70f60516a99de949a36ae3b258fa8c40", "vdc", "********-e6dd-4a06-8ccb-1c515d310ecb"));
    }

    public static Map<Class, List> convertImage(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbImageRes> imageInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbImageRes image = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                image = new CmdbImageRes();
                image.setCloud_type(request.getPlugin().getRealm());
                image.setOpen_id(infoObj.getString("id"));
                /**
                 * 封装资源唯一ID
                 */
                image.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_IMAGE_RES.value(), image.getOpen_id()));
                image.setOpen_name(infoObj.getString("name"));
                image.setAccount_id(accessBean.getCmpId());
                image.setStatus(infoObj.getString("status"));
                image.setOpen_status(infoObj.getString("status"));
                image.setImage_source("system");
                image.setVisibility("public");
                image.setSize(infoObj.getLong("imageSize") == null ? 0 : Float.valueOf(String.format("%.2f", infoObj.getLong("imageSize") / 1024.0 / 1024 / 1024.0)));
                image.setMin_disk(infoObj.getFloat("minDisk"));
                image.setType("image");
                imageInfos.add(image);

                CmdbOsRes os = new CmdbOsRes();
                os.setFull_name(infoObj.getString("osVersion"));
                if (ObjectUtil.isNotNull(infoObj.getString("osType"))&&infoObj.getString("osType").contains("indows")) {
                    os.setType(OsType.WINDOWS.getValue());
                } else {
                    os.setType(OsType.LINUX.getValue());
                }
                os.setOpen_id(infoObj.getString("id"));
                os.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_OS_RES.value(), os.getOpen_id()));
                os.setName(infoObj.getString("name"));
                os.setType(infoObj.getString("osType"));
                os.setVersion(infoObj.getString("osVersion"));
                associations.add(AssociationUtils.toAssociation(image, os));

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_IMAGE_RES.value(), image.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_IMAGE_RES.value(), image.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbImageRes.class, imageInfos);
        result.put(Association.class, associations);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertFlavor(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbFlavor> flavorInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbFlavor flavor = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                flavor = new CmdbFlavor();
                flavor.setOpen_id(infoObj.getString("id"));
                flavor.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_FLAVOR.value(), flavor.getOpen_id()));
                flavor.setOpen_name(infoObj.getString("name"));
                flavor.setCloud_type(request.getPlugin().getRealm());
                flavor.setAccount_id(accessBean.getCmpId());
                flavor.setCpu_size(infoObj.getInteger("vcpuSize"));
                flavor.setMem_size(infoObj.getInteger("ramSize") / 1024);
                flavor.setCategory("cpu");
                flavor.setDefault_sysdisk_size(infoObj.getFloat("diskSize"));
                flavorInfos.add(flavor);
                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_FLAVOR.value(), flavor.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_FLAVOR.value(), flavor.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbFlavor.class, flavorInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertHost(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbHostRes> hostInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbHostRes nxcHostRes = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                nxcHostRes = new CmdbHostRes();
                nxcHostRes.setOpen_id(infoObj.getString("id"));
                nxcHostRes.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), nxcHostRes.getOpen_id()));
                nxcHostRes.setOpen_name(infoObj.getString("name"));
                nxcHostRes.setIp(infoObj.getString("ipAddress"));
                nxcHostRes.setCloud_type(request.getPlugin().getRealm());
                nxcHostRes.setCpu_size(infoObj.getInteger("totalVcpuCores") == null ? 0 : infoObj.getInteger("totalVcpuCores"));
                nxcHostRes.setMem_size(infoObj.getInteger("totalVmemoryMB"));
                nxcHostRes.setAccount_id(accessBean.getCmpId());
                nxcHostRes.setSn(infoObj.getString("serialNumber"));
                nxcHostRes.setTotal_size(Float.parseFloat(NumberUtil.roundStr(infoObj.getLong("totalDiskSizeMB") / 1024f, 2)));
                nxcHostRes.setUsed_size(Float.parseFloat(NumberUtil.roundStr(infoObj.getLong("allocatedDiskSizeMB") * 1f / infoObj.getLong("totalDiskSizeMB"), 2)));

                switch (infoObj.getString("status")) {
                    case "normal":
                        nxcHostRes.setStatus(InstanceStatus.RUNNING.value());
                        break;
                    default:
                        nxcHostRes.setStatus(InstanceStatus.STOPPED.value());
                        break;
                }
                nxcHostRes.setOpen_status(infoObj.getString("status"));

                hostInfos.add(nxcHostRes);
                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_HOST_RES.value(), nxcHostRes.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_HOST_RES.value(), nxcHostRes.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbHostRes.class, hostInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static List<AlarmInfoBean> convertAlarm(BaseCloudRequest request, JSONArray array) {
        List<AlarmInfoBean> alarmList = new ArrayList<>();
        try {
            CloudAccessBean accessBean = request.getBody().getAccess();
            if (array != null && array.size() > 0) {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject alarmObj = array.getJSONObject(i);
                    AlarmInfoBean alarm = new AlarmInfoBean();
                    alarm.setId(IdUtils.encryptId(accessBean.getCmpId(), alarmObj.getInteger("csn").toString(), alarmObj.getLong("occurUtc") + ""));
                    alarm.setAccountId(accessBean.getCmpId());
                    alarm.setCloudType(request.getPlugin().getRealm());
                    alarm.setOpenId(alarmObj.getInteger("csn").toString());
                    alarm.setOpenName(alarmObj.getString("alarmName"));
                    alarm.setAlarmId(alarmObj.getInteger("csn").toString());
                    alarm.setAlarmName(alarmObj.getString("alarmName"));
                    alarm.setDetail(alarmObj.getString("additionalInformation"));
                    alarm.setClosedStatus(alarmObj.getInteger("cleared")==0?false:true);
                    alarm.setClosedTime(alarmObj.getLong("clearTime")==0?null:DateUtil.formatDateTime(new Date(alarmObj.getLong("clearTime"))));
                    alarm.setClosedUserName(alarmObj.getString("clearUser"));
                    alarm.setCount(1);
                    alarm.setCreateTime(DateUtil.formatDateTime(new Date(alarmObj.getLong("occurUtc"))));
                    alarm.setResourceType(alarmObj.getString("meType"));
//                    if ("vm".equalsIgnoreCase(alarmObj.getString("meType"))) {
//                        alarm.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
//                        alarm.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), alarmObj.getString("meDn")));
//                    } else if ("host".equalsIgnoreCase(alarmObj.getString("meType"))) {
//                        alarm.setResourceType(ResourceType.CMDB_HOST_RES.value());
//                        alarm.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), alarmObj.getString("meDn")));
//                    } else if ("network".equalsIgnoreCase(alarmObj.getString("meType"))) {
//                        alarm.setResourceType(ResourceType.CMDB_NETCARD_RES.value());
//                        alarm.setResId(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_NETCARD_RES.value(), alarmObj.getString("meDn")));
//                    }
                    switch (alarmObj.getString("severity")) {
                        case "1":
                            alarm.setAlarmLevel(AlarmLevel.CRITICAL.value());
                            break;
                        case "2":
                            alarm.setAlarmLevel(AlarmLevel.MAJOR.value());
                            break;
                        case "3":
                            alarm.setAlarmLevel(AlarmLevel.DEBUG.value());
                            break;
                        case "4":
                            alarm.setAlarmLevel(AlarmLevel.INFORMATION.value());
                            break;
                    }
                    alarmList.add(alarm);
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        log.info("推送告警数据={}",alarmList.size());

        return alarmList;
    }

    public static Map<Class, List> convertNetwork(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbVswitchRes> vswitchInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbVswitchRes vswitch = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                vswitch = new CmdbVswitchRes();
                vswitch.setOpen_id(infoObj.getString("id"));
                vswitch.setOpen_name(infoObj.getString("name"));
                vswitch.setCloud_type(request.getPlugin().getRealm());
                vswitch.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_VSWITCH_RES.value(), vswitch.getOpen_id()));
                vswitch.setAccount_id(accessBean.getCmpId());
                vswitch.setType(infoObj.getString("providerNetworkType"));
                vswitch.setStatus(infoObj.getString("status"));
                vswitch.setOpen_status(infoObj.getString("status"));
                vswitchInfos.add(vswitch);


                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_VSWITCH_RES.value(), vswitch.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_VSWITCH_RES.value(), vswitch.getOpen_id()));

            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbVswitchRes.class, vswitchInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertSubnet(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        List<CmdbSubnetRes> subnetInfos = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbSubnetRes subnet = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                subnet = new CmdbSubnetRes();
                subnet.setOpen_id(infoObj.getString("id"));
                subnet.setOpen_name(infoObj.getString("name"));
                subnet.setCloud_type(request.getPlugin().getRealm());
                subnet.setAccount_id(accessBean.getCmpId());
                subnet.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), subnet.getOpen_id()));
                subnet.setCidr_ipv4(infoObj.getString("cidr"));
                subnet.setGateway_ipv4(infoObj.getString("gatewayIp"));
                subnet.setDhcp_enable(infoObj.getBoolean("enableDhcp") ? 1 : 0);
                subnetInfos.add(subnet);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_SUBNET_RES.value(), subnet.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_SUBNET_RES.value(), subnet.getOpen_id()));

            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbSubnetRes.class, subnetInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertPort(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        Map<Class, List> result = new HashMap<>();
        return result;
    }

    public static Map<Class, List> convertRoute(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbRouteRes> routeInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbRouteRes route = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                route = new CmdbRouteRes();
                route.setOpen_id(infoObj.getString("id"));
                route.setOpen_name(infoObj.getString("name"));
                route.setCloud_type(request.getPlugin().getRealm());
                route.setAccount_id(accessBean.getCmpId());
                route.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_ROUTE_RES.value(), route.getOpen_id()));
                route.setStatus(infoObj.getString("status"));
                routeInfos.add(route);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_ROUTE_RES.value(), route.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_ROUTE_RES.value(), route.getOpen_id()));

            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbRouteRes.class, routeInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertEip(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbEipRes> eipInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbEipRes eip = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                eip = new CmdbEipRes();
                eip.setOpen_id(infoObj.getString("id"));
                eip.setOpen_name(infoObj.getString("name"));
                eip.setFloating_ip(infoObj.getString("floatingIpAddress"));
                eip.setFixed_ip(infoObj.getString("fixedIpAddress"));
                eip.setStatus(infoObj.getString("status"));
                eip.setOpen_status(infoObj.getString("status"));
                eip.setCloud_type(request.getPlugin().getRealm());
                eip.setAccount_id(accessBean.getCmpId());
                eip.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_ROUTE_RES.value(), eip.getOpen_id()));
                eip.setStatus(infoObj.getString("status"));
                eipInfos.add(eip);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_ROUTE_RES.value(), eip.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_ROUTE_RES.value(), eip.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbEipRes.class, eipInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertAcl(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbVfwRes> vfwInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbVfwRes vfw = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                vfw = new CmdbVfwRes();
                vfw.setOpen_id(infoObj.getString("id"));
                vfw.setOpen_name(infoObj.getString("name"));
                vfw.setDesc(infoObj.getString("description"));
                vfw.setStatus(infoObj.getString("status"));
                vfw.setOpen_status(infoObj.getString("status"));
                vfw.setCloud_type(request.getPlugin().getRealm());
                vfw.setAccount_id(accessBean.getCmpId());
                vfw.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), "cmdb_vfw_res", vfw.getOpen_id()));
                vfw.setStatus(infoObj.getString("status"));
                vfwInfos.add(vfw);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), "cmdb_vfw_res", vfw.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), "cmdb_vfw_res", vfw.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbVfwRes.class, vfwInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertAclPolicy(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbVfwPolicy> vfwInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbVfwPolicy vfw = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                vfw = new CmdbVfwPolicy();
                vfw.setOpen_id(infoObj.getString("id"));
                vfw.setOpen_name(infoObj.getString("name"));
                vfw.setDesc(infoObj.getString("description"));
                vfw.setAudited(infoObj.getBoolean("audited")?1:0);
                vfw.setStatus(infoObj.getString("status"));
                vfw.setOpen_status(infoObj.getString("status"));
                vfw.setCloud_type(request.getPlugin().getRealm());
                vfw.setAccount_id(accessBean.getCmpId());
                vfw.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), "cmdb_vfw_policy", vfw.getOpen_id()));
                vfw.setStatus(infoObj.getString("status"));
                vfwInfos.add(vfw);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), "cmdb_vfw_policy", vfw.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), "cmdb_vfw_policy", vfw.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbVfwPolicy.class, vfwInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertAclRule(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbVfwRule> vfwInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbVfwRule vfw = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                vfw = new CmdbVfwRule();
                vfw.setOpen_id(infoObj.getString("id"));
                vfw.setOpen_name(infoObj.getString("name"));
                vfw.setDesc(infoObj.getString("description"));
                vfw.setAction(infoObj.getString("action"));
                vfw.setEnabled(infoObj.getBoolean("enabled")?1:0);
                vfw.setStatus(infoObj.getString("managedStatus"));
                vfw.setOpen_status(infoObj.getString("status"));
                vfw.setCloud_type(request.getPlugin().getRealm());
                vfw.setAccount_id(accessBean.getCmpId());
                vfw.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), "cmdb_vfw_rule", vfw.getOpen_id()));
                vfw.setStatus(infoObj.getString("status"));
                vfwInfos.add(vfw);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), "cmdb_vfw_rule", vfw.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), "cmdb_vfw_rule", vfw.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbVfwRule.class, vfwInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertSecurity(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbSecuritygroupRes> sgInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbSecuritygroupRes securitygroup = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                securitygroup = new CmdbSecuritygroupRes();
                securitygroup.setAccount_id(accessBean.getCmpId());
                securitygroup.setCloud_type(request.getPlugin().getRealm());
                securitygroup.setOpen_id(infoObj.getString("id"));
                securitygroup.setOpen_name(infoObj.getString("name"));
                securitygroup.setRes_id(IdUtils.encryptId(accessBean.getCmpId(),request.getPlugin().getRealm(), ResourceType.CMDB_SECURITYGROUP_RES.value(), securitygroup.getOpen_id()));
                sgInfos.add(securitygroup);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_SECURITYGROUP_RES.value(), securitygroup.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_SECURITYGROUP_RES.value(), securitygroup.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbSecuritygroupRes.class, sgInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertSecurityRule(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbSecuritygroupRule> ruleInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        List<Association> associations = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbSecuritygroupRule rule = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                rule = new CmdbSecuritygroupRule();
                rule.setAccount_id(accessBean.getCmpId());
                rule.setCloud_type(request.getPlugin().getRealm());
                rule.setOpen_id(infoObj.getString("id"));
                rule.setDirection(infoObj.getString("direction"));
                rule.setDesc(infoObj.getString("direction"));
                rule.setIp_protocol(infoObj.getString("protocol"));
                rule.setPort_range(infoObj.getString("portRangeMin")+"-"+infoObj.getString("portRangeMax"));
                rule.setSource_cidr(infoObj.getString("remoteIpPrefix"));
                rule.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), request.getPlugin().getRealm(), ResourceType.CMDB_SECURITYGROUP_RULE.value(), rule.getOpen_id()));
                ruleInfos.add(rule);

                CmdbSecuritygroupRes group = new CmdbSecuritygroupRes();
                group.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), request.getPlugin().getRealm(), ResourceType.CMDB_SECURITYGROUP_RES.value(), infoObj.getString("securityGroupId")));
                group.setOpen_id(infoObj.getString("securityGroupId"));
                associations.add(AssociationUtils.toAssociation(group,rule));

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_SECURITYGROUP_RULE.value(), rule.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_SECURITYGROUP_RULE.value(), rule.getOpen_id()));

            }

        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbSecuritygroupRule.class, ruleInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        result.put(Association.class, associations);
        return result;
    }

    public static Map<Class, List> convertStoragePool(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbStoragePoolRes> poolInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbStoragePoolRes pool = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                pool = new CmdbStoragePoolRes();
                pool.setOpen_id(infoObj.getString("id"));
                pool.setOpen_name(infoObj.getString("name"));
                pool.setCloud_type(request.getPlugin().getRealm());
                pool.setAccount_id(accessBean.getCmpId());
                pool.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), pool.getOpen_id()));
                pool.setTotal_size(infoObj.getFloat("totalCapacityGB"));
                pool.setUsed_size(infoObj.getFloat("provisionedCapacityGB"));
                poolInfos.add(pool);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_STORAGE_POOL_RES.value(), pool.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_STORAGE_POOL_RES.value(), pool.getOpen_id()));
            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbStoragePoolRes.class, poolInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertSnapshot(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbSnapshotRes> snapshotInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbSnapshotRes snapshot = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                snapshot = new CmdbSnapshotRes();
                snapshot.setOpen_id(infoObj.getString("id"));
                snapshot.setOpen_name(infoObj.getString("name"));
                snapshot.setCloud_type(request.getPlugin().getRealm());
                snapshot.setAccount_id(accessBean.getCmpId());
                snapshot.setSize(Float.parseFloat(infoObj.getString("size")));
                snapshot.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_SNAPSHOT_RES.value(), snapshot.getOpen_id()));
                snapshot.setStatus(infoObj.getString("status"));
                snapshot.setOpen_status(infoObj.getString("status"));
                snapshot.setType(ResourceType.CMDB_DISK_RES.value());
                snapshotInfos.add(snapshot);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_SNAPSHOT_RES.value(), snapshot.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_SNAPSHOT_RES.value(), snapshot.getOpen_id()));

            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbSnapshotRes.class, snapshotInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static Map<Class, List> convertBucket(BaseCloudRequest request, List<ObsBucket> buckets, ObsClient obsClient) {
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbBucketRes> bucketInfos = new ArrayList<>();
        if (buckets != null && buckets.size() > 0) {
            CmdbBucketRes bucketRes = null;
            for (int i = 0; i < buckets.size(); i++) {
                ObsBucket bucket = buckets.get(i);
                bucketRes = new CmdbBucketRes();
                BucketVersioningConfiguration bucketVersioning = null;
                try {
                    bucketVersioning = obsClient.getBucketVersioning(bucket.getBucketName());
                    String versionStatus = Opt.ofBlankAble(bucketVersioning)
                            .map(BucketVersioningConfiguration::getVersioningStatus)
                            .map(statusEnum -> JSONUtil.toJsonStr(statusEnum.getCode()))
                            .orElse(null);
                    bucketRes.setVersioning_status(versionStatus);
                } catch (ObsException e) {
                    log.error("查询OBS bucket版本信息异常");
                }
                BucketCustomDomainInfo bucketCustomDomain = null;
                try {
                    bucketCustomDomain = obsClient.getBucketCustomDomain(bucket.getBucketName());
                    String domains = Opt.ofBlankAble(bucketCustomDomain)
                            .map(bucketCustomDomainInfo -> JSONUtil.toJsonStr(bucketCustomDomainInfo.getDomains()))
                            .orElse(null);
                    bucketRes.setCross_domain(domains);
                } catch (ObsException e) {
                    log.error("查询OBS bucket自定义域名信息异常");
                }
                LifecycleConfiguration bucketLifecycle = null;
                try {
                    bucketLifecycle = obsClient.getBucketLifecycle(bucket.getBucketName());
                    String lifecycleRules = Opt.ofBlankAble(bucketLifecycle)
                            .map(lifecycle -> JSONUtil.toJsonStr(lifecycle.getRules()))
                            .orElse(null);
                    bucketRes.setLifecycle(lifecycleRules);
                } catch (ObsException e) {
                    log.error("查询OBS bucket生命周期信息异常");
                }
                BucketLoggingConfiguration bucketLogging = null;
                try {
                    bucketLogging = obsClient.getBucketLogging(bucket.getBucketName());
                    bucketRes.setLogging(JSONUtil.toJsonStr(bucketLogging));
                } catch (ObsException e) {
                    log.error("查询OBS bucket日志信息异常");
                }
                String bucketPolicy = null;
                try {
                    bucketPolicy = obsClient.getBucketPolicy(bucket.getBucketName());
                    bucketRes.setPolicy(bucketPolicy);
                } catch (ObsException e) {
                    log.error("查询OBS bucket策略信息异常");
                }
                BucketStorageInfo bucketStorageInfo = null;
                try {
                    bucketStorageInfo = obsClient.getBucketStorageInfo(bucket.getBucketName());
                    Long objectNumber = Opt.ofBlankAble(bucketStorageInfo)
                            .map(BucketStorageInfo::getObjectNumber)
                            .orElse(null);
                    Long bucketSize = Opt.ofBlankAble(bucketStorageInfo)
                            .map(BucketStorageInfo::getSize)
                            .orElse(null);
                    bucketRes.setSize(Float.parseFloat(bucketSize+""));
                    bucketRes.setObject_number(objectNumber+"");
                } catch (ObsException e) {
                    log.error("查询OBS bucket存储信息异常");
                }

                if (bucket.getAcl() != null) {
                    String acl = Opt.ofBlankAble(bucket.getAcl())
                            .map(AccessControlList::getGrantAndPermissions)
                            .map(permissions -> ArrayUtil.join(permissions, ","))
                            .orElse(null);
                    bucketRes.setAcl(acl);
                }
                bucketRes.setOpen_name(bucket.getBucketName());
                bucketRes.setCloud_type(request.getPlugin().getRealm());
                bucketRes.setAccount_id(accessBean.getCmpId());
                bucketRes.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_BUCKET_RES.value(), bucket.getBucketName()));
                bucketRes.setType(ResourceType.CMDB_BUCKET_RES.value());
                bucketInfos.add(bucketRes);
            }
        }
        try {
            obsClient.close();
        }catch (Exception e) {
            e.printStackTrace();
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbSnapshotRes.class, bucketInfos);
        return result;
    }

    public static Map<Class, List> convertVmDisk(BaseCloudRequest request, JSONObject response) {
        JSONArray array = response.getJSONArray("objList");
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<CmdbDiskRes> diskInfos = new ArrayList<>();
        List<TmdbResourceSet> resourceSets = new ArrayList<>();
        if (array != null && array.size() > 0) {
            CmdbDiskRes disk = null;
            for (int i = 0; i < array.size(); i++) {
                JSONObject infoObj = array.getJSONObject(i);
                disk = new CmdbDiskRes();
                disk.setOpen_id(infoObj.getString("id"));
                disk.setOpen_name(infoObj.getString("name"));
                disk.setCloud_type(request.getPlugin().getRealm());
                disk.setAccount_id(accessBean.getCmpId());
                disk.setRes_id(IdUtils.encryptId(accessBean.getCmpId(), accessBean.getCloudType(), ResourceType.CMDB_DISK_RES.value(), disk.getOpen_id()));
                disk.setSize(Float.parseFloat(infoObj.getString("size")));
                disk.setType(infoObj.getString("volumeTypeId"));
                disk.setCategory(DiskCategory.DATA.getValue());
                disk.setOpen_status(infoObj.getString("status"));
                disk.setStatus("in-use".equals(infoObj.getString("status")) ? DiskStatus.IN_USE.value() : DiskStatus.AVAILABLE.value());
                diskInfos.add(disk);

                if(ObjectUtil.isNotEmpty(infoObj.getString("tenantId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "tenant", TmdbDevops.class, infoObj.getString("tenantId"), ResourceType.CMDB_DISK_RES.value(), disk.getOpen_id()));
                if(ObjectUtil.isNotEmpty(infoObj.getString("projectId")))
                    resourceSets.add(toTmdbResourceSet(accessBean, "project", TmdbDevops.class, infoObj.getString("projectId"), ResourceType.CMDB_DISK_RES.value(), disk.getOpen_id()));

            }
        }
        Map<Class, List> result = new HashMap<>();
        result.put(CmdbDiskRes.class, diskInfos);
        result.put(TmdbResourceSet.class, resourceSets);
        return result;
    }

    public static BasePageSortSearchRequest toBasePageSortSearchRequest(BaseCloudRequest request) {
        BasePageSortSearchRequest searchRequest = new BasePageSortSearchRequest();
        if (ObjectUtil.isNotNull(request)) {
            searchRequest.setCurrent(request.getBody().getCloud().getInteger("pageNo"));
            searchRequest.setSize(request.getBody().getCloud().getInteger("pageSize"));
            searchRequest.setSortField(BaseConstant.RES_ID);
            searchRequest.setSort(0);
        } else {
            searchRequest.setCurrent(1);
            searchRequest.setSize(50);
            searchRequest.setSortField(BaseConstant.RES_ID);
            searchRequest.setSort(0);
        }
        List<BaseSearchApiModel> searchList = new ArrayList<>();
        BaseSearchApiModel searchApiModel = new BaseSearchApiModel();
        searchApiModel.setKey(BaseConstant.ACCOUNT_ID);
        searchApiModel.setValue(request.getBody().getAccess().getCmpId());
        searchApiModel.setSearchClassiy("0");
        searchList.add(searchApiModel);
        searchRequest.setSearchList(searchList);
        searchRequest.setSearchList(searchList);
        return searchRequest;
    }

    public static TmdbResourceSet toTmdbResourceSet(CloudAccessBean bean, String setType, Class c, String setId, String resourceType, String resourceId) {
        TmdbResourceSet set = new TmdbResourceSet();
        set.setAccount_id(bean.getCmpId());
        set.setCloud_type(bean.getCloudType());
        set.setResource_type(resourceType);
        set.setResource_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), resourceType, resourceId));
        set.setResource_type(resourceType);
        set.setSet_type(setType);
        set.setSet_table(AssociationUtils.otcTableName(c));
        set.setSet_id(IdUtils.encryptId(bean.getCmpId(),  setType, setId));
        set.setBiz_id(IdUtils.encryptId(bean.getCmpId(), set.getSet_id(), set.getResource_id()));
        return set;
    }

    public static <T> T toCiResCloud(CloudAccessBean bean, String id, T classBean) {
        if (classBean instanceof CmdbHostRes) {
            CmdbHostRes host = (CmdbHostRes) classBean;
            host.setCloud_type(bean.getCloudType());
            host.setAccount_id(bean.getCmpId());
            host.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_HOST_RES.value(), id));
            return (T) host;
        } else if (classBean instanceof CmdbInstanceRes) {
            CmdbInstanceRes instance = (CmdbInstanceRes) classBean;
            instance.setCloud_type(bean.getCloudType());
            instance.setAccount_id(bean.getCmpId());
            instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_INSTANCE_RES.value(), id));
            return (T) instance;
        } else if (classBean instanceof CmdbSubnetRes) {
            CmdbSubnetRes instance = (CmdbSubnetRes) classBean;
            instance.setCloud_type(bean.getCloudType());
            instance.setAccount_id(bean.getCmpId());
            instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_SUBNET_RES.value(), id));
            return (T) instance;
        } else if (classBean instanceof CmdbStoragePoolRes) {
            CmdbStoragePoolRes instance = (CmdbStoragePoolRes) classBean;
            instance.setCloud_type(bean.getCloudType());
            instance.setAccount_id(bean.getCmpId());
            instance.setRes_id(IdUtils.encryptId(bean.getCmpId(), bean.getCloudType(), ResourceType.CMDB_STORAGE_POOL_RES.value(), id));
            return (T) instance;
        }
        return classBean;
    }
}
