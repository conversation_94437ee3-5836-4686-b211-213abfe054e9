package com.futong.gemini.plugin.cloud.manageone.client;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.util.List;
import java.util.Map;

@Slf4j
public class ManageOneClient {
    protected final CloseableHttpClient httpClient;
    protected final ConnectionConfig config;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String X_AUTH_TOKEN = "X-Auth-Token";

    public ManageOneClient(ConnectionConfig config) {
        this.config = config;
        this.httpClient = createHttpClient(config);
    }

    private CloseableHttpClient createHttpClient(ConnectionConfig config) {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(config.getConnectTimeout())
                .setSocketTimeout(config.getSocketTimeout())
                .build();

        try {
            // 忽略SSL证书验证
            SSLContext sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, (chain, authType) -> true)
                    .build();
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            return HttpClientBuilder.create()
                    .setDefaultRequestConfig(requestConfig)
                    .setSSLSocketFactory(sslSocketFactory)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create HTTP client with SSL context", e);
        }
    }

    public String getToken(String projectId) {
        Object token = null;
        String key = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getProjectId();
//        log.info("getoekn----key---{}",key);
        try {
            token = GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.FIXED, key);
//            log.info("获取---token={}",token);
        } catch (Exception e) {
            GuavaCacheUtils.del(GuavaCacheUtils.Mode.FIXED, key);
        }
//        log.info("token---ObjectUtil.isNull(token)---{}", ObjectUtil.isNull(token));
        if (ObjectUtil.isNull(token)||StrUtil.isNotEmpty(projectId)) {
            try {
                token = doToken(projectId);
            } catch (Exception e) {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD.ofI18n("gemini.public.token.fail"), e);
            }
//            log.info("调用云上接口获取token并存入缓存内  = {}", JSON.toJSONString(token));
            GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, key, token);
        }
        return token.toString();
    }


    public String getUrl(String project ,String type) {
        getToken(project);
        String key = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId()+"/"+type;
        String url = GuavaCacheUtils.get(GuavaCacheUtils.Mode.FIXED, key)==null?"https://ccs.ca-global-1.camcloud.changan.com/v1.0":GuavaCacheUtils.get(GuavaCacheUtils.Mode.FIXED, key);
        return url;
    }

    public String getUserId() {
        getToken("");
        String key = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId()+"/userId";
        String userId = GuavaCacheUtils.get(GuavaCacheUtils.Mode.FIXED, key);
        return userId;
    }

    protected synchronized String doToken(String projectId) throws Exception {
        AuthConfig auth = config.getAuthConfig();
        String url = config.getManageOneEndpoint() + "/v3/auth/tokens";
        String authJson = String.format("{" +
                        "\"auth\": {" +
                        "\"identity\": {" +
                        "\"methods\": [\"password\"]," +
                        "\"password\": {" +
                        "\"user\": {" +
                        "\"domain\": {\"name\": \"%s\"}," +
                        "\"name\": \"%s\"," +
                        "\"password\": \"%s\"" +
                        "}" +
                        "}" +
                        "}," +
                        "\"scope\": {" +
                        "\"project\": {\"id\": \"%s\"}" +
                        "}" +
                        "}" +
                        "}",
                auth.getDomain(),
                auth.getUsername(),
                auth.getPassword(),
                auth.getProjectId());

        log.info("调用云上接口manageoneClinent运营url  = {},authJson={}", url,authJson);

        HttpResponse response = HttpRequest.post(url)
                .body(authJson)
                .execute();
        int statusCode = response.getStatus();
        if (statusCode < 200 || statusCode >= 300) {
            throw new RuntimeException("API request failed: " + response.body());
        }
        //加上验证code信息
        String token = response.header("X-Subject-Token");
//        log.info("运营认证token----{}",token);
        return token;
    }

    // 通用请求执行方法
    private String executeRequest(HttpRequestBase request) throws Exception {
        String token = getToken("");
        request.setHeader(X_AUTH_TOKEN, token);
        //设置超时时间
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = EntityUtils.toString(response.getEntity());
            if (response.getStatusLine().getStatusCode() < 200 ||
                    response.getStatusLine().getStatusCode() >= 300) {
                throw new RuntimeException("API request failed: " + responseBody);
            }
            return responseBody;
        }
    }

    private String executeRequest2(String url) throws Exception {
        String token = getToken("");
        HttpResponse response = HttpRequest.get(url)
                .header(X_AUTH_TOKEN, token)
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .execute();
        int statusCode = response.getStatus();
        if (statusCode < 200 || statusCode >= 300) {
            throw new RuntimeException("API request failed: " + response.body());
        }
        log.info("response.status---{}", response.getStatus());
        String body = response.body();
        return body;
    }



    // GET请求
    public String doGet(String apiPath, Map<String, String> params) throws Exception {
        String url = config.getManageOneEndpoint() + apiPath;
        if (params != null && !params.isEmpty()) {
            url += "?" + FTHttpUtils.toFormString(params);
        }
        return executeRequest(new HttpGet(url));
    }

    public String doGet2(String url, Map<String, String> params) throws Exception {
        if (params != null && !params.isEmpty()) {
            url += "?" + FTHttpUtils.toFormString(params);
        }
        return executeRequest2(url);
    }

    public String doDelete2(String url, Map<String, String> params) throws Exception {
        if (params != null && !params.isEmpty()) {
            url += "?" + FTHttpUtils.toFormString(params);
        }
        return executeRequest(new HttpDelete(url));
    }

    // POST JSON请求
    public String doPostJson(String apiPath, Object requestBody) throws Exception {
        HttpPost request = new HttpPost(config.getManageOneEndpoint() + apiPath);
        String json = objectMapper.writeValueAsString(requestBody);
        log.info("POST JSON请求url  = {}", config.getManageOneEndpoint() + apiPath);
        log.info("POST JSON请求body  = {}", json);
        request.setEntity(new StringEntity(json));
        return executeRequest(request);
    }

    public String doPostJson2(String apiPath, Object requestBody) throws Exception {
        HttpPost request = new HttpPost(apiPath);
        String json = objectMapper.writeValueAsString(requestBody);
        log.info("POST JSON请求url  = {}", apiPath);
        log.info("POST JSON请求body  = {}", json);
        request.setEntity(new StringEntity(json));
        String result = executeRequest(request);
        log.info("POST result  = {}", result);
        return result;
    }

    public String doPutJson(String apiPath, Object requestBody) throws Exception {
        HttpPut request = new HttpPut(config.getManageOneEndpoint() + apiPath);
        String json = objectMapper.writeValueAsString(requestBody);
        request.setEntity(new StringEntity(json));
        return executeRequest(request);
    }

    // 响应解析工具方法
    public <T> T parseResponse(String json, Class<T> clazz) throws Exception {
        return objectMapper.readValue(json, clazz);
    }

    // 北向接口通用查询资源信息《ManageOne 8.2.1 北向API应用开发指南 01》
    public JSONObject doNorthApiInstances(String className, Map<String, String> params) throws Exception {
        String apiPath = StrUtil.format("/rest/cmdb/v1/instances/{class-name}", className);
        String result = doGet(apiPath, params);
        return JSONObject.parseObject(result);
    }

}
