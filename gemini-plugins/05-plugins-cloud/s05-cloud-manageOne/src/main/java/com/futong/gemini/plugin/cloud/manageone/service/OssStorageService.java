package com.futong.gemini.plugin.cloud.manageone.service;

import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.obs.services.ObsClient;
import com.obs.services.model.*;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;

@Slf4j
public class OssStorageService {

    /**
     * 创建对象存储桶
     * @param request
     * @return
     */
    public static BaseResponse createOssBucket(BaseCloudRequest request){
        try {
            ObsClient obsClient = ClientUtils.obsClient(request.getBody());
            CreateBucketRequest req = new CreateBucketRequest();
            req.setBucketName(request.getBody().getCloud().getString("name"));
            // 设置桶访问权限为公共读，默认是私有读写
            req.setAcl(AccessControlList.
                    REST_CANNED_PUBLIC_READ);
            // 创建桶
            ObsBucket bucket = obsClient.createBucket(req);
            return new BaseDataResponse<>(bucket);
        }catch (Exception e){
            log.error("创建对象存储桶失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建对象存储桶失败!"), e);
        }
    }

    /**
     * 删除对象存储桶
     * @param request
     * @return
     */
    public static BaseResponse deleteOssBucket(BaseCloudRequest request){
        try {
            ObsClient obsClient = ClientUtils.obsClient(request.getBody());
            // 删除桶
            HeaderResponse name = obsClient.deleteBucket(request.getBody().getCloud().getString("name"));
            return new BaseDataResponse<>(name);
        }catch (Exception e){
            log.error("删除对象存储桶失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除对象存储桶失败!"), e);
        }
    }

    public static BaseResponse uploadFile(BaseCloudRequest request){
        try {
            ObsClient obsClient = ClientUtils.obsClient(request.getBody());
            PutObjectRequest req = new PutObjectRequest();
            req.setBucketName(request.getBody().getCloud().getString("name"));
            req.setObjectKey(request.getBody().getCloud().getString("key"));
            req.setFile(new File(request.getBody().getCloud().getString("file")));
            PutObjectResult putObjectResult = obsClient.putObject(req);
            return new BaseDataResponse<>(putObjectResult);
        }catch (Exception e){
            log.error("上传文件失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("上传文件失败!"), e);
        }
    }

    public static BaseResponse downloadFile(BaseCloudRequest request){
        try {
            ObsClient obsClient = ClientUtils.obsClient(request.getBody());
            ObsObject obsObject = obsClient.getObject(request.getBody().getCloud().getString("name"), request.getBody().getCloud().getString("key"));
            InputStream input = obsObject.getObjectContent();
            byte[] b = new byte[1024];
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            int len;
            while ((len=input.read(b)) != -1){
                bos.write(b, 0, len);
            }
            return new BaseDataResponse<>(bos);
        }catch (Exception e){
            log.error("下载文件失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("下载件失败!"), e);
        }
    }

    public static BaseResponse deleteFile(BaseCloudRequest request){
        try {
            ObsClient obsClient = ClientUtils.obsClient(request.getBody());
            DeleteObjectResult deleteObjectResult = obsClient.deleteObject(request.getBody().getCloud().getString("name"), request.getBody().getCloud().getString("key"));
            return new BaseDataResponse<>(deleteObjectResult);
        }catch (Exception e){
            log.error("删除文件失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除文件失败!"), e);
        }
    }

    public static BaseResponse createFolder(BaseCloudRequest request){
        try {
            ObsClient obsClient = ClientUtils.obsClient(request.getBody());
            final String keySuffixWithSlash = request.getBody().getCloud().getString("folder");
            PutObjectResult putObjectResult = obsClient.putObject(request.getBody().getCloud().getString("name"), keySuffixWithSlash, new ByteArrayInputStream(new byte[0]));
            // 在文件夹下创建对象
            return new BaseDataResponse<>(putObjectResult);
        }catch (Exception e){
            log.error("创建目录失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建目录失败!"), e);
        }
    }

    public static BaseResponse listObject(BaseCloudRequest request){
        try {
            ObsClient obsClient = ClientUtils.obsClient(request.getBody());
            ObjectListing result = obsClient.listObjects(request.getBody().getCloud().getString("name"));
            return new BaseDataResponse<>(result.getObjects());
        }catch (Exception e){
            log.error("获取桶对象失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取桶对象失败!"), e);
        }
    }
}
