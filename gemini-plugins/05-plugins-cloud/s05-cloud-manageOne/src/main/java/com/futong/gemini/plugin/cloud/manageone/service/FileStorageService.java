package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileStorageService {

    /**
     * 创建文件共享
     * @param request
     * @return
     */
    public static BaseResponse createFs(BaseCloudRequest request){
        try {
            SCClient client = ClientUtils.client(SCClient.class, request.getBody());
            String projectId = request.getBody().getCloud().getString("projectId");
            String sfsUrl = client.getUrl(projectId,"sfs");
            log.info("----------创建文件共享url----------{}",sfsUrl);
            JSONObject cloud = request.getBody().getCloud();
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数资源空间不能为空!");
            String url =  sfsUrl + "/v2/"+projectId+"/shares";
            JSONObject body = convertCreateFs(cloud);
            client.doPostAction2(url, body);
            return new BaseDataResponse<>("");
        }catch (Exception e){
            log.error("创建文件共享失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建文件共享失败!"), e);
        }
    }

    public static BaseResponse updateFs(BaseCloudRequest request){
        try {
            SCClient client = ClientUtils.client(SCClient.class, request.getBody());
            String projectId = request.getBody().getCloud().getString("projectId");
            String sfsUrl = client.getUrl(projectId,"sfs");
            log.info("----------修改文件共享url----------{}",sfsUrl);
            JSONObject cloud = request.getBody().getCloud();
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数资源空间不能为空!");
            String url =  sfsUrl + "/v2/"+projectId+"/shares/"+request.getBody().getCloud().getString("shareId");
            JSONObject body = convertCreateFs(cloud);
            client.doPutJson(url, body);
            return new BaseDataResponse<>("");
        }catch (Exception e){
            log.error("修改文件存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改文件存储失败!"), e);
        }
    }

    /**
     * 删除文件存储
     * @param request
     * @return
     */
    public static BaseResponse deleteFs(BaseCloudRequest request){
        try {
            SCClient client = ClientUtils.client(SCClient.class, request.getBody());
            String projectId = request.getBody().getCloud().getString("projectId");
            String sfsUrl = client.getUrl(projectId,"sfs");
            log.info("----------创建文件共享url----------{}",sfsUrl);
            JSONObject cloud = request.getBody().getCloud();
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数资源空间不能为空!");
            String url =  sfsUrl + "/v2/"+projectId+"/shares";
            JSONObject body = convertCreateFs(cloud);
            client.doPostAction2(url, body);
            return new BaseDataResponse<>("");
        }catch (Exception e){
            log.error("删除文件存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除文件存储失败!"), e);
        }
    }

    /**
     * 扩容文件存储
     * @param request
     * @return
     */
    public static BaseResponse resizeFs(BaseCloudRequest request){
        try {
            SCClient client = ClientUtils.client(SCClient.class, request.getBody());
            String projectId = request.getBody().getCloud().getString("projectId");
            String sfsUrl = client.getUrl(projectId,"sfs");
            log.info("----------创建文件共享url----------{}",sfsUrl);
            JSONObject cloud = request.getBody().getCloud();
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数资源空间不能为空!");
            String url =  sfsUrl + "/v2/"+projectId+"/shares";
            JSONObject body = convertCreateFs(cloud);
            client.doPostAction2(url, body);
            return new BaseDataResponse<>("");
        }catch (Exception e){
            log.error("扩容文件存储失败!");
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("扩容文件存储失败!"), e);
        }
    }

    public static JSONObject convertCreateFs(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject server = new JSONObject();
        server.put("flavorRef",cloud.getString("flavorId"));
        server.put("imageRef",cloud.getString("imageId"));
        server.put("name",cloud.getString("name"));
        JSONArray nics = cloud.getJSONArray("nics");
        JSONArray networks = new JSONArray();
        for (int i = 0; i < nics.size(); i++) {
            JSONObject nic = nics.getJSONObject(i);
            JSONObject network = new JSONObject();
            network.put("uuid", nic.getString("subnetId"));
            networks.add(network);
        }
        server.put("networks",networks);

        JSONArray volumes = new JSONArray();
        JSONObject volume = new JSONObject();
        volume.put("uuid", cloud.getString("volumeId"));
        volume.put("source_type", "volume");
        volume.put("destination_type", "volume");
        volume.put("delete_on_termination", "False");
        volume.put("boot_index",0);
        volumes.add(volume);

        server.put("block_device_mapping_v2",volumes);
        server.put("availability_zone",cloud.getString("availabilityZone"));
        body.put("server",server);
        log.info("请求body={}",body);
        return body;
    }

}
