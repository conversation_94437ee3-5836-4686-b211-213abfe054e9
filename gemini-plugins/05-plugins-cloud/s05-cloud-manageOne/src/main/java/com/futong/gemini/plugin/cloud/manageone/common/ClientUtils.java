package com.futong.gemini.plugin.cloud.manageone.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.AuthConfig;
import com.futong.gemini.plugin.cloud.manageone.client.ConnectionConfig;
import com.futong.gemini.plugin.cloud.manageone.client.OBSClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequestBody;
import com.obs.services.ObsClient;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClientUtils {

    //获取Client对象
    public static <C> C client(Class<C> clazz, BaseCloudRequestBody body) {
        //加载认证对象信息
        body.loadAccess();
        try {
            //请求Client对象配置信息
            ConnectionConfig config = new ConnectionConfig.Builder()
                    .protocol(body.getAccess().getProtocol())
                    .host(body.getAccess().getServerIp())
                    .port(body.getAccess().getServerPort())
                    .authConfig(new AuthConfig.Builder()
                            .domain(body.getAccess().getDomain())
                            .username(body.getAccess().getUsername())
                            .password(body.getAccess().getPassword())
                            .projectId(body.getAccess().getProjectId())
                            .build()
                    )
                    .build();
            return clazz.getConstructor(ConnectionConfig.class).newInstance(config);
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }

    public static ObsClient obsClient(BaseCloudRequestBody body) {
        //加载认证对象信息
        try {
            CloudAccessBean access = body.getAccess();
            if (StrUtil.isNotEmpty(access.getJsonStr())) {
                JSONObject accessExt = JSON.parseObject(access.getJsonStr());
                String obsAK = accessExt.getString("AK");
                String obsSK = accessExt.getString("SK");
                String obsEndpoint = accessExt.getString("endpoint");
                ObsClient obsClient = new ObsClient(obsAK, obsSK, obsEndpoint);
                return obsClient;
            }else {
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "对象存储认证失败!");
            }
        } catch (Throwable e) {
            throw new BaseException(BaseResponse.ERROR_BIZ, e);
        }
    }
}

