package com.futong.gemini.plugin.cloud.manageone.client;

// 认证信息封装
public class AuthConfig {
    private final String domain;
    private final String username;
    private final String password;
    private final String projectId;

    private AuthConfig(Builder builder) {
        this.domain = builder.domain;
        this.username = builder.username;
        this.password = builder.password;
        this.projectId = builder.projectId;
    }

    public static class Builder {
        private String domain;
        private String username;
        private String password;
        private String projectId;

        public Builder domain(String domain) {
            this.domain = domain;
            return this;
        }

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }

        public Builder projectId(String projectId) {
            this.projectId = projectId;
            return this;
        }

        public AuthConfig build() {
            validate();
            return new AuthConfig(this);
        }

        private void validate() {
            if (username == null || password == null) {
                throw new IllegalArgumentException("Missing auth parameters");
            }
        }
    }

    // Getters
    public String getDomain() { return domain; }
    public String getUsername() { return username; }
    public String getPassword() { return password; }
    public String getProjectId() { return projectId; }
}