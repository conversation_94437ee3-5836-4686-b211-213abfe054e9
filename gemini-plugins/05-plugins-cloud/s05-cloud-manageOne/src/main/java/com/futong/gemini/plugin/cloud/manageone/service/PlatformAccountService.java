package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.manageone.client.OCClient;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PlatformAccountService {

    public static BaseResponse authAccount(BaseCloudRequest request) {
        try {
            CloudAccessBean access = request.getBody().getAccess();
            if("sub".equals(access.getAccountType())) {
                log.info("----------运营认证----------");
                SCClient client = ClientUtils.client(SCClient.class, request.getBody());
                client.getToken("");
            }else {
                log.info("----------运维认证----------");
                OCClient client = ClientUtils.client(OCClient.class, request.getBody());
                client.getToken("");
            }

            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            log.error("云账号信息认证失败", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD_ACCOUNT_AUTH, e);
        }
    }

}
