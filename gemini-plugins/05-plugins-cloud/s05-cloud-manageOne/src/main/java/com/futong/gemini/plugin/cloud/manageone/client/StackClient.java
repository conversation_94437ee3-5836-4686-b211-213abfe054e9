package com.futong.gemini.plugin.cloud.manageone.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;

/**
 * 基于stack调用
 * 此资源创建不会经过审批
 */
public class StackClient extends ManageOneClient {
    public StackClient(ConnectionConfig config) {
        super(config);
    }

    public JSONObject doServerAction(String tenantId, String serverId, JSONObject body) throws Exception {
        String apiPath = StrUtil.format("/v2/{tenant_id}/servers/{server_id}/action", tenantId, serverId);
        String result = doPostJson(apiPath, body);
        return JSONObject.parseObject(result);
    }
}
