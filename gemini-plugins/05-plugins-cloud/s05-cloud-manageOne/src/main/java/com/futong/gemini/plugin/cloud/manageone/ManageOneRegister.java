package com.futong.gemini.plugin.cloud.manageone;

import com.futong.gemini.plugin.cloud.manageone.common.DefaultUtils;
import com.futong.gemini.plugin.cloud.manageone.sampler.FetchService;
import com.futong.gemini.plugin.cloud.manageone.sampler.RefreshService;
import com.futong.gemini.plugin.cloud.manageone.service.*;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.constant.enums.ActionType;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.gemini.plugin.cloud.sdk.template.BaseCloudRegister;

public class ManageOneRegister extends BaseCloudRegister {
    @Override
    public void load() {
        //资源操作加载
        onAfterLoadPlatform();//加载云平台操作
        onAfterLoadFetch();//加载同步调度信息
        onAfterLoadFetchNetwork();//加载同步网络信息
        onAfterLoadFetchStorage();//加载同步存储信息
        onAfterLoadCompute();//加载云主机操作
        onAfterLoadComputeImage();//加载镜像操作
        onAfterLoadComputeSecurityGroup();//加载云主机安全组操作
        onAfterLoadStorageDisk();//加载存储云硬盘操作
        onAfterLoadStorageSnapshot();//加载存储云硬盘快照操作
        onAfterLoadNeutronVpc();//加载网络VPC操作
        onAfterLoadNeutronSubnet();//加载网络子网操作
        onAfterLoadNeutronEip();//加载网络弹性IP操作
        onAfterLoadNeutronVfw();//加载网络vfw网关操作
        onAfterLoadBucket();//加载对象存储操作
    }

    public void onAfterLoadPlatform() {
        register(ActionType.API_TEST, PlatformService::apiTest);//获取云账号表单信息
        //基础操作
        register(ActionType.GET_PLATFORM_ACCOUNT_ADD_FORM, BaseService::getAccountAddForm);//获取云账号表单信息
        register(ActionType.CREATE_PLATFORM_FETCH_DISPATCH, BaseService::createFetchDispatch);//添加默认调度任务
        //平台账号
        register(ActionType.AUTH_PLATFORM_ACCOUNT, PlatformAccountService::authAccount);//认证云账号
    }

    //同步调度
    public void onAfterLoadFetch() {
        register(ActionType.FETCH_PLATFORM_REGION, FetchService::fetchRegion)//同步地域信息
                .addBefore(DefaultUtils::defaultPage100);//默认分页参数
        register(ActionType.FETCH_PLATFORM_AZONE, FetchService::fetchZone)//同步可用区信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_PLATFORM_TENANT, FetchService::fetchTenant)//同步租户信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
//        register(ActionType.FETCH_PLATFORM_VDC, FetchService::fetchVDC)//同步vdc信息
//                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_PLATFORM_VDC_PROJECT, FetchService::fetchVDCProject)//同步vdc下资源空间信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_INSTANCE, FetchService::fetchVm)//同步云主机信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_INSTANCE_RELATION, FetchService::fetchVmRelation)//同步云主机云盘关联关系
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_INSTANCE_PERF, FetchService::fetchVmPerf)//同步云主机性能信息
                .addBefore(DefaultUtils::defaultPage10);//默认分页参数
        register(ActionType.FETCH_STORAGE_IMAGE, FetchService::fetchImage)//同步镜像信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_FLAVOR, FetchService::fetchFlavor)//同步模版信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_HOST, FetchService::fetchHost)//同步主机信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_HOST_PERF, FetchService::fetchHostPerf)//同步云主机性能信息
                .addBefore(DefaultUtils::defaultPage10);//默认分页参数
        register(ActionType.FETCH_PLATFORM_KEYPAIR, FetchService::fetchKeypair)//同步密钥对
                .addBefore(DefaultUtils::defaultPage10);//默认分页参数
        register(ActionType.FETCH_PLATFORM_ALARM, FetchService::fetchAlarm);//同步告警信息

        //获取对象存储obsv3.ca-global-1.camcloud.changan.comdddddd
        register(ActionType.FETCH_STORAGE_BUCKET, FetchService::fetchBucket);
        //获取文件存储
        register(ActionType.FETCH_STORAGE_BUCKET, FetchService::fetchSFS);
    }

    public void onAfterLoadFetchNetwork() {
        register(ActionType.FETCH_NEUTRON_SWITCH, FetchService::fetchNetwork)//同步网络信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_SUBNET, FetchService::fetchSubnet)//同步子网信息
                .addBefore(DefaultUtils::defaultPage100);//默认分页参数
        register(ActionType.FETCH_NEUTRON_PORT, FetchService::fetchPort)//同步端口信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_ROUTE, FetchService::fetchRoute)//同步路由信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_EIP, FetchService::fetchEip)//同步eip
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_ACL, FetchService::fetchAcl)//同步acl(虚拟防火墙)
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_ACL_POLICY, FetchService::fetchAclPolicy)//同步acl策略
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_ACL_RULE, FetchService::fetchAclRule)//同步acl规则
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_ACL_RELATION, FetchService::fetchAclRelation)//同步acl和acl策略关系
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_NEUTRON_ACL_POLICY_RELATION, FetchService::fetchAclPolicyRelation)//同步acl策略和acl规则关系
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP, FetchService::fetchSecurityGroup)//同步安全组信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_COMPUTE_SECURITYGROUP_RULE, FetchService::fetchSecurityGroupRule)//同步安全组规则信息
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数


    }

    public void onAfterLoadFetchStorage() {
//        register(ActionType.FETCH_NEUTRON_SWITCH, FetchService::fetchVolumeType)//同步卷类型
//                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_STORAGE_POOL, FetchService::fetchStoragePool)//同步存储池
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_STORAGE_SNAPSHOT, FetchService::fetchVolumeSnapshot)//同步卷快照
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
        register(ActionType.FETCH_STORAGE_DISK, FetchService::fetchVmDisk)//同步云盘
                .addBefore(DefaultUtils::defaultPage50);//默认分页参数
    }

    public void onAfterLoadCompute() {
        register(ActionType.QUERY_PLATFORM_REGION, ComputeInstanceService::queryRegion);//查询区域列表
        register(ActionType.QUERY_PLATFORM_AZONE, ComputeInstanceService::queryAzone);//查询可用区列表
        register(ActionType.QUERY_PLATFORM_PROJECT, ComputeInstanceService::queryProject);//查询企业项目列表
        register(ActionType.QUERY_PLATFORM_PRODUCT, ComputeInstanceService::queryProduct);//查询弹性云服务器产品列表
        register(ActionType.QUERY_PLATFORM_TAG, ComputeInstanceService::queryTag);//查询标签列表
        register(ActionType.QUERY_PLATFORM_KEYPAIR, ComputeInstanceService::queryKeypair);//查询密钥对列表
        register(ActionType.QUERY_PLATFORM_IMAGE, ComputeInstanceService::queryImage);//查询镜像列表
        register(ActionType.QUERY_PLATFORM_FLAVOR, ComputeInstanceService::queryFlavor);//查询规格列表
        register(ActionType.QUERY_PLATFORM_DISKTYPE, ComputeInstanceService::queryDiskType);//查询磁盘类型列表
        register(ActionType.QUERY_PLATFORM_VPC, ComputeInstanceService::queryVpc);//查询vpc列表
        register(ActionType.QUERY_PLATFORM_SECURITYGROUP, ComputeInstanceService::querySecurityGroup);//查询安全组列表
        register(ActionType.QUERY_PLATFORM_SUBNET, ComputeInstanceService::querySubnet);//查询子网列表

        register(ActionType.REFRESH_COMPUTE_INSTANCE, RefreshService::refreshEcs)
                .addTransferCloud("$.refreshConfig.data", "$.VMIDs", BaseUtils::formatSingle);


        register(ActionType.CREATE_COMPUTE_INSTANCE, ComputeInstanceService::createVm);//创建云主机
        register(ActionType.UPDATE_COMPUTE_INSTANCE, ComputeInstanceService::updateVm);//修改云主机

        //启动云主机
        register(ActionType.START_COMPUTE_INSTANCE, ComputeInstanceService::startInstance)
                .addTransferCloud("$.cis.openId", "$.instanceIds")
                .addTransferCloud("$.cis.sourceJson.devopsLevel02Code", "$.projectIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //关闭云主机
        register(ActionType.STOP_COMPUTE_INSTANCE, ComputeInstanceService::stopInstance)
                .addTransferCloud("$.cis.openId", "$.instanceIds")
                .addTransferCloud("$.cis.sourceJson.devopsLevel02Code", "$.projectIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //重启云主机
        register(ActionType.REBOOT_COMPUTE_INSTANCE, ComputeInstanceService::rebootInstance)
                .addTransferCloud("$.cis.openId", "$.instanceIds")
                .addTransferCloud("$.cis.sourceJson.devopsLevel02Code", "$.projectIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //休眠云主机
        register(ActionType.HIBERNATE_COMPUTE_INSTANCE, ComputeInstanceService::operationInstance)
                .addTransferCloud("$.cis.openId", "$.instanceIds")
                .addTransferCloud("$.cis.sourceJson.devopsLevel02Code", "$.projectIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addAfter(BaseCloudService::addRefreshGourdJob);
        //控制台
        register(ActionType.CONSOLE_COMPUTE_INSTANCE, ComputeInstanceService::consoleInstance)
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId");

        //重置密码
        register(ActionType.UPDATE_COMPUTE_INSTANCE_VNC, ComputeInstanceService::resetEcsPassword)
                .addTransferCloud("$.ci.openId", "$.instanceId")
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.model.password", "$.password");

        //删除云主机
        register(ActionType.DELETE_COMPUTE_INSTANCE, ComputeInstanceService::deleteInstance)
                .addTransferCloud("$.cis.openId", "$.instanceIds")
                .addTransferCloud("$.cis.sourceJson.devopsLevel02Code", "$.projectIds")
                .addSetRefresh(ActionType.REFRESH_COMPUTE_INSTANCE)
                .addSetRefreshConfig(10, 2000, 1000)
                .addSetRefreshSplitData("request","$.body.cloud.instanceIds")
                .addAfter(BaseCloudService::addRefreshGourdJob);
    }

    public void onAfterLoadComputeImage() {
        register(ActionType.CREATE_COMPUTE_IMAGE, ImageService::createImage);//创建镜像
        register(ActionType.UPDATE_COMPUTE_IMAGE, ImageService::updateImage)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.imageId");//修改镜像
        register(ActionType.DELETE_COMPUTE_IMAGE, ImageService::deleteImage)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.imageId");//删除镜像
    }

    public void onAfterLoadNeutronVpc() {
        //创建vpc
        register(ActionType.CREATE_NEUTRON_VPC, VpcService::createVpc);
        //修改vpc
        register(ActionType.UPDATE_NEUTRON_VPC, VpcService::updateVpc)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.vpcId");
        //删除vpc
        register(ActionType.DELETE_NEUTRON_VPC, VpcService::deleteVpc)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.vpcId");
    }

    public void onAfterLoadNeutronSubnet() {
        //创建子网
        register(ActionType.CREATE_NEUTRON_SUBNET, VpcService::createSubnet);
        //修改子网
        register(ActionType.UPDATE_NEUTRON_SUBNET, VpcService::updateSubnet)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.subnetId");
        //删除子网
        register(ActionType.DELETE_NEUTRON_SUBNET, VpcService::deleteSubnet)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.subnetId");
    }
    public void onAfterLoadNeutronEip() {
        //申请eip
        register(ActionType.CREATE_NEUTRON_EIP, EipService::createEip);
        //删除eip
        register(ActionType.DELETE_NEUTRON_EIP, EipService::deleteEip)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.eipId");
        //绑定eip
        register(ActionType.BIND_NEUTRON_EIP, EipService::bindEip)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.eipId");
        //解绑eip
        register(ActionType.UNBIND_NEUTRON_EIP, EipService::unbindEip)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.eipId");
    }


    public void onAfterLoadNeutronVfw() {
        //创建vfw
        register(ActionType.CREATE_NEUTRON_ACL, AclService::createAcl);
        //修改vfw
        register(ActionType.UPDATE_NEUTRON_ACL, AclService::updateAcl)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.vfwId");
        //删除vfw
        register(ActionType.DELETE_NEUTRON_ACL, AclService::deleteAcl)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.vfwId");

        //创建vfw策略
        register(ActionType.CREATE_NEUTRON_ACL_POLICY, AclService::createAclPolicy);
        //修改vfw策略
        register(ActionType.UPDATE_NEUTRON_ACL_POLICY, AclService::updateAclPolicy)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.policyId");
        //删除vfw策略
        register(ActionType.DELETE_NEUTRON_ACL_POLICY, AclService::deleteAclPolicy)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.policyId");
        //子网绑定acl
        register(ActionType.BIND_NEUTRON_ACL, AclService::bindAcl)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.vfwId");
    }

    public void onAfterLoadStorageDisk() {
        //申请云硬盘
        register(ActionType.CREATE_STORAGE_DISK, DiskService::createDisk);
        //删除云硬盘
        register(ActionType.DELETE_STORAGE_DISK, DiskService::deleteDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.volumeId");
        //扩容云硬盘
        register(ActionType.RESIZE_STORAGE_DISK, DiskService::modifyDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.diskId");
        //挂载云硬盘
        register(ActionType.ATTACH_STORAGE_DISK, DiskService::attachDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.serverId");
        //卸载云硬盘
        register(ActionType.DETACH_STORAGE_DISK, DiskService::detachDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.serverId");
    }

    public void onAfterLoadStorageSnapshot() {
        //创建云硬盘快照
        register(ActionType.CREATE_STORAGE_SNAPSHOT, DiskService::createSnapshotDisk);
        //删除云硬盘快照
        register(ActionType.DELETE_STORAGE_SNAPSHOT, DiskService::deleteSnapshotDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.snapshotId");
        //修改云硬盘快照
        register(ActionType.UPDATE_STORAGE_SNAPSHOT, DiskService::updateSnapshotDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.snapshotId");
        //回滚云硬盘快照
        register(ActionType.RESET_STORAGE_SNAPSHOT, DiskService::resetSnapshotDisk)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.snapshotId");
    }

    public void onAfterLoadComputeSecurityGroup() {
        //创建安全组
        register(ActionType.CREATE_COMPUTE_SECURITYGROUP, SecurityGroupService::createSecurityGroup);
        //删除安全组
        register(ActionType.DELETE_COMPUTE_SECURITYGROUP, SecurityGroupService::deleteSecurityGroup)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.securityGroupId");
        //修改安全组
        register(ActionType.UPDATE_COMPUTE_SECURITYGROUP, SecurityGroupService::modifySecurityGroup)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.securityGroupId")
                .addTransferCloud("$.model.resourceName", "$.name", false)
                .addTransferCloud("$.model.description", "$.description", false);
        //绑定安全组
        register(ActionType.BIND_COMPUTE_SECURITYGROUP, SecurityGroupService::joinSecurityGroup)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.portId")
                .addTransferCloud("$.model.resourceId", "$.securityGroupId");
        //解绑安全组
        register(ActionType.UNBIND_COMPUTE_SECURITYGROUP, SecurityGroupService::leaveSecurityGroup)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.securityGroupId")
                .addTransferCloud("$.model.portId", "$.portId");

        //创建安全组规则
        register(ActionType.CREATE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::createSecurityGroupRule);
        //删除安全组规则
        register(ActionType.DELETE_COMPUTE_SECURITYGROUP_RULE, SecurityGroupService::deleteSecurityGroupRule)
                .addTransferCloud("$.ci.sourceJson.devopsLevel02Code", "$.projectId")
                .addTransferCloud("$.ci.openId", "$.securityGroupRuleId");
    }

    public void onAfterLoadBucket() {
        //创建桶
        register(ActionType.CREATE_OSS_STORAGE_BUCKET, OssStorageService::createOssBucket);
        //删除桶
        register(ActionType.DELETE_OSS_STORAGE_BUCKET, OssStorageService::deleteOssBucket);
        //上传文件
        register(ActionType.CREATE_OSS_STORAGE_BUCKET, OssStorageService::uploadFile);
        //列举对象
        register(ActionType.CREATE_OSS_STORAGE_BUCKET, OssStorageService::listObject);
        //下载文件
        register(ActionType.CREATE_OSS_STORAGE_BUCKET, OssStorageService::downloadFile);
        //删除文件
        register(ActionType.CREATE_OSS_STORAGE_BUCKET, OssStorageService::deleteFile);
        //创建目录
        register(ActionType.CREATE_OSS_STORAGE_BUCKET, OssStorageService::createFolder);

    }

}
