package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class VpcService {
    public static BaseResponse createVpc(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------创建vpcurl----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/router";
            JSONObject body = doCreateVpc(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建vpc成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建vpc失败!"), e);
        }
    }

    public static BaseResponse updateVpc(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String scUrl = client.getUrl(projectId,"sc");
            log.info("----------修改vpcurl----------{}",scUrl);
            String url =  scUrl + "/v2.0/routers/"+cloud.getString("vpcId");
            JSONObject body = doUpdateVpc(cloud);
            client.doPutJson(url,body);
            return BaseResponse.SUCCESS.of("修改vpc成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改vpc失败!"), e);
        }
    }

    public static BaseResponse deleteVpc(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            JSONArray vpcs = cloud.getJSONArray("cis");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------删除vpcurl----------{}",vpcUrl);
            for (int i = 0; i < vpcs.size(); i++) {
                String url =  vpcUrl + "/v2.0/routers/"+cloud.getString("vpcId");
                client.doDeleteAction(url,null);
            }
            return BaseResponse.SUCCESS.of("删除vpc成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除vpc失败!"), e);
        }
    }


    /**
     * {
     * "subnet": {
     * "name": "subnet01",
     * "enable_dhcp": true,
     * "network_id": "4f9d8a12-63a6-4c33-b9e6-0d31150c0781",
     * "tenant_id": "05e1fbc9eab048bd8f791deeef654321",
     * "dns_nameservers": [
     * ],
     * "allocation_pools": [
     * {
     * "start": "************",
     * "end": "*************"
     * }
     * ],
     * "host_routes": [],
     * "ip_version": 4,
     * "gateway_ip": "*************",
     * "cidr": "***********/24"
     * }
     * }
     * @param request
     * @return
     */
    public static BaseResponse createSubnet(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------创建子网url----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/subnets";
            JSONObject body = doCreateSubnet(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建子网成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建子网失败!"), e);
        }
    }

    public static BaseResponse updateSubnet(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------修改子网url----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/subnets/"+cloud.getString("subnetId");
            JSONObject body = doUpdateSubnet(cloud);
            client.doPutJson(url,body);
            return BaseResponse.SUCCESS.of("修改子网成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改子网失败!"), e);
        }
    }

    public static BaseResponse deleteSubnet(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------删除子网url----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/subnets/"+cloud.getString("subnetId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("删除子网成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除子网失败!"), e);
        }
    }

    /**
     * {
     * "router": {
     * "name": "router01",
     * "admin_state_up": true,
     * "external_gateway_info": {
     * "network_id": "00ed6888-6aee-40c1-90b9-6d1dbcd24d5a"
     * }
     * }
     * }
     * @param cloud
     * @return
     * @throws Exception
     */
    public static JSONObject doCreateVpc(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject router = new JSONObject();
        router.put("name",cloud.getString("name"));
        router.put("admin_state_up",true);
        JSONObject network = new JSONObject();
        network.put("network_id",cloud.getString("networkId"));
        router.put("external_gateway_info",network);
        body.put("router",router);
        log.info("请求body={}",body);
        return body;
    }

    public static JSONObject doCreateSubnet(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject subnet = new JSONObject();
        subnet.put("name",cloud.getString("name"));
        subnet.put("enable_dhcp",true);
        subnet.put("network_id",cloud.getString("network_id"));
        subnet.put("tenant_id",cloud.getString("projectId"));
        subnet.put("ip_version",cloud.getString("ip_version"));
        subnet.put("gateway_ip",cloud.getString("gateway_ip"));
        subnet.put("cidr",cloud.getString("cidr"));
        JSONArray pool = new JSONArray();
        subnet.put("allocation_pools",pool);
        body.put("subnet",subnet);
        log.info("请求body={}",body);
        return body;
    }

    /**
     * {
     * "subnet": {
     * "name": "test_subnet001",
     * "dns_nameservers": [
     * "***********",
     * "************"
     * ],
     * "enable_dhcp": true
     * }
     * }
     * @param cloud
     * @return
     * @throws Exception
     */
    public static JSONObject doUpdateSubnet(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject subnet = new JSONObject();
        subnet.put("name",cloud.getString("name"));
        subnet.put("enable_dhcp",true);
        JSONArray pool = new JSONArray();
        subnet.put("dns_nameservers",pool);
        body.put("subnet",subnet);
        log.info("请求body={}",body);
        return body;
    }

    public static JSONObject doUpdateVpc(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject router = new JSONObject();
        router.put("name",cloud.getString("name"));
        body.put("router",router);
        log.info("请求body={}",body);
        return body;
    }

}
