package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.manageone.client.ManageOneClient;
import com.futong.gemini.plugin.cloud.manageone.client.OCClient;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.sun.media.jfxmedia.logging.Logger;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class PlatformService {
    public static BaseResponse apiTest(BaseCloudRequest request) {
        String clientType = request.getBody().getString("clientType");
        ManageOneClient client;
        if (StrUtil.isEmpty(clientType) || "oc".equals(clientType)) {
            client = ClientUtils.client(OCClient.class, request.getBody());
        } else {
            client = ClientUtils.client(SCClient.class, request.getBody());
        }
        try {
            String apiPath = request.getBody().getString("apiPath");
            JSONObject cloud = request.getBody().getCloud();
            Map<String, String> query;
            try {
                query = FTHttpUtils.query(cloud);
            } catch (Exception e) {
                throw new BaseException(BaseResponse.FAIL_PARAM, e);
            }
            String protocol = request.getBody().getString("protocol");
            if(protocol!=null&&"post".equals(protocol)){
                String result = client.doPostJson(apiPath, cloud);
                return BaseResponse.SUCCESS.of(result);
            }else {
                String result = client.doGet(apiPath, query);
                return BaseResponse.SUCCESS.of(result);
            }

        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e);
        }
    }
}
