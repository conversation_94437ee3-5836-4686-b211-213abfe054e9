package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SecurityGroupService {
    public static BaseResponse createSecurityGroup(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------创建安全组url----------{}",vpcUrl);
            if(StrUtil.isEmpty(cloud.getString("name")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数name不能为空!");
            String url =  vpcUrl + "/v2.0/security-groups";
            JSONObject body = convertSecurityGroup(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建安全组成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组失败!"), e);
        }
    }

    public static BaseResponse createSecurityGroupRule(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------创建安全组规则url----------{}",vpcUrl);
            if(StrUtil.isEmpty(cloud.getString("name")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数name不能为空!");
            String url =  vpcUrl + "/v2.0/security-group-rules";
            JSONObject body = convertSecurityGroupRule(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建安全组规则成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组规则失败!"), e);
        }
    }


    public static BaseResponse deleteSecurityGroupRule(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------删除安全组规则url----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/security-group-rules/"+cloud.getString("securityGroupRuleId");
            JSONObject body = convertSecurityGroupRule(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("删除安全组规则成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除安全组规则失败!"), e);
        }
    }



    public static BaseResponse deleteSecurityGroup(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------删除安全组url----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/security-groups"+cloud.getString("securityGroupId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("删除安全组成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除安全组失败!"), e);
        }
    }

    public static BaseResponse modifySecurityGroup(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------创建安全组url----------{}",vpcUrl);
            if(StrUtil.isEmpty(cloud.getString("name")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数name不能为空!");
            String url =  vpcUrl + "/v2.0/security-groups/"+cloud.getString("securityGroupId");
            JSONObject body = new JSONObject();
            body.put("security_group",cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建安全组成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建安全组失败!"), e);
        }
    }

    /**
     * {
     * "port": {
     * "security_groups": ["44abceac-15f1-48a3-b3b8-723c161260af","05e1fbc9eab048bd8f791deeef654321"]
     * }
     * }
     * @param request
     * @return
     */
    public static BaseResponse joinSecurityGroup(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------绑定安全组url----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/ports/"+cloud.getString("portId");
            JSONObject body = new JSONObject();
            body.put("port",cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("绑定安全组成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定安全组失败!"), e);
        }
    }

    public static BaseResponse leaveSecurityGroup(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            log.info("----------解绑安全组url----------{}",vpcUrl);
            String url =  vpcUrl + "/v2.0/ports/"+cloud.getString("portId");
            JSONObject body = new JSONObject();
            body.put("port",cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("解绑安全组成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑安全组失败!"), e);
        }
    }

    public static JSONObject convertSecurityGroup(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject securityGroup = new JSONObject();
        securityGroup.put("name",cloud.getString("name"));
        body.put("security_group",securityGroup);
        log.info("请求body={}",body);
        return body;
    }

    /**
     * @param cloud
     * @return
     */
    public static JSONObject convertSecurityGroupRule(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject securityGroupRule = new JSONObject();
        securityGroupRule.put("security_group_id",cloud.getString("security_group_id"));
        securityGroupRule.put("direction",cloud.getString("direction"));
        securityGroupRule.put("protocol",cloud.getString("protocol"));
        securityGroupRule.put("remote_ip_prefix",cloud.getString("remote_ip_prefix"));
        body.put("security_group_rule",securityGroupRule);
        log.info("请求body={}",body);
        return body;
    }

    public static String convertDeleteDisk(JSONObject cloud) {
        String body ="{\"subscriptions\":[\"service_type\":\"{service_type}\",\"region_id\":\"{region_id}\",\"project_id\":\"{project_id}\",\"tenancy\":\"0\",\"time_zone\":\"Asia/Shanghai\",\"comments\":\"\",\"operate_type\":\"{operate_type}\",\"params\":\"{params}\"}]}";
        String params = "{\\\"action\\\":\\\"delete_volume\\\",\\\"ids\\\":[{volumes}],\\\"region_id\\\":\\\"dg-tky-1\\\",\\\"display\\\":{\\\"zh_CN\\\":[{\\\"label\\\":\\\"区域\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"东莞\\\"},{\\\"label\\\":\\\"可用分区\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"华为\\\"},{\\\"label\\\":\\\"磁盘类型\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"SATA\\\"},{\\\"label\\\":\\\"容量\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":30},{\\\"label\\\":\\\"数据来源\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"无\\\"},{\\\"label\\\":\\\"共享方式\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"否\\\"}],\\\"en_US\\\":[{\\\"label\\\":\\\"Region\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"东莞\\\"},{\\\"label\\\":\\\"AZ\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"华为\\\"},{\\\"label\\\":\\\"Disk Type\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"SATA\\\"},{\\\"label\\\":\\\"Capacity\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":30},{\\\"label\\\":\\\"Data Source\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"None\\\"},{\\\"label\\\":\\\"Disk Sharing\\\",\\\"type\\\":\\\"string\\\",\\\"value\\\":\\\"No\\\"}]}}";

        params = params.replace("{region}", cloud.getString("region"));
        params = params.replace("{name}", cloud.getString("name"));
        params = params.replace("{size}", cloud.getString("size"));
        params = params.replace("{availabilityZone}", cloud.getString("availabilityZone"));

        JSONArray disks = cloud.getJSONArray("cis");
        String volumes = "";
        for (int i = 0; i < disks.size(); i++) {
            JSONObject disk = disks.getJSONObject(i);
            if("".equals(volumes)){
                volumes = "{\\\"id\\\": \\\""+disk.getString("openId")+"\\\",\\\"service_type\\\": \\\"evs\\\",\\\"name\\\": \\\""+disk.getString("openName")+"\\\",\\\"display\\\": {}}}";
            }else{
                volumes = volumes +",{\\\"id\\\": \\\""+disk.getString("openId")+"\\\",\\\"service_type\\\": \\\"evs\\\",\\\"name\\\": \\\""+disk.getString("openName")+"\\\",\\\"display\\\": {}}}";
            }
        }
        params = params.replace("{volumes}",  volumes);

        body = body.replace("{service_type}", cloud.getString("serviceType")==null?"evs":cloud.getString("serviceType"));
        body = body.replace("{region_id}", cloud.getString("regionId"));
        body = body.replace("{project_id}", cloud.getString("projectId"));
        body = body.replace("{operate_type}", cloud.getString("operateType")==null?"delete":cloud.getString("operateType"));

        body = body.replace("{params}", params);
        log.info("请求body={}",body);
        return body;
    }


    public static String convertModifyDisk(JSONObject cloud) {
        String body ="{\"subscriptions\":[\"service_type\":\"{service_type}\",\"region_id\":\"{region_id}\",\"project_id\":\"{project_id}\",\"tenancy\":\"0\",\"time_zone\":\"Asia/Shanghai\",\"comments\":\"\",\"operate_type\":\"{operate_type}\",\"params\":\"{params}\"}]}";
        String params = "{\\\"action\\\":\\\"extend\\\",\\\"ids\\\":[{\\\"id\\\":\\\"{diskId}\\\",\\\"name\\\":\\\"{diskName}\\\",\\\"new_size\\\":{diskSize},\\\"service_type\\\":\\\"evs\\\"}],\\\"region_id\\\":\\\"{region}\\\",\\\"oldDisplay\\\":{}";

        params = params.replace("{region}", cloud.getString("region"));
        params = params.replace("{diskName}", cloud.getString("diskName"));
        params = params.replace("{diskSize}", cloud.getString("diskSize"));
        params = params.replace("{diskId}", cloud.getString("diskId"));

        body = body.replace("{service_type}", cloud.getString("serviceType")==null?"evs":cloud.getString("serviceType"));
        body = body.replace("{region_id}", cloud.getString("regionId"));
        body = body.replace("{project_id}", cloud.getString("projectId"));
        body = body.replace("{operate_type}", cloud.getString("operateType")==null?"modify":cloud.getString("operateType"));

        body = body.replace("{params}", params);
        log.info("请求body={}",body);
        return body;
    }



}
