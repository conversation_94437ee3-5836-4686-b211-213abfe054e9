package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ImageService {
    /**
     *
     * @param request
     * @return
     */
    public static BaseResponse createImage(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            String imsUrl = client.getUrl(projectId,"ims");
            log.info("----------创建镜像url----------{}",imsUrl);
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            if(StrUtil.isEmpty(cloud.getString("availabilityZone")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数azId不能为空!");
            String url =  imsUrl + "/v2/images";
            JSONObject body = convertCreateImage(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建镜像成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建镜像失败!"), e);
        }
    }


    public static BaseResponse updateImage(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String imsUrl = client.getUrl(projectId,"ims");
            log.info("----------修改镜像url----------{}",imsUrl);
            String url =  imsUrl + "/v2/cloudimages/"+cloud.getString("imageId");
            JSONObject body = convertUpdateImage(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("修改镜像成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改镜像失败!"), e);
        }
    }

    public static BaseResponse deleteImage(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String imsUrl = client.getUrl(projectId,"ims");
            log.info("----------删除镜像url----------{}",imsUrl);
            String url =  imsUrl + "/v2/images/"+cloud.getString("imageId");
            JSONObject body = convertCreateImage(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("删除镜像成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除镜像失败!"), e);
        }
    }

    /**
     * {
     *      * "__os_version": "Ubuntu 14.04 server 64bit",
     *      * "container_format": "bare",
     *      * "disk_format": "vhd",
     *      * "id": "4ca46bf1-5c61-48ff-b4f3-0ad4e5e3ba86",
     *      * "min_disk": 1,
     *      * 镜像服务(IMS)
     *      * API 参考 4 IMS 场景对接指导
     *      * 文档版本 01 (2023-09-22) 版权所有 © 华为云计算技术有限公司 18
     *      * "min_ram": 1024,
     *      * "name": "imageName",
     *      * "tags": [
     *      * "test",
     *      * "image"
     *      * ],
     *      * "visibility": "private",
     *      * "protected": false
     *      * }
     * @param cloud
     * @return
     * @throws Exception
     */
    public static JSONObject convertCreateImage(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        body.put("__os_version","");
        body.put("container_format","");
        body.put("disk_format","");
        body.put("id","");
        body.put("min_disk","");
        body.put("min_ram","");
        body.put("name",cloud.getString("name"));
        body.put("visibility","private");
        body.put("protected",false);
        log.info("请求body={}",body);
        return body;
    }

    public static JSONObject convertUpdateImage(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject bodyparam = new JSONObject();
        bodyparam.put("op","replace");
        bodyparam.put("path","name");
        bodyparam.put("value","ims_test");
        body.put("bodyparam",bodyparam);
        log.info("请求body={}",body);
        return body;
    }

}
