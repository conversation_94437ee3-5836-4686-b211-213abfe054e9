package com.futong.gemini.plugin.cloud.manageone.common;

import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;

public class DefaultUtils {
    public static boolean defaultPage50(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageNo")) {
            request.getBody().getCloud().put("pageNo", 1);//默认1
        }
        if (!request.getBody().getCloud().containsKey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 50);//默认100
        }
        return true;
    }

    public static boolean defaultPage10(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageNo")) {
            request.getBody().getCloud().put("pageNo", 1);//默认1
        }
        if (!request.getBody().getCloud().contains<PERSON>ey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 5);//默认100
        }
        return true;
    }

    public static boolean defaultPage100(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageNo")) {
            request.getBody().getCloud().put("pageNo", 1);//默认1
        }
        if (!request.getBody().getCloud().containsKey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 100);//默认100
        }
        return true;
    }

    public static boolean defaultLimit100(BaseCloudRequest request) {
        Integer pageNo = request.getBody().getCloud().getInteger("pageNo");
        Integer pageSize = request.getBody().getCloud().getInteger("pageSize");
        if (pageSize == null) {
            pageSize = 100;
            request.getBody().getCloud().put("limit", pageSize);//默认100
        }
        if (pageNo != null) {
            request.getBody().getCloud().put("offset", pageNo * pageSize);//默认1
        }
        return true;
    }

    public static boolean defaultSize10(BaseCloudRequest request) {
        if (!request.getBody().getCloud().containsKey("pageNo")) {
            request.getBody().getCloud().put("pageNo", 1);//默认1
        }
        if (!request.getBody().getCloud().containsKey("pageSize")) {
            request.getBody().getCloud().put("pageSize", 50);//默认100
        }
        return true;
    }
}
