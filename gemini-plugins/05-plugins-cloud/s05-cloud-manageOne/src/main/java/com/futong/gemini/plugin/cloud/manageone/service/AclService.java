package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AclService {
    public static BaseResponse createAcl(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_groups";
            JSONObject body = convertCreateAcl(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建acl成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建acl失败!"), e);
        }
    }

    public static BaseResponse updateAcl(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_groups/"+cloud.getString("vfwId");
            JSONObject body = convertUpdateAcl(cloud);
            client.doPutJson(url,body);
            return BaseResponse.SUCCESS.of("修改acl成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改acl失败!"), e);
        }
    }

    public static BaseResponse deleteAcl(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_groups/"+cloud.getString("vfwId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("删除acl成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除acl失败!"), e);
        }
    }


    /**
     * {
     * 	"firewall_group": {
     * 		"ingress_firewall_policy_id": "5df92ad5-446a-400a-b2b2-8ceed5e440f2",
     * 		"ports": [],
     * 		"name": "vfw-group001"
     * 	    }
     * }
     * @param cloud
     * @return
     */
    public static JSONObject convertCreateAcl(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("name",cloud.getString("name"));
        floatingip.put("ports",new JSONArray());
        floatingip.put("ingress_firewall_policy_id",cloud.getString("ingress_firewall_policy_id"));
        body.put("firewall_group",floatingip);
        log.info("请求body={}",body);
        return body;
    }

    public static JSONObject convertUpdateAcl(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("egress_firewall_policy_id",new JSONArray());
        body.put("firewall_group",floatingip);
        log.info("请求body={}",body);
        return body;
    }


    public static BaseResponse createAclPolicy(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_policies";
            JSONObject body = convertCreateAclPolicy(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建acl策略成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建acl策略失败!"), e);
        }
    }

    public static BaseResponse updateAclPolicy(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_policies/"+cloud.getString("policyId");
            JSONObject body = convertUpdateAclPolicy(cloud);
            client.doPutJson(url,body);
            return BaseResponse.SUCCESS.of("修改acl策略成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改acl策略失败!"), e);
        }
    }

    public static BaseResponse deleteAclPolicy(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_policies/"+cloud.getString("policyId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("删除acl策略成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除acl策略失败!"), e);
        }
    }


    public static BaseResponse bindAcl(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_groups/"+cloud.getString("vfwId");
            JSONObject body = convertBindAcl(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("绑定acl策略成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定acl策略失败!"), e);
        }
    }

    /**
     * {
     * "firewall_policy":
     * {
     * "name": "vfw-policy001",
     * "firewall_rules": []
     * }
     * }
     * @param cloud
     * @return
     */
    public static JSONObject convertCreateAclPolicy(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("name",cloud.getString("name"));
        floatingip.put("firewall_rules",new JSONArray());
        body.put("firewall_policy",floatingip);
        log.info("请求body={}",body);
        return body;
    }

    /**
     * @param cloud
     * @return
     */
    public static JSONObject convertUpdateAclPolicy(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("firewall_rules",new JSONArray());
        body.put("firewall_policy",floatingip);
        log.info("请求body={}",body);
        return body;
    }


    public static BaseResponse createAclRule(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/fwaas/firewall_policies/"+cloud.getString("policyId");;
            JSONObject body = convertCreateAclRule(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建acl成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建acl失败!"), e);
        }
    }

    public static JSONObject convertCreateAclRule(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("name",cloud.getString("name"));
        floatingip.put("firewall_rules",new JSONArray());
        body.put("firewall_policy",floatingip);
        log.info("请求body={}",body);
        return body;
    }


    /**
     * {
     * "firewall_group":
     * {
     * "ports": ["0ff9ce32-9011-4981-b891-38d836726623"]
     * }
     * }
     * @param cloud
     * @return
     */
    public static JSONObject convertBindAcl(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("ports",new JSONArray());
        body.put("firewall_group",floatingip);
        log.info("请求body={}",body);
        return body;
    }

}
