package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.OCClient;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DiskService {
    public static BaseResponse createDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------创建云盘url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/volumes";
            JSONObject body = convertCreateDisk(cloud);
            client.doPostAction2(url, body);
            return BaseResponse.SUCCESS.of("创建云盘成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云盘失败!"), e);
        }
    }

    public static BaseResponse deleteDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------删除硬盘url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/volumes/"+cloud.getString("volumeId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("删除硬盘成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除硬盘失败!"), e);
        }
    }

    public static BaseResponse modifyDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------修改云盘url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/volumes";
            String body = convertModifyDisk(cloud);
            client.doSubscription(url,body);
            return BaseResponse.SUCCESS.of("修改云盘成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改云盘失败!"), e);
        }
    }

    public static BaseResponse attachDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------挂载云盘url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/servers/"+cloud.getString("serverId")+"/os-volume_attachments";
            JSONObject body = convertAttachDisk(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("挂载云盘成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("挂载云盘失败!"), e);
        }
    }

    public static BaseResponse detachDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------卸载云盘url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/servers/"+cloud.getString("serverId")+"/os-volume_attachments/"+cloud.getString("volumeId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("卸载云盘成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("卸载云盘失败!"), e);
        }
    }


    public static BaseResponse createSnapshotDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------创建云盘快照url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/snapshots";
            JSONObject body = convertCreateSnapshotDisk(cloud);
            client.doPostAction2(url, body);
            return BaseResponse.SUCCESS.of("创建云盘快照成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建云盘快照失败!"), e);
        }
    }

    public static BaseResponse deleteSnapshotDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------删除硬盘快照url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/snapshots/"+cloud.getString("snapshotId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("删除硬盘快照成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除硬盘快照失败!"), e);
        }
    }

    public static BaseResponse updateSnapshotDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------修改硬盘快照url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/snapshots/"+cloud.getString("snapshotId");
            JSONObject body = convertUpdateSnapshotDisk(cloud);
            client.doPutJson(url,body);
            return BaseResponse.SUCCESS.of("修改硬盘快照成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改硬盘快照失败!"), e);
        }
    }

    public static BaseResponse resetSnapshotDisk(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------回滚硬盘快照url----------{}",evsUrl);
            String url =  evsUrl + "/"+projectId+"/os-vendor-snapshots/"+cloud.getString("snapshotId")+"/rollback";
            JSONObject body = convertResetSnapshotDisk(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("回滚硬盘快照成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("回滚硬盘快照失败!"), e);
        }
    }

    /**
     * {
     * "volume" : {
     * "name" : "volume02",
     * "availability_zone" : "az1.dc1",
     * "description" : "create for api test",
     * "volume_type" : "SAS",
     * "metadata" : {
     * "volume_owner" : "openapi"
     * },
     * "consistencygroup_id" : null,
     * "source_volid" : null,
     * "snapshot_id" : null,
     * "multiattach" : false,
     * "size" : 20
     * }
     * }
     * @param cloud
     * @return
     */
    public static JSONObject convertCreateDisk(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject volume = new JSONObject();
        volume.put("name",cloud.getString("name"));
        volume.put("availability_zone",cloud.getString("availabilityZone"));
        volume.put("description",cloud.getString("description"));
        volume.put("volume_type",cloud.getString("volumeType"));
        volume.put("multiattach",cloud.getString("multiattach"));
        volume.put("size",cloud.getInteger("size"));
        volume.put("metadata",cloud.getJSONObject("metadata"));
        volume.put("consistencygroup_id",cloud.getString("consistencygroup_id"));
        volume.put("source_volid",cloud.getString("source_volid"));
        volume.put("snapshot_id",cloud.getString("snapshot_id"));
        body.put("volume",volume);
        log.info("请求body={}",body);
        return body;
    }

    /**
     * {
     * "snapshot" : {
     * "name" : "snap-001",
     * "description" : "Daily backup",
     * "volume_id" : "5aa119a8-d25b-45a7-8d1b-88e127885635",
     * "force" : false,
     * "metadata" : {}
     * }
     * }
     * @param cloud
     * @return
     */
    public static JSONObject convertCreateSnapshotDisk(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject snapshot = new JSONObject();
        snapshot.put("name",cloud.getString("name"));
        snapshot.put("description",cloud.getString("description"));
        snapshot.put("volume_id",cloud.getString("volumeId"));
        snapshot.put("force",false);
        snapshot.put("metadata",cloud.getJSONObject("metadata"));
        body.put("snapshot",snapshot);
        log.info("请求body={}",body);
        return body;
    }

    public static JSONObject convertUpdateSnapshotDisk(JSONObject cloud) {
        JSONObject body = new JSONObject();
        body.put("name",cloud.getString("name"));
        body.put("description",cloud.getString("description"));
        log.info("请求body={}",body);
        return body;
    }

    /**
     * {
     *     "rollback" : {
     * "name" : "test-001",
     * "volume_id" : "5aa119a8-d25b-45a7-8d1b-88e127885635"
     * }
     * }
     * @param cloud
     * @return
     */
    public static JSONObject convertResetSnapshotDisk(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject rollback = new JSONObject();
        rollback.put("name",cloud.getString("name"));
        rollback.put("volume_id",cloud.getString("volumeId"));
        body.put("rollback",rollback);
        log.info("请求body={}",body);
        return body;
    }

    public static String convertModifyDisk(JSONObject cloud) {
        String body ="{\"subscriptions\":[\"service_type\":\"{service_type}\",\"region_id\":\"{region_id}\",\"project_id\":\"{project_id}\",\"tenancy\":\"0\",\"time_zone\":\"Asia/Shanghai\",\"comments\":\"\",\"operate_type\":\"{operate_type}\",\"params\":\"{params}\"}]}";
        String params = "{\\\"action\\\":\\\"extend\\\",\\\"ids\\\":[{\\\"id\\\":\\\"{diskId}\\\",\\\"name\\\":\\\"{diskName}\\\",\\\"new_size\\\":{diskSize},\\\"service_type\\\":\\\"evs\\\"}],\\\"region_id\\\":\\\"{region}\\\",\\\"oldDisplay\\\":{}";

        params = params.replace("{region}", cloud.getString("region"));
        params = params.replace("{diskName}", cloud.getString("diskName"));
        params = params.replace("{diskSize}", cloud.getString("diskSize"));
        params = params.replace("{diskId}", cloud.getString("diskId"));

        body = body.replace("{service_type}", cloud.getString("serviceType")==null?"evs":cloud.getString("serviceType"));
        body = body.replace("{region_id}", cloud.getString("region"));
        body = body.replace("{project_id}", cloud.getString("projectId"));
        body = body.replace("{operate_type}", cloud.getString("operateType")==null?"modify":cloud.getString("operateType"));

        body = body.replace("{params}", params);
        log.info("请求body={}",body);
        return body;
    }

    /**
     * {
     * "volumeAttachment" : {
     * "volumeId" : "2a70890e-591b-4cc2-918e-9ac34ebf596e",
     * "device" : "/dev/sdb",
     * }
     * }
     * @param cloud
     * @return
     */
    public static JSONObject convertAttachDisk(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject volume = new JSONObject();
        volume.put("volumeId",cloud.getString("volumeId"));
        volume.put("device",cloud.getString("device"));
        body.put("volumeAttachment",volume);
        log.info("请求body={}",body);
        return body;
    }


}
