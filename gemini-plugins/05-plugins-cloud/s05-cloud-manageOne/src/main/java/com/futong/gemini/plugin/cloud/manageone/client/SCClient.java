package com.futong.gemini.plugin.cloud.manageone.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.EntityUtils;

import java.util.Map;

/**
 * 运营侧Client
 */
@Slf4j
public class SCClient extends ManageOneClient{
    public SCClient(ConnectionConfig config) {
        super(config);
    }

    @Override
    protected synchronized String doToken(String projectId) throws Exception {
        String token = "";
        AuthConfig auth = config.getAuthConfig();
        String url = config.getManageOneEndpoint() + "/v3/auth/tokens";
        String authJson = String.format("{" +
                        "\"auth\": {" +
                        "\"identity\": {" +
                        "\"methods\": [\"password\"]," +
                        "\"password\": {" +
                        "\"user\": {" +
                        "\"domain\": {\"name\": \"%s\"}," +
                        "\"name\": \"%s\"," +
                        "\"password\": \"%s\"" +
                        "}" +
                        "}" +
                        "}," +
                        "\"scope\": {" +
                        "\"project\": {\"id\": \"%s\"}" +
                        "}" +
                        "}" +
                        "}",
                auth.getDomain(),
                auth.getUsername(),
                auth.getPassword(),
                StrUtil.isEmpty(projectId) ? auth.getProjectId() : projectId);
        log.info("调用云上接口scclient运营url  = {},authJson={}", url, authJson);
        HttpResponse response = HttpRequest.post(url)
                .body(authJson)
                .execute();
        int statusCode = response.getStatus();
        if(statusCode !=200) {
            throw new RuntimeException("Authentication failed: ");
        }
        String body = response.body();
        token = response.header("X-Subject-Token");
        JSONObject obj = JSONObject.parseObject(body);
        JSONObject jsonObject = obj.getJSONObject("token");
        JSONArray catalogs = jsonObject.getJSONArray("catalog");
        JSONObject user = jsonObject.getJSONObject("user");
        String ecsUrl = "";
        String scUrl = "";
        String vpcUrl = "";
        String imsUrl = "";
        String ccsUrl = "";
        String userId = user.getString("id");
        for (int i = 0; i < catalogs.size(); i++) {
            JSONObject catlog = catalogs.getJSONObject(i);
            if ("ecs".equals(catlog.getString("name"))) {
                JSONArray endpoints = catlog.getJSONArray("endpoints");
                for (int j = 0; j < endpoints.size(); j++) {
                    JSONObject endpoint = endpoints.getJSONObject(j);
                    if ("public".equals(endpoint.getString("interface"))) {
                        ecsUrl = endpoint.getString("url");
                        break;
                    }
                }
            } else if ("sc".equals(catlog.getString("name"))) {
                JSONArray endpoints = catlog.getJSONArray("endpoints");
                for (int j = 0; j < endpoints.size(); j++) {
                    JSONObject endpoint = endpoints.getJSONObject(j);
                    if ("public".equals(endpoint.getString("interface"))) {
                        scUrl = endpoint.getString("url");
                        break;
                    }
                }
            } else if ("vpc".equals(catlog.getString("name"))) {
                JSONArray endpoints = catlog.getJSONArray("endpoints");
                for (int j = 0; j < endpoints.size(); j++) {
                    JSONObject endpoint = endpoints.getJSONObject(j);
                    if ("public".equals(endpoint.getString("interface"))) {
                        vpcUrl = endpoint.getString("url");
                        break;
                    }
                }
            } else if ("ims".equals(catlog.getString("name"))) {
                JSONArray endpoints = catlog.getJSONArray("endpoints");
                for (int j = 0; j < endpoints.size(); j++) {
                    JSONObject endpoint = endpoints.getJSONObject(j);
                    if ("public".equals(endpoint.getString("interface"))) {
                        imsUrl = endpoint.getString("url");
                        break;
                    }
                }
            } else if ("ccs".equals(catlog.getString("name"))) {
                JSONArray endpoints = catlog.getJSONArray("endpoints");
                for (int j = 0; j < endpoints.size(); j++) {
                    JSONObject endpoint = endpoints.getJSONObject(j);
                    if ("public".equals(endpoint.getString("interface"))) {
                        ccsUrl = endpoint.getString("url");
                        break;
                    }
                }
            }
        }
        String ecskey = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId() + "/ecs";
        String scKey = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId() + "/sc";
        String userIdKey = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId() + "/userId";
        String vpcKey = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId() + "/vpc";
        String imsKey = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId() + "/ims";
        String ccsKey = config.getManageOneEndpoint() + "/" + config.getAuthConfig().getDomain() + "/" + config.getAuthConfig().getProjectId() + "/ccs";
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, ecskey, ecsUrl);
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, scKey, scUrl);
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, userIdKey, userId);
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, vpcKey, vpcUrl);
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, imsKey, imsUrl);
        GuavaCacheUtils.put(GuavaCacheUtils.Mode.FIXED, ccsKey, ccsUrl);
        return token;
    }

    /**
     * 调用申请统一接口
     * @param url
     * @param body
     * @return
     * @throws Exception
     */
    public JSONObject doSubscription(String url ,String body) throws Exception {
        String result= doPostJson(url, body);
        return parseResponse(result, JSONObject.class);
    }

    public JSONObject doInstancesAction(Map<String, Object> params) throws Exception {
        String apiPath = "/rest/orchestration/v3.0/fcecs/servers/action";
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String result = doPostJson(apiPath, query);
        return parseResponse(result, JSONObject.class);
    }

    public JSONObject doPostAction(String apiPath , JSONObject req) throws Exception {
        String result= doPostJson(apiPath, req);
        return parseResponse(result, JSONObject.class);
    }

    public JSONObject doPostAction2(String apiPath , JSONObject req) throws Exception {
        String result= doPostJson2(apiPath, req);
        return parseResponse(result, JSONObject.class);
    }


    public JSONObject doGetAction(String apiPath , JSONObject req) throws Exception {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(req);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String result= doGet(apiPath,query);
        return parseResponse(result, JSONObject.class);
    }

    public JSONObject doGetAction2(String apiPath , JSONObject req) throws Exception {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(req);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String result= doGet2(apiPath,query);
        return parseResponse(result, JSONObject.class);
    }

    public JSONObject doDeleteAction(String apiPath , JSONObject req) throws Exception {
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(req);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String result= doDelete2(apiPath,query);
        return parseResponse(result, JSONObject.class);
    }
}
