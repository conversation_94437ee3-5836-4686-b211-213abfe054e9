package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.OCClient;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
public class ComputeInstanceService {
    public static BaseResponse queryRegion(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject regions = client.doInstances("SYS_BusinessRegion", request.getBody().getCloud());
            JSONArray array = regions.getJSONArray("objList");
            return new BaseDataResponse<>(array);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取地域失败!"), e);
        }
    }

    public static BaseResponse queryAzone(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String scUrl = client.getUrl(projectId,"sc");
            scUrl+="/rest/vdc/v3.1/projects/"+projectId;
            log.info("获取可用区url={}", scUrl);
            JSONObject product = client.doGetAction2(scUrl, null);
            JSONArray regions = product.getJSONObject("project").getJSONArray("regions");
            log.info("获取可用区regions={}", regions);
            JSONArray azones = new JSONArray();
            if(regions!=null) {
                try {
                    for (int i = 0; i < regions.size(); i++) {
                        JSONObject region = regions.getJSONObject(i);
                        JSONArray infras = region.getJSONArray("cloud_infras");
                        if (infras != null) {
                            for (int j = 0; j < infras.size(); j++) {
                                JSONObject infra = infras.getJSONObject(j);
                                JSONArray azs = infra.getJSONArray("azs");
                                if (azs != null) {
                                    for (int m = 0; m < azs.size(); m++) {
                                        JSONObject az = azs.getJSONObject(m);
                                        azones.add(az);
                                    }
                                }
                            }
                        }
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                }
            }
            log.info("返回可用区result={}",azones);
            return new BaseDataResponse<>(azones);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取可用区失败!"), e);
        }
    }

    public static BaseResponse queryProject(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String scUrl = client.getUrl(projectId,"sc");
            scUrl+="/v1.0/enterprise-projects?project_id="+projectId;
            log.info("获取企业项目url={}", scUrl);
            JSONObject product = client.doGetAction2(scUrl, null);
            log.info("返回企业项目result={}",product);
            return new BaseDataResponse<>(product);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取企业项目失败!"), e);
        }
    }

    public static BaseResponse queryProduct(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String scUrl = client.getUrl("","sc");
            String regionId = request.getBody().getCloud().getString("regionId");
            scUrl+="/rest/product/v3.0/apply/products?service_type=ecs&region_id="+regionId+"&start=1&limit=100";
            log.info("获取产品url={}", scUrl);
            JSONObject product = client.doGetAction2(scUrl, null);
            log.info("返回产品result={}",product);
            return new BaseDataResponse<>(product);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取产品失败!"), e);
        }
    }

    public static BaseResponse queryTag(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doGetAction("/rest/tag/v3.0/tags", request.getBody().getCloud());
            return new BaseDataResponse<>(vdcs);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取标签失败!"), e);
        }
    }

    public static BaseResponse queryKeypair(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String ecsUrl = client.getUrl(projectId,"ecs");
            String url =  ecsUrl + "/os-keypairs";
            log.info("获取密钥对url={}", url);
            JSONObject keypairs = client.doGetAction2(url, null);
            log.info("获取密钥对结果={}", keypairs);
            return new BaseDataResponse<>(keypairs);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取密钥对失败!"), e);
        }
    }

    public static BaseResponse queryImage(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String imsUrl = client.getUrl(projectId,"ims");
            String visibility = request.getBody().getCloud().getString("visibility");
            String url = imsUrl + "/v2/cloudimages?&visibility="+visibility;
            log.info("查询镜像url:{}",url);
            JSONObject images = client.doGetAction2(url, null);
            log.info("查询镜像结果:{}",images);
            return new BaseDataResponse<>(images);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取镜像失败!"), e);
        }
    }

    public static BaseResponse queryFlavor(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String ccsUrl = client.getUrl(projectId,"ccs");
            String azId = request.getBody().getCloud().getString("availabilityZone")==null?"":request.getBody().getCloud().getString("availabilityZone");
            String url =  ccsUrl + "/resource-tags?resource_type=flavor&availability_zone="+azId;
            log.info("查询规格url={}",url);
            JSONObject flavors = client.doGetAction2(url, null);
            return new BaseDataResponse<>(flavors);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取规格失败!"), e);
        }
    }

    public static BaseResponse queryDiskType(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String ccsUrl = client.getUrl(projectId,"ccs");
            String azId = request.getBody().getCloud().getString("availabilityZone")==null?"":request.getBody().getCloud().getString("availabilityZone");
            String url =  ccsUrl + "/resource-tags?resource_type=volume_type&availability_zone="+azId;
            JSONObject diskTypes = client.doGetAction2(url, null);
            return new BaseDataResponse<>(diskTypes);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取磁盘类型失败!"), e);
        }
    }

    public static BaseResponse queryVpc(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v1/"+projectId+"/vpcs";
            log.info("查询vpc--url={}",url);
            JSONObject vpcs = client.doGetAction2(url, null);
            log.info("查询vpc--result={}",vpcs.toJSONString());
            return new BaseDataResponse<>(vpcs);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取vpc失败!"), e);
        }
    }

    public static BaseResponse querySecurityGroup(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/security-groups?tenant_id="+projectId;
            JSONObject sgs = client.doGetAction2(url, null);
            return new BaseDataResponse<>(sgs);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取安全组失败!"), e);
        }
    }

    public static BaseResponse querySubnet(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String vpcId = request.getBody().getCloud().getString("vpcId")==null?"":request.getBody().getCloud().getString("vpcId");
            String url =  vpcUrl + "/v1/"+projectId+"/subnets?vpc_id="+vpcId;
            log.info("查询子网--url={}",url);
            JSONObject subnets = client.doGetAction2(url, null);
            return new BaseDataResponse<>(subnets);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取子网失败!"), e);
        }
    }

    public static BaseResponse createVm(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String ecsUrl = client.getUrl(projectId,"ecs");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------创建虚拟机url----------{}",ecsUrl);
            JSONObject cloud = request.getBody().getCloud();
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数资源空间不能为空!");
            if(StrUtil.isEmpty(cloud.getString("availabilityZone")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数可用区不能为空!");
            if(StrUtil.isEmpty(cloud.getString("vpcId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数虚拟私有云不能为空!");
            String url =  evsUrl + "/v2/"+projectId+"/volumes";
            JSONObject volume = convertCreateVmVolume(cloud);
            JSONObject result = client.doPostAction2(url, volume);
            JSONObject vol = result.getJSONObject("volume");
            cloud.put("volumeId",vol.getString("id"));
            JSONObject body = convertCreateVm(cloud);
            client.doPostAction2(url, body);
            return BaseResponse.SUCCESS.of("创建ecs成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建ecs失败!"), e);
        }
    }

    public static BaseResponse updateVm(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String ecsUrl = client.getUrl(projectId,"ecs");
            String evsUrl = client.getUrl(projectId,"evs");
            log.info("----------修改虚拟机url----------{}",ecsUrl);
            JSONObject cloud = request.getBody().getCloud();
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数资源空间不能为空!");
            if(StrUtil.isEmpty(cloud.getString("availabilityZone")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数可用区不能为空!");
            if(StrUtil.isEmpty(cloud.getString("vpcId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数虚拟私有云不能为空!");
            String url =  evsUrl + "/v2/"+projectId+"/volumes";
            JSONObject volume = convertCreateVmVolume(cloud);
            JSONObject result = client.doPostAction2(url, volume);
            JSONObject vol = result.getJSONObject("volume");
            cloud.put("volumeId",vol.getString("id"));
            JSONObject body = convertCreateVm(cloud);
            client.doPostAction2(url, body);
            return BaseResponse.SUCCESS.of("修改ecs成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("修改ecs失败!"), e);
        }
    }

    /**
     * POST: https://{运营侧域名}/orchestration/v3.0/fcecs/servers/action
     * Content-Type: application/json
     * x-auth-token: AuthTokenInfo
     * X-Language:zh-cn
     * {
     *   "cloud_infra_id" : "VRM_local-FM",
     *   "vmIds" : ["1234"],
     *   "type" : "stop",
     *   "mode" : "safe"
     * }
     * @param request
     * @return
     */
    public static BaseResponse startInstance(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            List<String> instanceIds = (List)request.getBody().getCloud().get("instanceIds");
            List<String> projectIds = (List)request.getBody().getCloud().get("projectIds");
            for(int i=0;i<instanceIds.size();i++) {
                String projectId = projectIds.get(i);
                if(projectId==null) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "操作云主机失败!");
                }
                String ecsUrl = client.getUrl(projectId,"ecs");
                log.info("ecs---url={}",ecsUrl);
                String url =  ecsUrl + "/servers/"+instanceIds.get(i)+"/action";
                String msg ="{\"os-start\" : { }}";
                JSONObject jsonObject = client.doPostAction2(url, JSONObject.parseObject(msg));
                log.info("操作云主机返回:{}",jsonObject);
            }
            return BaseResponse.SUCCESS.of("操作云主机成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("操作云主机失败!"), e);
        }
    }

    public static BaseResponse stopInstance(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            List<String> instanceIds = (List)request.getBody().getCloud().get("instanceIds");
            List<String> projectIds = (List)request.getBody().getCloud().get("projectIds");
            for(int i=0;i<instanceIds.size();i++) {
                String projectId = projectIds.get(i);
                if(projectId==null) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "操作云主机失败!");
                }
                String ecsUrl = client.getUrl(projectId,"ecs");
                log.info("ecs---url={}",ecsUrl);
                String url =  ecsUrl + "/servers/"+instanceIds.get(i)+"/action";
                String msg ="{\"os-stop\" : {\"type\" : \"SOFT\"}}";
                JSONObject jsonObject = client.doPostAction2(url, JSONObject.parseObject(msg));
                log.info("操作云主机返回:{}",jsonObject);
            }
            return BaseResponse.SUCCESS.of("操作云主机成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("操作云主机失败!"), e);
        }
    }

    public static BaseResponse rebootInstance(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            List<String> instanceIds = (List)request.getBody().getCloud().get("instanceIds");
            List<String> projectIds = (List)request.getBody().getCloud().get("projectIds");
            for(int i=0;i<instanceIds.size();i++) {
                String projectId = projectIds.get(i);
                if(projectId==null) {
                    throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "操作云主机失败!");
                }
                String ecsUrl = client.getUrl(projectId,"ecs");
                log.info("ecs---url={}",ecsUrl);
                String url =  ecsUrl + "/servers/"+instanceIds.get(i)+"/action";
                String msg ="{\"reboot\" : {\"type\" : \"SOFT\"}}";
                JSONObject jsonObject = client.doPostAction2(url, JSONObject.parseObject(msg));
                log.info("操作云主机返回:{}",jsonObject);
            }
            return BaseResponse.SUCCESS.of("操作云主机成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("操作云主机失败!"), e);
        }
    }

    public static BaseResponse operationInstance(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            List<String> instanceIds = (List)request.getBody().getCloud().get("instanceIds");
            List<String> projectIds = (List)request.getBody().getCloud().get("projectIds");
            for(int i=0;i<instanceIds.size();i++) {
                String projectId = projectIds.get(i);
                String ecsUrl = client.getUrl(projectId,"ecs");
                log.info("ecs---url={}",ecsUrl);
                String url =  ecsUrl + "/servers/"+instanceIds.get(i)+"/action";
                JSONObject jsonObject = client.doPostAction(url,request.getBody().getCloud());
                log.info("操作云主机返回:{}",jsonObject);
            }
            return BaseResponse.SUCCESS.of("操作云主机成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("操作云主机失败!"), e);
        }
    }

    /**
     * {
     * "remote_console":{
     * "type": "novnc",
     * "protocol": "vnc"
     * }
     * }
     * @param request
     * @return
     */
    public static BaseResponse consoleInstance(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String openId = request.getBody().getCloud().getString("instanceId");
            String ecsUrl = client.getUrl(projectId,"ecs");
            log.info("ecs---url={}",ecsUrl);
            String url =  ecsUrl + "/servers/"+openId+"/remote-consoles";
            JSONObject jsonObject = client.doPostAction(url,request.getBody().getCloud());
            log.info("操作云主机返回:{}",jsonObject);
            return new BaseDataResponse<>(jsonObject);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("操作云主机失败!"), e);
        }
    }

    /**
     * 响应参数
     * 无
     * 请求示例
     *
     * @param request
     * @return
     */
    public static BaseResponse resetEcsPassword(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String projectId = request.getBody().getCloud().getString("projectId");
            String openId = request.getBody().getCloud().getString("instanceId");
            String ecsUrl = client.getUrl(projectId,"ecs");
            log.info("ecs---url={}",ecsUrl);
            String url =  ecsUrl + "/cloudservers/"+openId+"/os-reset-password";
            JSONObject body = convertResetPassword(request.getBody().getCloud());
            String message = client.doPutJson(url, body);
            log.info("重置密码返回:{}",message);
            return new BaseDataResponse<>(message);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("重置密码失败!"), e);
        }
    }

    public static BaseResponse deleteInstance(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            List<String> instanceIds = (List)request.getBody().getCloud().get("instanceIds");
            List<String> projectIds = (List)request.getBody().getCloud().get("projectIds");
            for(int i=0;i<instanceIds.size();i++) {
                String projectId = projectIds.get(i);
                String ecsUrl = client.getUrl(projectId,"ecs");
                String url =  ecsUrl + "/servers/"+instanceIds.get(i);
                client.doDeleteAction(url,null);
            }
            return BaseResponse.SUCCESS.of("删除ecs成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除ecs失败!"), e);
        }
    }



    public static BaseResponse createSnapshot(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            String scUrl = client.getUrl("","sc");
            String userId = client.getUserId();
            log.info("----------创建快照url----------{}",scUrl);
            JSONObject cloud = request.getBody().getCloud();
            cloud.put("userId",userId);
            cloud.put("productId","f2558623980449f49c653b39986c7fe1");
            if(StrUtil.isEmpty(cloud.getString("region")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数region不能为空!");
            if(StrUtil.isEmpty(cloud.getString("productId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数productId不能为空!");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String url =  scUrl + "/rest/subscription/v3.0/subscriptions";
            return BaseResponse.SUCCESS.of("创建镜像成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建镜像失败!"), e);
        }
    }

    /**
     * {
     * "volume": {
     * "size": 20,
     * "availability_zone": "az1.dc1",
     * "name":"volume01",
     * "multiattach": false,
     * "imageRef":"8d99dbb4-3991-4e4a-abac-9ae4ad13803c",
     * "volume_type": "business_type_01"
     * }
     * }
     * @param cloud
     * @return
     * @throws Exception
     */
    public static JSONObject convertCreateVmVolume(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject volume = new JSONObject();
        volume.put("multiattach",false);
        volume.put("name","volume01");
        volume.put("imageRef",cloud.getString("imageId"));

        JSONArray disks = cloud.getJSONArray("disks");
        if(CollUtil.isNotEmpty(disks)) {
            JSONObject disk = disks.getJSONObject(0);
            String diskType = disk.getString("diskType");
            int diskSize = disk.getInteger("diskSize");
            volume.put("size",diskSize);
            volume.put("volume_type",diskType);
        }
        volume.put("availability_zone",cloud.getString("availabilityZone"));
        body.put("volume",volume);
        log.info("请求body={}",body);
        return body;
    }


    /**
     * {
     *      "reset-password" : {
     *      "new_password" : "password"
     *      }
     *      }
     * @param cloud
     * @return
     * @throws Exception
     */
    public static JSONObject convertResetPassword(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject password = new JSONObject();
        password.put("new_password",cloud.getString("password"));
        body.put("reset-password",password);
        log.info("请求body={}",body);
        return body;
    }

    /**
     * {
     * "server": {
     * "flavorRef": "5",
     * "name": "vm01",
     * "networks": [
     * {
     * "uuid": "d7149f40-d04b-4068-a81d-73282651fb74"
     * }
     * ],
     * "block_device_mapping_v2": [
     * {
     * "uuid": "2a70890e-591b-4cc2-918e-9ac34ebf596e",
     * "source_type": "volume",
     * "destination_type": "volume",
     * "delete_on_termination": "False",
     * "boot_index": 0
     * }
     * ],
     * "availability_zone": "az1.dc1"
     * }
     * }
     * @param cloud
     * @return
     * @throws Exception
     */
    public static JSONObject convertCreateVm(JSONObject cloud) throws Exception {
        JSONObject body = new JSONObject();
        JSONObject server = new JSONObject();
        server.put("flavorRef",cloud.getString("flavorId"));
        server.put("imageRef",cloud.getString("imageId"));
        server.put("name",cloud.getString("name"));
        JSONArray nics = cloud.getJSONArray("nics");
        JSONArray networks = new JSONArray();
        for (int i = 0; i < nics.size(); i++) {
            JSONObject nic = nics.getJSONObject(i);
            JSONObject network = new JSONObject();
            network.put("uuid", nic.getString("subnetId"));
            networks.add(network);
        }
        server.put("networks",networks);

        JSONArray volumes = new JSONArray();
        JSONObject volume = new JSONObject();
        volume.put("uuid", cloud.getString("volumeId"));
        volume.put("source_type", "volume");
        volume.put("destination_type", "volume");
        volume.put("delete_on_termination", "False");
        volume.put("boot_index",0);
        volumes.add(volume);

        server.put("block_device_mapping_v2",volumes);
        server.put("availability_zone",cloud.getString("availabilityZone"));
        body.put("server",server);
        log.info("请求body={}",body);
        return body;
    }

    public static String convertApplyVm(JSONObject cloud) throws Exception {
        String body =
                "{\"subscriptions\":[{\"product_id\":\"{product_id}\",\"service_type\":\"{service_type}\",\"region_id\":\"{region_id}\",\"project_id\":\"{project_id}\",\"tenancy\":\"0\",\"time_zone\":\"GMT+08:00\",\"comments\":\"\",\"operate_type\":\"{operate_type}\",\"secret_params\":\"{secret}\",\"params\":\"{params}\"}]}";
        String params = "{\\\"tenantId\\\":\\\"{tenantId}\\\",\\\"availability_zone\\\":\\\"{availability_zone}\\\",\\\"name\\\":\\\"{name}\\\",\\\"description\\\":\\\"{description}\\\",\\\"imageRef\\\":\\\"{imageRef}\\\",\\\"flavorRef\\\":\\\"{flavorRef}\\\"," +
                "\\\"config_drive\\\":false,\\\"root_volume\\\":{\\\"volumetype\\\":\\\"{diskType}\\\",\\\"size\\\":{diskSize},\\\"extendparam\\\":{\\\"resourceSpecCode\\\":\\\"\\\",\\\"resourceType\\\":\\\"\\\"}},\\\"data_volumes\\\":[],\\\"vpcid\\\":\\\"{vpcId}\\\"," +
                "\\\"nics\\\":[{nics}]," +
                "\\\"security_groups\\\":[{\\\"id\\\":\\\"{security_group}\\\"}],\\\"personality\\\":[],\\\"count\\\":1,\\\"extendparam\\\":{\\\"chargingMode\\\":0,\\\"regionID\\\":\\\"{region_id}\\\",\\\"enterprise_project_id\\\":\\\"{enterprise_project_id}\\\"},\\\"metadata\\\":{\\\"op_svc_userid\\\":\\\"{op_svc_userid}\\\",\\\"__instance_vwatchdog\\\":\\\"false\\\",\\\"_ha_policy_type\\\":\\\"remote_rebuild\\\"},\\\"tags\\\":[],\\\"power_on\\\": \\\"{powerOn}\\\",\\\"extra\\\":{\\\"devices\\\":[{\\\"device_type\\\":\\\"cdrom\\\"}]}}";
        params = params.replace("{tenantId}", cloud.getString("tenantId"));
        params = params.replace("{availability_zone}", cloud.getString("availabilityZone"));
        params = params.replace("{name}", cloud.getString("name"));


        params = params.replace("{imageRef}", cloud.getString("imageId"));
        params = params.replace("{flavorRef}", cloud.getString("flavorId"));
        params = params.replace("{vpcId}", cloud.getString("vpcId"));

        JSONArray disks = cloud.getJSONArray("disks");
        if(CollUtil.isNotEmpty(disks)) {
            JSONObject disk = disks.getJSONObject(0);
            String diskType = disk.getString("diskType");
            int diskSize = disk.getInteger("diskSize");
            params = params.replace("{diskType}", diskType);
            params = params.replace("{diskSize}", diskSize+"");
        }

        JSONArray nics = cloud.getJSONArray("nics");
        String nicStr = "";
        for (int i = 0; i < nics.size(); i++) {
            JSONObject nic = nics.getJSONObject(i);
            if("".equals(nicStr)){
                nicStr = "{\\\"subnet_id\\\": \\\""+nic.getString("subnetId")+"\\\",\\\"ip_address\\\": \\\"\\\",\\\"nictype\\\": \\\"\\\",\\\"physical_network\\\": \\\"\\\",\\\"extra_dhcp_opts\\\": [],\\\"binding:profile\\\": {\\\"disable_security_groups\\\": false,\\\"availability_zone\\\": \\\"\\\"}}";
            }else{
                nicStr = nicStr +",{\\\"subnet_id\\\": \\\""+nic.getString("subnetId")+"\\\",\\\"ip_address\\\": \\\"\\\",\\\"nictype\\\": \\\"\\\",\\\"physical_network\\\": \\\"\\\",\\\"extra_dhcp_opts\\\": [],\\\"binding:profile\\\": {\\\"disable_security_groups\\\": false,\\\"availability_zone\\\": \\\"\\\"}}";
            }
        }
        params = params.replace("{nics}",  nicStr);

        params = params.replace("{security_group}", cloud.getJSONArray("securityGroup").getString(0));
        params = params.replace("{region_id}", cloud.getString("region"));
        params = params.replace("{enterprise_project_id}", cloud.getString("enterpriseProjectId"));
        params = params.replace("{op_svc_userid}", cloud.getString("userId"));
        params = params.replace("{powerOn}", cloud.getString("powerOn")==null?"true":cloud.getString("powerOn"));
        params = params.replace("{ip_address}", cloud.getString("ipAddress")==null?"":cloud.getString("ipAddress"));
        params = params.replace("{description}", cloud.getString("description")==null?"":cloud.getString("description"));

        body = body.replace("{product_id}", cloud.getString("productId"));
        body = body.replace("{service_type}", cloud.getString("serviceType")==null?"ecs":cloud.getString("serviceType"));
        body = body.replace("{region_id}", cloud.getString("region"));
        body = body.replace("{project_id}", cloud.getString("projectId"));

        Integer loginType = cloud.getInteger("loginType");
        String secret = "";
        if(loginType==1) {
            secret = "{\\\"metadata\\\":{},\\\"key_name\\\":\\\""+cloud.getString("keyName")+"\\\"}";
        }else if(loginType==2){
            secret = "{\\\"metadata\\\":{\\\"admin_pass\\\":\\\"\\\"},\\\"admin_password\\\":\\\""+cloud.getString("password")+"\\\"}";
        }else {
            secret = "{\\\"metadata\\\":{}}";
        }

        body = body.replace("{secret}", secret);
        body = body.replace("{operate_type}", cloud.getString("operateType")==null?"apply":cloud.getString("operateType"));

        if (StringUtils.isNotBlank(cloud.getString("password"))) {
            body = body.replace("{password}", cloud.getString("password"));
        }
        body = body.replace("{params}", params);
        log.info("请求body={}",body);
        return body;
    }

    public static String convertDeleteVm(JSONObject cloud) throws Exception {
        String body =
                "{\"subscriptions\":[{\"service_type\":\"{service_type}\",\"region_id\":\"{region_id}\",\"project_id\":\"{project_id}\",\"operate_type\":\"{operate_type}\",\"secret_params\":\"\",\"params\":\"{params}\"}]}";
        String params = "{\"count\":1,\"display\":{\"zh_CN\":[{\"label\":\"名称\",\"type\":\"string\",\"value\":\"{ecsName}\"},{\"label\":\"可用分区\",\"type\":\"string\",\"value\":\"华为\"},{\"label\":\"是否删除系统盘\",\"type\":\"string\",\"value\":\"是\"},{\"label\":\"是否删除数据盘\",\"type\":\"string\",\"value\":\"是\"},{\"label\":\"是否删除快照\",\"type\":\"string\",\"value\":\"是\"}],\"en_US\":[{\"label\":\"Region\",\"type\":\"string\",\"value\":\"东莞\"},{\"label\":\"Name\",\"type\":\"string\",\"value\":\"{ecsName}\"},{\"label\":\"AZ\",\"type\":\"string\",\"value\":\"华为\"},{\"label\":\"System Disk\",\"type\":\"string\",\"value\":\"Yes\"},{\"label\":\"Data Disk\",\"type\":\"string\",\"value\":\"Yes\"},{\"label\":\"Snapshot\",\"type\":\"string\",\"value\":\"Yes\"}]},\"ids\":[{\"id\":\"{ecsId}\"}],\"delete_publicip\":false,\"delete_volume\":true,\"delete_snapshot\":true}";
        params = params.replace("{escName}", cloud.getString("escName"));
        params = params.replace("{ecsId}", cloud.getString("ecsId"));
        body = body.replace("{product_id}", cloud.getString("productId"));
        body = body.replace("{service_type}", cloud.getString("serviceType"));
        body = body.replace("{region_id}", cloud.getString("regionId"));
        body = body.replace("{project_id}", cloud.getString("projectId"));
        body = body.replace("{operate_type}", cloud.getString("operateType"));

        body = body.replace("{params}", params);
        log.info("请求body={}",body);
        return body;
    }


}
