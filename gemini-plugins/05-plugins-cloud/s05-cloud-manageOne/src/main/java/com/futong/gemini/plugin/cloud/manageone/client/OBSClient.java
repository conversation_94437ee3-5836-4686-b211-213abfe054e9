package com.futong.gemini.plugin.cloud.manageone.client;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.FTHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;

import java.util.Map;

/**
 * 运维侧CLient
 */
@Slf4j
public class OBSClient extends ManageOneClient {

    public OBSClient(ConnectionConfig config) {
        super(config);
    }

    //运维侧的认证方式与管理侧的认证方式不同
    @Override
    protected synchronized String doToken(String projectId) throws Exception {
        AuthConfig auth = config.getAuthConfig();
        String url = config.getManageOneEndpoint() + "/rest/plat/smapp/v1/oauth/token";
        String authJson = String.format("{ \n" +
                        "  \"grantType\": \"password\", \n" +
                        "  \"userName\": \"%s\", \n" +
                        "  \"value\": \"%s\" \n" +
                        "} ",
                auth.getUsername(),
                auth.getPassword());
        HttpPut request = new HttpPut(url);
        request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        request.setEntity(new StringEntity(authJson));
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new RuntimeException("Authentication failed: " +
                        EntityUtils.toString(response.getEntity()));
            }
            String responseBody = EntityUtils.toString(response.getEntity());
            JSONObject jsonBody = JSONObject.parseObject(responseBody);
            String accessSession = jsonBody.getString("accessSession");
            if (StrUtil.isEmpty(accessSession)) {
                throw new RuntimeException("Authentication failed: " + responseBody);
            }
            return accessSession;
        }
    }

    // 运维侧通用查询资源信息《ManageOne 8.3.0 运维面API参考 01》
    // 17.1 查询指定资源类型的所有实例
    public JSONObject doInstances(String className, Map<String, Object> params) throws Exception {
        String apiPath = StrUtil.format("/rest/cmdb/v1/instances/{}", className);
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String result = doGet(apiPath, query);
        return parseResponse(result, JSONObject.class);
    }

    // 运维侧通用查询资源信息《ManageOne 8.3.0 运维面API参考 01》
    // 17.83 租户资源查询接口
    public JSONObject doResources(String className, Map<String, Object> params) throws Exception {
        String apiPath = StrUtil.format("/rest/tenant-resource/v1/tenant/resources/{}", className);
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        String result = doGet(apiPath, query);
        return parseResponse(result, JSONObject.class);
    }

    public JSONObject doResourcesPerf(JSONObject req) throws Exception {
        String apiPath = "/rest/performance/v1/data-svc/history-data/action/query";
        String result= doPostJson(apiPath, req);
        return parseResponse(result, JSONObject.class);
    }

    public JSONArray doResourcesAlarm( Map<String, Object> params,String type) throws Exception {
        String apiPath = StrUtil.format("/rest/fault/v1/{}", type);
        Map<String, String> query;
        try {
            query = FTHttpUtils.query(params);
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_PARAM, e);
        }
        log.info("apiPath={}",apiPath);
        String result= doGet(apiPath, query);
        return parseResponse(result, JSONArray.class);
    }

    public JSONObject doResourcesAlarmCsn(String type) throws Exception {
        String apiPath ="/rest/fault/v1/current-alarms/csns";
        long startTime = System.currentTimeMillis() - 1000 * 60 * 5;
        long endTime = System.currentTimeMillis();
        String obj = "{\"query\": {\"filters\": [{\"name\": \""+type+"\",\"field\": \""+type+"\",\"operator\": \"BETWEEN\","
                + "\"values\": ["+startTime+","+endTime+"] }],\"express\": \"and\" }, \"sort\": [{\"field\": \"CSN\", \"order\": \"desc\" }]}";
        String result= doPostJson(apiPath, JSONObject.parse(obj));
        return parseResponse(result, JSONObject.class);
    }

}
