package com.futong.gemini.plugin.cloud.manageone.sampler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.common.utils.PageUtils;
import com.futong.constant.dict.ResourceType;
import com.futong.gemini.model.api.ApiFactory;
import com.futong.gemini.model.api.entity.BaseResponseDataListModel;
import com.futong.gemini.model.api.entity.ResHostStoragePoolApiModel;
import com.futong.gemini.model.api.entity.ResInstanceDiskApiModel;
import com.futong.gemini.model.api.model.BasePageSortSearchRequest;
import com.futong.gemini.model.otc.bxc.entity.TmdbDevopsTenant;
import com.futong.gemini.model.otc.bxc.entity.TmdbTenantLink;
import com.futong.gemini.model.otc.common.model.MoKpi;
import com.futong.gemini.model.otc.gjc.entity.AlarmInfoBean;
import com.futong.gemini.model.otc.nxc.entity.ResourcePerfDetail;
import com.futong.gemini.plugin.cloud.manageone.client.OCClient;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.common.BaseUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import com.futong.gemini.plugin.cloud.sdk.service.BaseCloudService;
import com.futong.sniffgourd.sdk.model.JobInfo;
import com.obs.services.ObsClient;
import com.obs.services.model.ListBucketsRequest;
import com.obs.services.model.ObsBucket;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class FetchService {
    public static BaseResponse toPageGourdResponse(BaseCloudRequest request, BaseResponse response, int totalCount, int pageSize) {
        if (!request.getBody().getGourd().isSeed()) return response;
        response.of("。云上共有" + totalCount + "条信息");
        List<Integer> totalPage = PageUtils.totalPageListSkipOnePage(totalCount, pageSize);
        if (CollUtil.isEmpty(totalPage)) return response;
        return BaseCloudService.toGourdResponse(response, totalPage, (Integer t) -> {
            JobInfo jobInfo = new JobInfo();
            request.getBody().getCloud().put("pageNo", t);
            jobInfo.setRequest(request.cloneJSONObject());
            return jobInfo;
        });
    }

    public static BaseResponse fetchRegion(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject regions = client.doInstances("SYS_BusinessRegion", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertRegion(request, regions);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    regions.getInteger("totalNum"),
                    regions.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取地域失败!"), e);
        }
    }

    public static BaseResponse fetchZone(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject zones = client.doInstances("SYS_Azone", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertZone(request, zones);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    zones.getInteger("totalNum"),
                    zones.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取可用区失败!"), e);
        }
    }

    public static BaseResponse fetchVDC(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("tenant_vdc", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertVDC(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取VDC失败!"), e);
        }
    }

    public static BaseResponse fetchVDCProject(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcProjects = client.doInstances("tenant_project_vdc", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertTenantProject(request, vdcProjects);
            JSONObject projectRegions = client.doInstances("tenant_project_region", request.getBody().getCloud());
            List<TmdbTenantLink> links = Convert.convertProjectRegion(request, projectRegions);
            data.put(TmdbTenantLink.class, links);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcProjects.getInteger("totalNum"),
                    vdcProjects.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取VDC资源空间失败!"), e);
        }
    }

    public static BaseResponse fetchTenant(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("tenant_domain", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertTenant(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取租户失败!"), e);
        }
    }

    public static BaseResponse fetchVm(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_VM", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertEcs(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取云主机失败!"), e);
        }
    }

    public static BaseResponse fetchVmRelation(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("relations/R_VOLUME_MOUNTON_VM", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertVmRelation(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取云主机关系失败!"), e);
        }
    }

    public static BaseResponse fetchVmPerf(BaseCloudRequest request) {
        long current = DateUtil.current();
        BasePageSortSearchRequest searchRequest = Convert.toBasePageSortSearchRequest(request);
        //根据入参分页获取云主机列表
        BaseDataResponse<BaseResponseDataListModel<ResInstanceDiskApiModel>> response = ApiFactory.Api.res.listInstanceDisk(searchRequest);
//        log.info("获取云主机返回信息={}", JSONObject.toJSONString(response));
        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }
        BaseResponse baseResponse = fetchAndSendInstancePerf(response.getData().getList(), request, current);

        return toPageGourdResponse(request, baseResponse,
                response.getData().getCount(),
                request.getBody().getCloud().getInteger("pageSize"));
    }

    public static BaseResponse fetchHostPerf(BaseCloudRequest request) {
        long current = DateUtil.current();
        BasePageSortSearchRequest searchRequest = Convert.toBasePageSortSearchRequest(request);
        //根据入参分页获取主机列表
        BaseDataResponse<BaseResponseDataListModel<ResHostStoragePoolApiModel>> response = ApiFactory.Api.res.listHostStoragePool(searchRequest);
//        log.info("获取主机返回信息={}", JSONObject.toJSONString(response));
        if (ObjectUtil.isNull(response) || ObjectUtil.isNull(response.getData()) || ObjectUtil.isEmpty(response.getData().getList())) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, "未获取到待同步性能数据的资源信息.");
        }
        BaseResponse baseResponse = fetchAndSendHostPerf(response.getData().getList(), request, current);
        return toPageGourdResponse(request, baseResponse,
                response.getData().getCount(),
                request.getBody().getCloud().getInteger("pageSize"));
    }


    public static BaseResponse fetchImage(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_IMAGE", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertImage(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取镜像失败!"), e);
        }
    }

    public static BaseResponse fetchFlavor(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_FLAVOR", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertFlavor(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取规格失败!"), e);
        }
    }

    public static BaseResponse fetchHost(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_HOST", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertHost(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取主机失败!"), e);
        }
    }

    public static BaseResponse fetchKeypair(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_HOST", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertHost(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取主机失败!"), e);
        }
    }

    public static BaseResponse fetchAlarm(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        List<AlarmInfoBean> alarmList = new ArrayList();
        try {
            JSONObject current_csn = client.doResourcesAlarmCsn("occurUtc");
            log.info("采集csns={}",current_csn.toJSONString());
            List<Integer> csns = (List<Integer>)current_csn.get("csns");
            String cns ="";
            if(csns!=null&&!"".equals(csns)){
                for(Integer ics:csns){
                    if("".equals(cns)){
                        cns ="csns="+ics;
                    }else{
                        cns = cns+"&csns="+ics;
                    }
                }
            }
            JSONArray alarm = client.doResourcesAlarm(null, "current-alarms?"+cns);
            List<AlarmInfoBean> currentAlarmInfoBeans = Convert.convertAlarm(request, alarm);
            alarmList.addAll(currentAlarmInfoBeans);
            JSONObject history_csn = client.doResourcesAlarmCsn("clearUtc");
            log.info("采集csns={}",history_csn.toJSONString());
            List<Integer> his_csns = (List<Integer>)history_csn.get("csns");
            String his_cns ="";
            if(his_csns!=null&&!"".equals(his_csns)){
                for(Integer ics:his_csns){
                    if("".equals(his_cns)){
                        his_cns ="csns="+ics;
                    }else{
                        his_cns = his_cns+"&csns="+ics;
                    }
                }
            }
            JSONArray his_alarm = client.doResourcesAlarm(null, "history-alarms?"+his_cns);
            List<AlarmInfoBean> historyAlarmInfoBeans = Convert.convertAlarm(request, his_alarm);
            alarmList.addAll(historyAlarmInfoBeans);
            BaseCloudService.toAetMessageAndSend(alarmList, "alarm");
            BaseResponse response = BaseResponse.SUCCESS.of("采集告警成功");
            return response;
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取告警失败!"), e);
        }
    }

    public static BaseResponse fetchNetwork(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_NETWORK", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertNetwork(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取网络失败!"), e);
        }
    }

    public static BaseResponse fetchSubnet(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_SUBNET", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertSubnet(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取子网失败!"), e);
        }
    }

    public static BaseResponse fetchPort(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_PORT", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertPort(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取端口失败!"), e);
        }
    }

    public static BaseResponse fetchRoute(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_ROUTER", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertRoute(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取路由失败!"), e);
        }
    }

    public static BaseResponse fetchEip(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_EIP", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertEip(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取eip失败!"), e);
        }
    }

    public static BaseResponse fetchAcl(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_VFW", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertAcl(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟防火墙失败!"), e);
        }
    }

    public static BaseResponse fetchAclPolicy(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_VFW_POLICY", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertAclPolicy(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟防火墙策略失败!"), e);
        }
    }

    public static BaseResponse fetchAclRule(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_VFW_RULE", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertAclRule(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取虚拟防火墙规则失败!"), e);
        }
    }

    public static BaseResponse fetchAclRelation(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("relations/R_VFW_USE_POLICY", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertAclRelation(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取acl关系失败!"), e);
        }
    }

    public static BaseResponse fetchAclPolicyRelation(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("relations/R_VFW_POLICY_USE_RULE", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertAclPolicyRelation(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取acl策略关系失败!"), e);
        }
    }

    public static BaseResponse fetchSecurityGroup(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_SECURITY_GROUP", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertSecurity(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取安全组失败!"), e);
        }
    }

    /**
     * 1HKSA6G4E1M1XE7M7YY8
     * LGGLVH4AOPF3IZHVJOCU
     * @param request
     * @return
     */
    public static BaseResponse fetchBucket(BaseCloudRequest request) {
        ObsClient obsClient = ClientUtils.obsClient(request.getBody());
        try {
            ListBucketsRequest req = new ListBucketsRequest();
            req.setQueryLocation(true);
            List<ObsBucket> buckets = obsClient.listBuckets(req);
            log.info("获取桶buckets.size:{}", buckets.size());
            Map<Class, List> data = Convert.convertBucket(request, buckets,obsClient);
            BaseCloudService.fetchSend(request, data);
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取桶失败!"), e);
        }
    }

    public static BaseResponse fetchSFS(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String sfsUrl = client.getUrl(projectId,"sfs");
            log.info("----------创建sfsurl----------{}",sfsUrl);

            return BaseResponse.SUCCESS.of("创建vpc成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建vpc失败!"), e);
        }
    }

    public static BaseResponse fetchSecurityGroupRule(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_SECURITY_GROUP_RULE", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertSecurityRule(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取安全组规则失败!"), e);
        }
    }

    public static BaseResponse fetchVolumeType(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doResources("CLOUD_VOLUME_TYPE", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertVDC(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取卷类型失败!"), e);
        }
    }

    public static BaseResponse fetchStoragePool(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_STORAGE_POOL", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertStoragePool(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取存储池失败!"), e);
        }
    }

    public static BaseResponse fetchVolumeSnapshot(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_SNAPSHOT", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertSnapshot(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取卷快照失败!"), e);
        }
    }

    public static BaseResponse fetchVmDisk(BaseCloudRequest request) {
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        try {
            JSONObject vdcs = client.doInstances("CLOUD_VOLUME", request.getBody().getCloud());
            Map<Class, List> data = Convert.convertVmDisk(request, vdcs);
            BaseResponse response = BaseCloudService.fetchSend(request, data);
            return toPageGourdResponse(request, response,
                    vdcs.getInteger("totalNum"),
                    vdcs.getInteger("pageSize"));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取云盘失败!"), e);
        }
    }

    public static BaseResponse fetchAndSendInstancePerf(List<ResInstanceDiskApiModel> list, BaseCloudRequest request, long current) {
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        List<MoKpi> kpiList = MoKpi.getVmKpiList();
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<String> objIds = new ArrayList<String>();
        List<Long> indicatorIds = new ArrayList<Long>();
        Long objTypeId = 562958543355904l;
        if (list != null) {
            objIds = list.stream().map(ResInstanceDiskApiModel::getOpen_id).collect(Collectors.toList());
        }
        JSONObject perfs = new JSONObject();
        indicatorIds.add(562958543421441l);
        indicatorIds.add(562958543486979l);
        indicatorIds.add(562958543552537l);
        indicatorIds.add(562958543552538l);
        indicatorIds.add(562958543618052l);
        indicatorIds.add(562958543618061l);
        indicatorIds.add(562958543618062l);
        JSONObject req = new JSONObject();
        req.put("range","LAST_1_HOUR");
        req.put("interval","MINUTE");
        req.put("obj_type_id",objTypeId);
        req.put("obj_ids",objIds);
        req.put("indicator_ids",indicatorIds);
        try {
            perfs = client.doResourcesPerf(req).getJSONObject("data");
//            log.info("获取云主机性能数据：{}", JSONObject.toJSONString(client.doResourcesPerf(req)));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取性能失败!"), e);
        }

        for (ResInstanceDiskApiModel vminfo : list) {
            String vmId = vminfo.getOpen_id();
            JSONObject vmJson = perfs.getJSONObject(vmId);
            if (vmJson == null)
                continue;
            long writeTime = 0l;
            Double value = 0.0d;
            ResourcePerfDetail perf = new ResourcePerfDetail();
            perf.setResId(vminfo.getRes_id());
            perf.setOpenId(vminfo.getOpen_id());
            perf.setOpenName(vminfo.getOpen_name());
            perf.setCpuSize(vminfo.getCpu_size() == null ? 0 : Double.valueOf(vminfo.getCpu_size()));
            perf.setMemSize(vminfo.getMem_size() == null ? 0 : Double.valueOf(vminfo.getMem_size()));
            perf.setResourceType(ResourceType.CMDB_INSTANCE_RES.value());
            perf.setCloudType(accessBean.getCloudType());
            perf.setAccountId(accessBean.getCmpId());
            perf.setDiskSize(0d);
            perf.setCpuUsage(0d);
            perf.setMemUsage(0d);
            perf.setDiskUsage(0d);
            perf.setNetIn(0d);
            perf.setNetOut(0d);
            perf.setNetIo(0d);
            perf.setDiskIo(0d);
            perf.setDiskRead(0d);
            perf.setDiskWrite(0d);
            perf.setId(vmId);
            perf.setCreateTime(DateUtil.formatDateTime(new Date(current)));
            JSONObject kpiJson = null;
            for (MoKpi kpi : kpiList) {
                kpiJson = vmJson.getJSONObject(kpi.getIndicatorId() + "");
                if (kpiJson != null) {
                    JSONArray kpiArrays = kpiJson.getJSONArray("series");
                    if (kpiArrays != null) {
                        JSONObject valJson = kpiArrays.getJSONObject(kpiArrays.size() - 2);
                        for (Map.Entry<String, Object> entry : valJson.entrySet()) {
                            if (entry.getValue() == null) {
                                value = -1.0d;
                            } else {
                                value = Double.parseDouble((String) entry.getValue());
                            }
                            writeTime = Long.parseLong(entry.getKey());
                        }
                    }
                }

                value = Double.parseDouble(NumberUtil.roundStr(value, 2));

                if ("cpuUsage".equals(kpi.getKpi())) {
                    perf.setCpuUsage(value < 0 ? 0 : value);
                } else if ("memUsage".equals(kpi.getKpi())) {
                    perf.setMemUsage(value < 0 ? 0 : value);
                } else if ("diskUsage".equals(kpi.getKpi())) {
                    perf.setDiskUsage(value < 0 ? 0 : value);
                } else if ("diskIORead".equals(kpi.getKpi())) {
                    perf.setDiskRead(value < 0 ? 0 : value);
                } else if ("diskIOWrite".equals(kpi.getKpi())) {
                    perf.setDiskWrite(value < 0 ? 0 : value);
                } else if ("netOutRate".equals(kpi.getKpi())) {
                    perf.setNetOut(value < 0 ? 0 : value);
                } else if ("netInRate".equals(kpi.getKpi())) {
                    perf.setNetIn(value < 0 ? 0 : value);
                }
            }
            perfList.add(perf);
        }
        log.info("本次推送虚拟机性能数据：{}", perfList.size());
        BaseCloudService.toPerfMessageAndSend(perfList, "API");
        return BaseResponse.SUCCESS.of("采集性能数据成功");
    }


    public static BaseResponse fetchAndSendHostPerf(List<ResHostStoragePoolApiModel> list, BaseCloudRequest request, long current) {
        List<ResourcePerfDetail> perfList = new ArrayList<>();
        List<MoKpi> kpiList = MoKpi.getHostKpiList();
        OCClient client = ClientUtils.client(OCClient.class, request.getBody());
        CloudAccessBean accessBean = request.getBody().getAccess();
        List<String> objIds = new ArrayList<String>();
        List<Long> indicatorIds = new ArrayList<Long>();
//        log.info("获取主机性能数据page：{},size:{}", page,size);
        Long objTypeId = 1407379178520576l;
        if (list != null) {
            objIds = list.stream().map(ResHostStoragePoolApiModel::getOpen_id).collect(Collectors.toList());
        }
        JSONObject perfs = new JSONObject();
        indicatorIds.add(1407379178586113l);
        indicatorIds.add(1407379178651656l);
        indicatorIds.add(1407379178717195l);
        indicatorIds.add(1407379178717196l);
        indicatorIds.add(1407379178782738l);
        indicatorIds.add(1407379178782739l);
        indicatorIds.add(1407379178782740l);
        JSONObject req = new JSONObject();
        req.put("range","LAST_1_HOUR");
        req.put("interval","MINUTE");
        req.put("obj_type_id",objTypeId);
        req.put("obj_ids",objIds);
        req.put("indicator_ids",indicatorIds);
        try {
            perfs = client.doResourcesPerf(req).getJSONObject("data");
//            log.info("获取主机性能性能数据：{}", JSONObject.toJSONString(perfs));
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("获取性能失败!"), e);
        }

        for (ResHostStoragePoolApiModel hostinfo : list) {
            String vmId = hostinfo.getOpen_id();
            JSONObject vmJson = perfs.getJSONObject(vmId);
//            log.info(vmId+"性能数据---"+vmJson);
            if (vmJson == null)
                continue;
            long writeTime = 0l;
            Double value = 0.0d;
            ResourcePerfDetail perf = new ResourcePerfDetail();
            perf.setResId(hostinfo.getRes_id());
            perf.setOpenId(hostinfo.getOpen_id());
            perf.setOpenName(hostinfo.getOpen_name());
            perf.setCpuSize(hostinfo.getCpu_size() == null ? 0 : Double.valueOf(hostinfo.getCpu_size()));
            perf.setMemSize(hostinfo.getMem_size() == null ? 0 : Double.valueOf(hostinfo.getMem_size()));
            perf.setResourceType(ResourceType.CMDB_HOST_RES.value());
            perf.setCloudType(accessBean.getCloudType());
            perf.setAccountId(accessBean.getCmpId());
            perf.setDiskSize(0d);
            perf.setCpuUsage(0d);
            perf.setMemUsage(0d);
            perf.setDiskUsage(0d);
            perf.setNetIn(0d);
            perf.setNetOut(0d);
            perf.setNetIo(0d);
            perf.setDiskIo(0d);
            perf.setDiskRead(0d);
            perf.setDiskWrite(0d);
            perf.setId(vmId);
            perf.setCreateTime(DateUtil.formatDateTime(new Date(current)));
            JSONObject kpiJson = null;
            for (MoKpi kpi : kpiList) {
                kpiJson = vmJson.getJSONObject(kpi.getIndicatorId() + "");
                if (kpiJson != null) {
                    JSONArray kpiArrays = kpiJson.getJSONArray("series");
                    if (kpiArrays != null) {
                        JSONObject valJson = kpiArrays.getJSONObject(kpiArrays.size() - 2);
                        for (Map.Entry<String, Object> entry : valJson.entrySet()) {
                            if (entry.getValue() == null) {
                                value = -1.0d;
                            } else {
                                value = Double.parseDouble((String) entry.getValue());
                            }
                            writeTime = Long.parseLong(entry.getKey());
                        }
                    }
                }

                value = Double.parseDouble(NumberUtil.roundStr(value, 2));

                if ("cpuUsage".equals(kpi.getKpi())) {
                    perf.setCpuUsage(value < 0 ? 0 : value);
                } else if ("memUsage".equals(kpi.getKpi())) {
                    perf.setMemUsage(value < 0 ? 0 : value);
                } else if ("diskUsage".equals(kpi.getKpi())) {
                    perf.setDiskUsage(value < 0 ? 0 : value);
                } else if ("diskIORead".equals(kpi.getKpi())) {
                    perf.setDiskRead(value < 0 ? 0 : value);
                } else if ("diskIOWrite".equals(kpi.getKpi())) {
                    perf.setDiskWrite(value < 0 ? 0 : value);
                } else if ("netOutRate".equals(kpi.getKpi())) {
                    perf.setNetOut(value < 0 ? 0 : value);
                } else if ("netInRate".equals(kpi.getKpi())) {
                    perf.setNetIn(value < 0 ? 0 : value);
                }
            }
            perfList.add(perf);
        }
        log.info("本次推送主机性能数据：{}", perfList.size());
        BaseCloudService.toPerfMessageAndSend(perfList, "API");
        return BaseResponse.SUCCESS.of("采集性能数据成功");
    }


//    public static BaseResponse fetchBucket(BaseCloudRequest request) {
//        try {
//            OCClient client = ClientUtils.client(OCClient.class, request.getBody());
//            JSONObject bucket = client.doActionJSON("DescribeOSS", request.getBody().getCloud());
//            Map<Class, List> data = Convert.convertOss(request, bucket);
//            BaseResponse baseResponse = BaseCloudService.fetchSend(request, data);
//            return toPageGourdResponse(request, baseResponse,
//                    bucket.getInteger("TotalCount"),
//                    request.getBody().getCloud().getInteger("Limit"));
//        }catch (Exception e){
//            throw new BaseException(BaseResponse.FAIL_OP,"获取对象存储失败");
//        }
//    }

}

