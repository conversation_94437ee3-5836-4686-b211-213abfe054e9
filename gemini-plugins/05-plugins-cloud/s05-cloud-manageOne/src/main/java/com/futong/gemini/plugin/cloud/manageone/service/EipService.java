package com.futong.gemini.plugin.cloud.manageone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPatch;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.manageone.client.SCClient;
import com.futong.gemini.plugin.cloud.manageone.common.ClientUtils;
import com.futong.gemini.plugin.cloud.sdk.model.BaseCloudRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EipService {
    public static BaseResponse createEip(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/floatingips";
            JSONObject body = convertCreateEip(cloud);
            client.doPostAction2(url,body);
            return BaseResponse.SUCCESS.of("创建eip成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("创建eip失败!"), e);
        }
    }

    public static BaseResponse deleteEip(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(cloud.getString("projectId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String url =  vpcUrl + "/v2.0/floatingips/"+cloud.getString("eipId");
            client.doDeleteAction(url,null);
            return BaseResponse.SUCCESS.of("删除eip成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("删除eip失败!"), e);
        }
    }

    public static BaseResponse bindEip(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String eipId = cloud.getString("eipId");
            log.info("----------绑定eipurl----------{}",vpcUrl);
            if(StrUtil.isEmpty(eipId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数eipId不能为空!");
            if(StrUtil.isEmpty(cloud.getString("portId")))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数portId不能为空!");
            String url =  vpcUrl + "/v2.0/floatingips/"+cloud.getString("eipId");;
            JSONObject body = convertBindEip(cloud);
            client.doPutJson(url, body);
            return BaseResponse.SUCCESS.of("绑定eip成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("绑定eip失败!"), e);
        }
    }

    public static BaseResponse unbindEip(BaseCloudRequest request) {
        SCClient client = ClientUtils.client(SCClient.class, request.getBody());
        try {
            JSONObject cloud = request.getBody().getCloud();
            String projectId = cloud.getString("projectId");
            if(StrUtil.isEmpty(projectId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数projectId不能为空!");
            String vpcUrl = client.getUrl(projectId,"vpc");
            String eipId = cloud.getString("eipId");
            if(StrUtil.isEmpty(eipId))
                throw new BaseException(BaseResponse.FAIL_PARAM_EMPTY, "请求参数eipId不能为空!");
            String url =  vpcUrl + "/v2.0/floatingips/"+cloud.getString("eipId");;
            String body =  "{\"floatingip\":{\"port_id\":null}}";
            client.doPutJson(url,body);
            return BaseResponse.SUCCESS.of("解绑eip成功");
        } catch (Exception e) {
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD.of("解绑eip失败!"), e);
        }
    }

    /**
     *{
     "floatingip": {
     "floating_network_id": "fa5773bc-6459-4217-9cc1-494ceefb3d5e",
     "port_id": "24826299-88fb-4937-b80c-4458e1e087bb"
     }
     }
     * @param cloud
     * @return
     */
    public static JSONObject convertCreateEip(JSONObject cloud) {
        JSONObject body = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("floating_network_id",cloud.getString("networkId"));
        floatingip.put("port_id",cloud.getString("portId"));
        body.put("floatingip",floatingip);
        log.info("请求body={}",body);
        return body;
    }

    public static String convertDeleteDisk(JSONObject cloud) {
        String body ="{\"subscriptions\":[\"service_type\":\"{service_type}\",\"region_id\":\"{region_id}\",\"project_id\":\"{project_id}\",\"tenancy\":\"0\",\"time_zone\":\"Asia/Shanghai\",\"comments\":\"\",\"operate_type\":\"{operate_type}\",\"params\":\"{params}\"}]}";
        String params = "{\\\"publicIpId\\\":\\\"{publicIpId}\\\",\\\"tenant_id\\\":\\\"{projectId}\\\",\\\"regionId\\\":\\\"{region}\\\",\\\"display\\\":{\\\"en_US\\\":[{\\\"label\\\":\\\"Region\\\",\\\"value\\\":\\\"boc-test-1\\\",\\\"type\\\":\\\"string\\\"},{\\\"label\\\":\\\"External Network\\\",\\\"value\\\":\\\"exNet_02\\\",\\\"type\\\":\\\"string\\\"},{\\\"label\\\":\\\"Elastic IP Address\\\",\\\"value\\\":\\\"**************\\\",\\\"type\\\":\\\"string\\\"}],\\\"zh_CN\\\":[{\\\"label\\\":\\\"地域\\\",\\\"value\\\":\\\"boc-test-1\\\",\\\"type\\\":\\\"string\\\"},{\\\"label\\\":\\\"外部网络\\\",\\\"value\\\":\\\"exNet_02\\\",\\\"type\\\":\\\"string\\\"},{\\\"label\\\":\\\"弹性IP地址\\\",\\\"value\\\":\\\"**************\\\",\\\"type\\\":\\\"string\\\"}]},\\\"ids\\\":[{\\\"id\\\":\\\"0a6948ad-3e79-4c0c-bba5-728d7427a826\\\",\\\"service_type\\\":\\\"eip\\\"}]}";

        params = params.replace("{region}", cloud.getString("region"));
        params = params.replace("{publicIpId}", cloud.getString("publicIpId"));
        params = params.replace("{projectId}", cloud.getString("projectId"));

        body = body.replace("{service_type}", cloud.getString("serviceType")==null?"eip":cloud.getString("serviceType"));
        body = body.replace("{region_id}", cloud.getString("region"));
        body = body.replace("{project_id}", cloud.getString("projectId"));
        body = body.replace("{operate_type}", cloud.getString("operateType")==null?"delete":cloud.getString("operateType"));

        body = body.replace("{params}", params);
        log.info("请求body={}",body);
        return body;
    }

    public static JSONObject convertBindEip(JSONObject cloud) {
        JSONObject msg = new JSONObject();
        JSONObject floatingip = new JSONObject();
        floatingip.put("port_id",cloud.getString("portId"));
        msg.put("floatingip",floatingip);
        return msg;
    }

}
