package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.CreateImageRequest;
import com.futong.gemini.plugin.cloud.vmware.request.CreateSnapshotRequest;
import com.futong.gemini.plugin.cloud.vmware.request.RequestBody;
import com.futong.gemini.plugin.cloud.vmware.request.UpdateSnapshotRequest;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

;

@Slf4j
public class DatacenterService {

    public static final DatacenterService bean = new DatacenterService();

    public static BaseResponse addDatacenter(JSONObject arguments) {
        String message = "成功发起创建数据中心请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        try {
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            connection.getVimPort().createDatacenter(rootRef, request.getModel().getString("name"));
        } catch (Exception e) {
            log.error("创建数据中心异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建数据中心异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    /**
     * 移除数据中心
     * @param arguments
     * @return
     */
    public static BaseResponse removeDatacenter(JSONObject arguments) {
        String message = "成功发起移除数据中心请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ManagedObjectReference dcMor = new ManagedObjectReference();
        try {
            dcMor.setType(MORTypeEnum.Datacenter.toString());
            dcMor.setValue(request.getModel().getString("openId"));
            connection.getVimPort().destroyTask(dcMor);
        } catch (Exception e) {
            log.error("移除数据中心异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "移除数据中心异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    /**
     * 重命名数据中心
     * @param arguments
     * @return
     */
    public static BaseResponse renameDatacenter(JSONObject arguments) {
        String message = "成功发起重命名数据中心请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ManagedObjectReference dcMor = new ManagedObjectReference();
        try {
            dcMor.setType(MORTypeEnum.Datacenter.toString());
            dcMor.setValue(request.getModel().getString("openId"));
            connection.getVimPort().renameTask(dcMor, request.getModel().getString("name"));
        } catch (Exception e) {
            log.error("重命名数据中心异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重命名数据中心异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

}
