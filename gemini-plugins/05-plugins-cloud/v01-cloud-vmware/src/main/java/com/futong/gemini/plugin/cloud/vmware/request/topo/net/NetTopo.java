package com.futong.gemini.plugin.cloud.vmware.request.topo.net;

import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNode;
import lombok.Data;

import java.util.List;

/**
 * 计算资源基础数据
 * <AUTHOR>
 *
 */
@Data
public class NetTopo {

	/**
	 * 主机网卡信息
	 */
	private List<NetTopoNode> hostNics;

	/**
	 * 网络
	 */
	private List<TopoNode> nets;


	/**
	 * 虚拟机网卡信息
	 */
	private List<NetTopoNode> vmNics;

	private String maxVmId;

	private String maxNicId;

	private String maxNicName;

}
