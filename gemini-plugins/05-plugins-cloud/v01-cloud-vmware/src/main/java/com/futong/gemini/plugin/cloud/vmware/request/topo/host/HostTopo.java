package com.futong.gemini.plugin.cloud.vmware.request.topo.host;

import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNodeInfo;
import lombok.Data;

import java.util.List;

/**
 * 主机关联关系拓扑基础数据
 * <AUTHOR>
 *
 */
@Data
public class HostTopo {

	private TopoNodeInfo node;

	/**
	 * 集群
	 */
	private TopoNodeInfo cluster;

	/**
	 * 网络
	 */
	private List<TopoNodeInfo> nets;

	/**
	 * 数据存储
	 */
	private List<TopoNodeInfo> datastores;

	/**
	 * 虚拟机
	 */
	private List<TopoNodeInfo> vms;

}
