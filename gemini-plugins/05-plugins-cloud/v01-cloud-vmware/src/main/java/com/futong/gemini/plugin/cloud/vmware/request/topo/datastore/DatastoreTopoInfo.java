package com.futong.gemini.plugin.cloud.vmware.request.topo.datastore;


import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNodeInfo;
import lombok.Data;

import java.util.List;

/**
 * 存储详情，映射关系拓扑数据
 * <AUTHOR>
 *
 */
@Data
public class DatastoreTopoInfo {

	/**
	 * 数据存储
	 */
	private TopoNodeInfo node;

	/**
	 * 虚拟机
	 */
	private List<TopoNodeInfo> vms;
	/**
	 * 主机
	 */
	private List<TopoNodeInfo> hosts;

}
