package com.futong.gemini.plugin.cloud.vmware.request.topo.datastore;



import lombok.Data;

/**
 * 存储资源，数据存储基本信息
 * <AUTHOR>
 *
 */
@Data
public class DatastoreNode {

	private String id;

	/**
	 * 数据存储名称
	 */
	private String name;

	private boolean accessible;

	private String type;

	/**
	 * 容量
	 */
	private float size;
	/**
	 * 剩余量
	 */
	private float free;

	/**
	 * 使用量
	 */
	private float used;

	/**
	 * 数据存储每秒读数据次数，单位：r/s
	 */
	private float read;
	/**
	 * 数据存储每秒写数据次数单位：w/s
	 */
	private float write;
}
