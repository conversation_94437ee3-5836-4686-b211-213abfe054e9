package com.futong.gemini.plugin.cloud.vmware.service;

import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseResponse;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.RequestBody;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.vmware.vim25.ClusterConfigSpec;
import com.vmware.vim25.ManagedObjectReference;
import lombok.extern.slf4j.Slf4j;

;

@Slf4j
public class ClusterService {

    public static final ClusterService bean = new ClusterService();

    public static BaseResponse addCluster(JSONObject arguments) {
        String message = "成功发起创建主机集群请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        try {
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            ClusterConfigSpec clusterConfigSpec = new ClusterConfigSpec();
            connection.getVimPort().createCluster(rootRef, request.getModel().getString("name"),clusterConfigSpec);
        } catch (Exception e) {
            log.error("创建主机集群异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "创建主机集群异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }


    /**
     * 移除数据中心
     * @param arguments
     * @return
     */
    public static BaseResponse removeCluster(JSONObject arguments) {
        String message = "成功发起移除主机集群请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ManagedObjectReference dcMor = new ManagedObjectReference();
        try {
            dcMor.setType(MORTypeEnum.ClusterComputeResource.toString());
            dcMor.setValue(request.getModel().getString("openId"));
            connection.getVimPort().destroyTask(dcMor);
        } catch (Exception e) {
            log.error("移除主机集群异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "移除主机集群异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    /**
     * 重命名数据中心
     * @param arguments
     * @return
     */
    public static BaseResponse renameCluster(JSONObject arguments) {
        String message = "成功发起重命名主机集群请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ManagedObjectReference dcMor = new ManagedObjectReference();
        try {
            dcMor.setType(MORTypeEnum.ClusterComputeResource.toString());
            dcMor.setValue(request.getModel().getString("openId"));
            connection.getVimPort().renameTask(dcMor, request.getModel().getString("name"));
        } catch (Exception e) {
            log.error("重命名主机集群异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "重命名主机集群异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

    /**
     * 变更配置主机集群
     * @param arguments
     * @return
     */
    public static BaseResponse reconfigCluster(JSONObject arguments) {
        String message = "成功发起变更主机集群请求.";
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ManagedObjectReference dcMor = new ManagedObjectReference();
        try {
            dcMor.setType(MORTypeEnum.ClusterComputeResource.toString());
            dcMor.setValue(request.getModel().getString("openId"));
            ClusterConfigSpec clusterConfigSpec = new ClusterConfigSpec();
            connection.getVimPort().reconfigureClusterTask(dcMor, clusterConfigSpec,false);
        } catch (Exception e) {
            log.error("变更主机集群异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "变更主机集群异常");
        }
        return BaseResponse.SUCCESS.of(message);
    }

}
