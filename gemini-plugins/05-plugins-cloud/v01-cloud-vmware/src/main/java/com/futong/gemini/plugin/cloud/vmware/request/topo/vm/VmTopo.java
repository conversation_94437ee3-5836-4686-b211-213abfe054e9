package com.futong.gemini.plugin.cloud.vmware.request.topo.vm;

import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNode;
import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNodeInfo;
import lombok.Data;

import java.util.List;

/**
 * 虚拟机关联关系拓扑基础数据
 * <AUTHOR>
 *
 */
@Data
public class VmTopo {

	private TopoNode node;

	/**
	 * 主机
	 */
	private TopoNodeInfo host;

	/**
	 * 集群
	 */
	private TopoNodeInfo cluster;

	/**
	 * 资源池
	 */
	private TopoNodeInfo resourcepool;

	/**
	 * 文件夹
	 */
	private TopoNodeInfo folder;
	/**
	 * 网络
	 */
	private List<TopoNodeInfo> nets;

	/**
	 * 数据存储
	 */
	private List<TopoNodeInfo> datastores;

}
