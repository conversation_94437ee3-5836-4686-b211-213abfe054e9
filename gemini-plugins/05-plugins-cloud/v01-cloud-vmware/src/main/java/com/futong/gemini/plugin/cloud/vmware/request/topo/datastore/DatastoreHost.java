package com.futong.gemini.plugin.cloud.vmware.request.topo.datastore;


import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNodeInfo;
import lombok.Data;

import java.util.List;

/**
 * 存储资源，存储关联主机信息
 * <AUTHOR>
 *
 */
@Data
public class DatastoreHost {

	private String id;

	private String name;

	private List<HostDatastorePath> newpathList;

	/***
	 * 网卡信息
	 */
	private List<TopoNodeInfo> nics;



}
