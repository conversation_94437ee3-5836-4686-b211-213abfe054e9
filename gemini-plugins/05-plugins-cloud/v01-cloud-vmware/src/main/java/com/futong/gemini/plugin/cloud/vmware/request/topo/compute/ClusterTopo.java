package com.futong.gemini.plugin.cloud.vmware.request.topo.compute;

import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNode;
import lombok.Data;

import java.util.List;

/**
 * 计算资源基础数据
 * <AUTHOR>
 *
 */
@Data
public class ClusterTopo<T> {


	private T node;

	/**
	 * 虚拟机
	 */
	private List<TopoNode> vms;

	/**
	 * 主机
	 */
	private List<TopoNode> hosts;

	/**
	 * 网络
	 */
	private List<TopoNode> nets;

	/**
	 * 数据存储
	 */
	private List<TopoNode> datastores;


}
