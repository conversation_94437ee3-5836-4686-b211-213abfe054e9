package com.futong.gemini.plugin.cloud.vmware.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.futong.bean.CloudAccessBean;
import com.futong.common.exception.BaseException;
import com.futong.common.model.BaseDataResponse;
import com.futong.common.model.BaseResponse;
import com.futong.constant.dict.InstanceStatus;
import com.futong.gemini.model.otc.nxc.entity.CmdbNetcardRes;
import com.futong.gemini.plugin.cloud.sdk.client.BaseClient;
import com.futong.gemini.plugin.cloud.vmware.connection.VcConnection;
import com.futong.gemini.plugin.cloud.vmware.connection.server.MorManager;
import com.futong.gemini.plugin.cloud.vmware.enums.DatastoreStatusEnum;
import com.futong.gemini.plugin.cloud.vmware.enums.MORTypeEnum;
import com.futong.gemini.plugin.cloud.vmware.request.RequestBody;
import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNode;
import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNodeInfo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.VmNetTopoNode;
import com.futong.gemini.plugin.cloud.vmware.request.topo.compute.ClusterTopo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.compute.NodeClusterInfo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.compute.NodeHostInfo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.compute.NodeVmInfo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.datastore.*;
import com.futong.gemini.plugin.cloud.vmware.request.topo.host.HostTopo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.net.NetTopo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.net.NetTopoNode;
import com.futong.gemini.plugin.cloud.vmware.request.topo.net.NetTopoRelation;
import com.futong.gemini.plugin.cloud.vmware.request.topo.net.NodeNetInfo;
import com.futong.gemini.plugin.cloud.vmware.request.topo.vm.VmTopo;
import com.futong.gemini.plugin.cloud.vmware.util.VmwareUtils;
import com.futong.gemini.plugin.sdk.common.GuavaCacheUtils;
import com.google.common.graph.Network;
import com.vmware.vim25.*;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.agent.VirtualMachine;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

;

@Slf4j
public class TopoService {

    public static final TopoService bean = new TopoService();

    public static BaseResponse fetchCompute(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ClusterTopo<NodeClusterInfo> topo = new ClusterTopo<NodeClusterInfo>();
        try {
            JSONObject cloud = request.getCloud();
            String clusterId = cloud.getString("clusterId");
            String dcId = cloud.getString("dcId");


            NodeClusterInfo info = new NodeClusterInfo();
            DecimalFormat df = new DecimalFormat("0.00");
            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            List<ManagedObjectReference> clusterList = null;
            ManagedObjectReference rootRef = connection.getServiceContent().getRootFolder();
            try {
                clusterList = morManager.getMorByType(rootRef, MORTypeEnum.ClusterComputeResource.toString());
            } catch (Exception e) {
                log.error("获取" + connection.getHost() + "下集群异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取" + connection.getHost() + "下集群异常");
            }

            for (ManagedObjectReference cluster : clusterList) {
                if (clusterId.equals(cluster.getValue())) {
                    info.setId(cluster.getValue());
                    info.setName(VmwareUtils.bean.getName(cluster,morManager));
                    break;
                }
            }
            List<TopoNode> vms = new ArrayList<TopoNode>();
            TopoNode node = null;
            List<ManagedObjectReference> vmList = null;
            try {
                vmList = morManager.getMorByType(rootRef, MORTypeEnum.VirtualMachine.toString());
            } catch (Exception e) {
                log.error("获取" + connection.getHost() + "下集群异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取虚拟机异常");
            }

            for (ManagedObjectReference mor : vmList) {
                VirtualMachineSummary summary = null;
                try {
                    summary = (VirtualMachineSummary) morManager.getDynamicProperty(mor, "summary");
                } catch (Exception e) {
                    log.error("获取云主机summary信息异常{}", e);
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
                }

                VirtualMachineRuntimeInfo runtimeInfo = null;
                try {
                    runtimeInfo = (VirtualMachineRuntimeInfo) morManager.getDynamicProperty(mor, "runtime");
                } catch (Exception e) {
                    log.error("获取宿主机runtime信息异常{}", e);
                }
                ManagedObjectReference host = runtimeInfo.getHost();
                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager
                            .getDynamicProperty(host, "parent");
                } catch (Exception e) {
                    log.error("获取宿主机runtime信息异常{}", e);
                }
                if (parentMor!=null&&parentMor.getValue().contains("domain-c")&&clusterId.equals(parentMor.getValue()) && !summary.getConfig().isTemplate()) {
                    node = new TopoNode();
                    node.setId(mor.getValue());
                    node.setName(VmwareUtils.bean.getName(mor,morManager));
                    if (ObjectUtil.isNotNull(summary.getConfig().getGuestId()) && summary.getConfig().getGuestId().contains("windows")) {
                        node.setType(2);
                    } else {
                        node.setType(1);
                    }
                    vms.add(node);
                }
            }
            topo.setVms(vms);
            List<TopoNode> hosts = new ArrayList<TopoNode>();
            List<ManagedObjectReference> hostList = null;
            try {
                hostList = morManager.getMorByType(rootRef, MORTypeEnum.HostSystem.toString());
            } catch (Exception e) {
                log.error("获取" + connection.getHost() + "下集群异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取物理机异常");
            }
            int cpu_size = 0;
            Float mem_size = 0f;

            List<TopoNode> nets = new ArrayList<TopoNode>();
            for (ManagedObjectReference hostMor : hostList) {

                ManagedObjectReference parentMor = null;
                try {
                    parentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor, "parent");
                } catch (Exception e) {
                    log.error("获取宿主机parent信息异常{}", e);
                }

                HostListSummary summary = null;
                try {
                    summary = (HostListSummary) morManager.getDynamicProperty(hostMor, "summary");
                } catch (Exception e) {
                    log.error("获取宿主机summary信息异常{}", e);
                }
                if (ObjectUtil.isNotNull(summary)) {
                    HostHardwareSummary hardware = summary.getHardware();
                    cpu_size+=hardware.getNumCpuCores() + 0;
                    mem_size+=(int) (hardware.getMemorySize() / 1024 / 1024/1024);
                }
                if (parentMor.getValue().contains("domain-c")&&clusterId.equals(parentMor.getValue())) {
                    node = new TopoNode();
                    node.setId(hostMor.getValue());
                    node.setName(VmwareUtils.bean.getName(hostMor,morManager));
                    hosts.add(node);

                    List<ManagedObjectReference> networks = null;
                    try {
                        networks = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "network");
                    } catch (Exception e) {
                        log.error("获取宿主机config.network.vswitch信息异常{}", e);
                    }
                    for(ManagedObjectReference hp : networks) {
                        node = new TopoNode();
                        node.setId(hp.getValue());
                        node.setName(VmwareUtils.bean.getName(hp,morManager));
                        nets.add(node);
                    }
                }
            }
            topo.setHosts(hosts);
            List<TopoNode> dsNodes = new ArrayList<TopoNode>();
            List<ManagedObjectReference> datastoreList = null;
            try {
                datastoreList = morManager.getMorByType(rootRef, MORTypeEnum.Datastore.toString());
            } catch (Exception e) {
                log.error("获取" + connection.getHost() + "下集群异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取存储异常");
            }
            long capacity = 0;
            if (datastoreList != null && datastoreList.size() > 0) {
                for (ManagedObjectReference mor : datastoreList) {

                    ManagedObjectReference parentMor = null;
                    try {
                        parentMor = (ManagedObjectReference) morManager.getDynamicProperty(mor, "parent");
                    } catch (Exception e) {
                        log.error("获取宿主机parent信息异常{}", e);
                    }
                    if (!parentMor.getValue().contains("datacenter-")) {
                        parentMor = VmwareUtils.bean.getDcManagedObjectReference(parentMor, morManager);
                    }
                    if (parentMor.getValue().equals(dcId)) {
                        DatastoreSummary summary = null;
                        try {
                            summary = (DatastoreSummary) morManager.getDynamicProperty(mor, "summary");
                        } catch (Exception e) {
                            log.error("获取数据存储summary信息异常{}", e);
                        }
                        node = new TopoNode();
                        node.setId(mor.getValue());
                        node.setName(VmwareUtils.bean.getName(mor,morManager));
                        capacity = summary.getCapacity();
                        dsNodes.add(node);
                    }
                }
            }
            topo.setDatastores(dsNodes);
            info.setDiskSize(Float.parseFloat(df.format(capacity / 1024.0 / 1024 / 1024)));
            info.setCpuCoreNum(cpu_size);
            info.setMemorySize(mem_size);
            topo.setNets(nets);
            info.setHostNum(topo.getHosts().size());
            info.setVmNum(topo.getVms().size());
            topo.setNode(info);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }


    public static BaseResponse fetchComputeVm(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ClusterTopo<NodeVmInfo> topo = new ClusterTopo<NodeVmInfo>();
        try {
            JSONObject cloud = request.getCloud();
            String clusterId = cloud.getString("clusterId");
            String dcId = cloud.getString("dcId");
            String vmId = cloud.getString("vmId");

            NodeVmInfo info = new NodeVmInfo();
            DecimalFormat df = new DecimalFormat("0.00");
            TopoNode node = null;
            String hostId = "";
            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);

            VirtualMachineSummary summary = null;
            try {
                summary = (VirtualMachineSummary) morManager.getDynamicProperty(vmMor, "summary");
            } catch (Exception e) {
                log.error("获取云主机summary信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机summary信息异常");
            }
            info.setCpuTotal(summary.getConfig().getNumCpu());
            info.setMemorySize(summary.getConfig().getMemorySizeMB() / 1024.0f);
            info.setDiskSize(0);
            info.setStatus(summary.getRuntime().getPowerState().toString());
            info.setCpuCoreNum(summary.getConfig().getNumCpu());
            info.setDiskNums(0);
            info.setNetNums(0);
            info.setName(VmwareUtils.bean.getName(vmMor,morManager));
            info.setId(vmMor.getValue());

            VirtualMachineRuntimeInfo runtimeInfo = null;
            try {
                runtimeInfo = (VirtualMachineRuntimeInfo) morManager.getDynamicProperty(vmMor, "runtime");
            } catch (Exception e) {
                log.error("获取宿主机runtime信息异常{}", e);
            }
            ManagedObjectReference hostMor = runtimeInfo.getHost();

            List<TopoNode> hosts = new ArrayList<TopoNode>();
            node = new TopoNode();
            node.setId(hostMor.getValue());
            node.setName(VmwareUtils.bean.getName(hostMor,morManager));
            hosts.add(node);

            topo.setHosts(hosts);

            List<TopoNode> datastores = new ArrayList<TopoNode>();
            List<ManagedObjectReference> datastoreList = null;
            try {
                datastoreList = (List<ManagedObjectReference>) morManager.getDynamicProperty(vmMor, "datastore");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            if (datastoreList != null && datastoreList.size() > 0) {
                for (ManagedObjectReference mr : datastoreList) {
                    node = new TopoNode();
                    node.setId(mr.getValue());
                    node.setName(VmwareUtils.bean.getName(mr,morManager));
                    datastores.add(node);
                }
            }
            List<TopoNode> nets = new ArrayList<TopoNode>();
            List<ManagedObjectReference> networkList = null;
            try {
                networkList = (List<ManagedObjectReference>) morManager.getDynamicProperty(vmMor, "network");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            if (networkList != null && networkList.size() > 0) {
                for (ManagedObjectReference nic : networkList) {
                    NetworkSummary networkSummary = (NetworkSummary)morManager.getDynamicProperty(nic, "summary");
                    node = new TopoNode();
                    node.setId(networkSummary.getNetwork().getValue());
                    node.setName(networkSummary.getName());
                    nets.add(node);
                }
            }
            topo.setNets(nets);
            topo.setDatastores(datastores);
            topo.setNode(info);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }


    public static BaseResponse fetchComputeVmDetail(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        VmTopo topo = new VmTopo();
        try {
            JSONObject cloud = request.getCloud();
            String vmId = cloud.getString("vmId");

            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);

            TopoNode vmNode = new TopoNode();
            vmNode.setId(vmId);
            vmNode.setName(VmwareUtils.bean.getName(vmMor,morManager));
            topo.setNode(vmNode);

            VirtualMachineRuntimeInfo runtimeInfo = null;
            try {
                runtimeInfo = (VirtualMachineRuntimeInfo) morManager.getDynamicProperty(vmMor, "runtime");
            } catch (Exception e) {
                log.error("获取宿主机runtime信息异常{}", e);
            }
            ManagedObjectReference hostMor = runtimeInfo.getHost();

            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (parentMor.getValue().contains("domain-c")) {
                TopoNodeInfo cluster = new TopoNodeInfo();
                cluster.setId(parentMor.getValue());
                cluster.setName(VmwareUtils.bean.getName(parentMor,morManager));
                topo.setCluster(cluster);
            }

            TopoNodeInfo hostNode = new TopoNodeInfo();
            hostNode.setId(hostMor.getValue());
            hostNode.setName(VmwareUtils.bean.getName(hostMor,morManager));
            topo.setHost(hostNode);

            List<ManagedObjectReference> datastoreList = null;
            try {
                datastoreList = (List<ManagedObjectReference>) morManager.getDynamicProperty(vmMor, "datastore");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }

            List<TopoNodeInfo> datastores = new ArrayList<TopoNodeInfo>();
            TopoNodeInfo tnode = null;
            if (datastoreList != null && datastoreList.size() > 0) {
                for (ManagedObjectReference mr : datastoreList) {
                    tnode = new TopoNodeInfo();
                    tnode.setId(mr.getValue());
                    tnode.setName(VmwareUtils.bean.getName(mr,morManager));
                    datastores.add(tnode);
                }
            }
            List<TopoNodeInfo> nets = new ArrayList<TopoNodeInfo>();
            List<ManagedObjectReference> networkList = null;
            try {
                networkList = (List<ManagedObjectReference>) morManager.getDynamicProperty(vmMor, "network");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            if (networkList != null && networkList.size() > 0) {
                for (ManagedObjectReference nic : networkList) {
                    tnode = new TopoNodeInfo();
                    tnode.setId(nic.getValue());
                    tnode.setName(VmwareUtils.bean.getName(nic,morManager));
                    nets.add(tnode);
                }
                topo.setNets(nets);
            }
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }


    public static BaseResponse fetchComputeHost(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ClusterTopo<NodeHostInfo> topo = new ClusterTopo<>();
        try {
            JSONObject cloud = request.getCloud();
            String hostId = cloud.getString("hostId");

            TopoNode node = null;
            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);
            HostListSummary summary = null;
            NodeHostInfo info = new NodeHostInfo();
            try {
                summary = (HostListSummary) morManager.getDynamicProperty(hostMor, "summary");
            } catch (Exception e) {
                log.error("获取宿主机summary信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(summary)) {
                HostHardwareSummary hardware = summary.getHardware();
                info.setCpuCoreNum(hardware.getNumCpuCores() + 0);
                info.setMemorySize((int) (hardware.getMemorySize() / 1024 / 1024 / 1024));
                HostRuntimeInfo hostRuntimeInfo = summary.getRuntime();
                if (ObjectUtil.isNotNull(hostRuntimeInfo)) {
                    switch (hostRuntimeInfo.getPowerState().value().toString()) {
                        case "poweredOn":
                            info.setStatus(InstanceStatus.RUNNING.value());
                            break;
                        case "poweredOff":
                            info.setStatus(InstanceStatus.STOPPED.value());
                            break;
                        default:
                            info.setStatus(InstanceStatus.UNKNOWN.value());
                            break;
                    }
                    info.setDiskSize(0);
                    info.setName(VmwareUtils.bean.getName(hostMor,morManager));
                    info.setId(hostMor.getValue());
                }
            }

            List<TopoNode> vms = new ArrayList<TopoNode>();

            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(ManagedObjectReference vmMor : vmList) {
                node = new TopoNode();
                node.setId(vmMor.getValue());
                node.setName(VmwareUtils.bean.getName(vmMor,morManager));
                vms.add(node);
            }
            topo.setVms(vms);


            List<ManagedObjectReference> dsList = null;
            try {
                dsList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "datastore");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            List<TopoNode> datastores = new ArrayList<TopoNode>();
            for (ManagedObjectReference dsMor : dsList) {
                node = new TopoNode();
                node.setId(dsMor.getValue());
                node.setName(VmwareUtils.bean.getName(dsMor,morManager));
                datastores.add(node);
            }

            topo.setDatastores(datastores);
            List<TopoNode> nets = new ArrayList<TopoNode>();

            List<ManagedObjectReference> networkList = null;
            try {
                networkList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "network");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for (ManagedObjectReference netMor : networkList) {
                node = new TopoNode();
                node.setId(netMor.getValue());
                node.setName(VmwareUtils.bean.getName(netMor,morManager));
                nets.add(node);
            }
            topo.setNets(nets);
            info.setVmNum(topo.getVms().size());
            topo.setNode(info);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }


    public static BaseResponse fetchComputeHostDetail(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        HostTopo topo = new HostTopo();
        try {
            JSONObject cloud = request.getCloud();
            String hostId = cloud.getString("hostId");

            DecimalFormat df = new DecimalFormat("0.00");
            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);

            TopoNodeInfo hostNode = new TopoNodeInfo();
            hostNode.setId(hostId);
            hostNode.setName(VmwareUtils.bean.getName(hostMor,morManager));
            topo.setNode(hostNode);

            ManagedObjectReference parentMor = null;
            try {
                parentMor = (ManagedObjectReference) morManager.getDynamicProperty(hostMor, "parent");
            } catch (Exception e) {
                log.error("获取宿主机parent信息异常{}", e);
            }
            if (parentMor.getValue().contains("domain-c")) {
                TopoNodeInfo cluster = new TopoNodeInfo();
                cluster.setId(parentMor.getValue());
                cluster.setName(VmwareUtils.bean.getName(parentMor,morManager));
                topo.setCluster(cluster);
            }

            TopoNodeInfo node = null;
            List<TopoNodeInfo> vms = new ArrayList<TopoNodeInfo>();
            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(ManagedObjectReference vmMor : vmList) {
                node = new TopoNodeInfo();
                node.setId(vmMor.getValue());
                node.setName(VmwareUtils.bean.getName(vmMor,morManager));
                vms.add(node);
            }
            topo.setVms(vms);

            List<TopoNodeInfo> datastores = new ArrayList<TopoNodeInfo>();
            List<ManagedObjectReference> dsList = null;
            try {
                dsList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "datastore");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for (ManagedObjectReference dsMor : dsList) {
                node = new TopoNodeInfo();
                node.setId(dsMor.getValue());
                node.setName(VmwareUtils.bean.getName(dsMor,morManager));
                datastores.add(node);
            }

            topo.setDatastores(datastores);
            List<TopoNodeInfo> nets = new ArrayList<TopoNodeInfo>();

            List<ManagedObjectReference> networkList = null;
            try {
                networkList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "network");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for (ManagedObjectReference netMor : networkList) {
                node = new TopoNodeInfo();
                node.setId(netMor.getValue());
                node.setName(VmwareUtils.bean.getName(netMor,morManager));
                nets.add(node);
            }
            topo.setNets(nets);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }


    public static BaseResponse fetchComputeDatastore(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ClusterTopo<NodeDatastoreInfo> topo = new ClusterTopo<>();
        try {
            JSONObject cloud = request.getCloud();
            String datastoreId = cloud.getString("datastoreId");

            DecimalFormat df = new DecimalFormat("0.00");
            TopoNode node = null;
            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            ManagedObjectReference dsMor = VmwareUtils.bean.getMor(MORTypeEnum.Datastore.toString(), datastoreId);
            NodeDatastoreInfo info = new NodeDatastoreInfo();
            DatastoreSummary summary = null;
            try {
                summary = (DatastoreSummary) morManager.getDynamicProperty(dsMor, "summary");
            } catch (Exception e) {
                log.error("获取数据存储summary信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(summary)) {
                if (summary.isAccessible()) {
                    info.setStatus(DatastoreStatusEnum.ACTIVE.getKey());
                    info.setName(summary.getName());
                    info.setId(dsMor.getValue());
                    info.setCapacityGB(Float.valueOf(String.format("%.2f", summary.getCapacity() / 1024 / 1024 / 1024.0)));
                    info.setFreeSizeGB(Float.valueOf(String.format("%.2f", summary.getFreeSpace() / 1024 / 1024 / 1024.0)));
                    info.setUsedSizeGB(Float.valueOf(String.format("%.2f", (summary.getCapacity() - summary.getFreeSpace()) / 1024 / 1024 / 1024.0)));
                } else {
                    info.setStatus(DatastoreStatusEnum.INACTIVE.getKey());
                }
            }
            List<TopoNode> vms = new ArrayList<TopoNode>();
            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(dsMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(ManagedObjectReference vmMor : vmList) {
                node = new TopoNode();
                node.setId(vmMor.getValue());
                node.setName(VmwareUtils.bean.getName(vmMor,morManager));
                vms.add(node);
            }
            topo.setVms(vms);

            List<TopoNode> hosts = new ArrayList<TopoNode>();
            List<DatastoreHostMount> hostList = null;
            try {
                hostList = (List<DatastoreHostMount>) morManager.getDynamicProperty(dsMor, "host");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(DatastoreHostMount vmMor : hostList) {
                ManagedObjectReference key = vmMor.getKey();
                node = new TopoNode();
                node.setId(key.getValue());
                node.setName(VmwareUtils.bean.getName(key,morManager));
                hosts.add(node);
            }
            topo.setHosts(hosts);

            info.setHostNum(hosts.size());
            info.setVmNum(vms.size());
            topo.setNode(info);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }

    public static BaseResponse fetchComputeNetwork(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        ClusterTopo<NodeNetInfo> topo = new ClusterTopo<>();
        try {
            JSONObject cloud = request.getCloud();
            String networkId = cloud.getString("networkId");

            TopoNode node = null;
            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            ManagedObjectReference networkMor = VmwareUtils.bean.getMor(MORTypeEnum.Network.toString(), networkId);
            NodeNetInfo info = new NodeNetInfo();
            info.setId(networkMor.getValue());
            info.setName(VmwareUtils.bean.getName(networkMor,morManager));
            List<TopoNode> vms = new ArrayList<TopoNode>();
            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(networkMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(ManagedObjectReference vmMor : vmList) {
                node = new TopoNode();
                node.setId(vmMor.getValue());
                node.setName(VmwareUtils.bean.getName(vmMor,morManager));
                vms.add(node);
            }
            topo.setVms(vms);

            List<TopoNode> hosts = new ArrayList<TopoNode>();
            List<ManagedObjectReference> hostList = null;
            try {
                hostList = (List<ManagedObjectReference>) morManager.getDynamicProperty(networkMor, "host");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(ManagedObjectReference vmMor : hostList) {
                node = new TopoNode();
                node.setId(vmMor.getValue());
                node.setName(VmwareUtils.bean.getName(vmMor,morManager));
                hosts.add(node);
            }
            topo.setHosts(hosts);
            info.setHostNum(hosts.size());
            info.setVmNum(vms.size());
            topo.setNode(info);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }


    public static BaseResponse fetchDatastore(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        DatastoreTopo topo  = new DatastoreTopo();
        try {
            JSONObject cloud = request.getCloud();
            String datastoreId = cloud.getString("datastoreId");

            MorManager morManager = new MorManager();
            morManager.setConnection(connection);

            ManagedObjectReference datastoreMor = VmwareUtils.bean.getMor(MORTypeEnum.Datastore.toString(), datastoreId);
            DatastoreNode node = new DatastoreNode();
            DatastoreSummary summary = null;
            try {
                summary = (DatastoreSummary) morManager.getDynamicProperty(datastoreMor, "summary");
            } catch (Exception e) {
                log.error("获取数据存储summary信息异常{}", e);
            }
            if (ObjectUtil.isNotNull(summary)) {
                if (summary.isAccessible()) {
                    node.setSize(Float.valueOf(String.format("%.2f", summary.getCapacity() / 1024 / 1024 / 1024.0)));
                    node.setFree(Float.valueOf(String.format("%.2f", summary.getFreeSpace() / 1024 / 1024 / 1024.0)));
                    node.setUsed(Float.valueOf(String.format("%.2f", (summary.getCapacity() - summary.getFreeSpace()) / 1024 / 1024 / 1024.0)));
                    node.setId(datastoreMor.getValue());
                    node.setName(VmwareUtils.bean.getName(datastoreMor,morManager));
                    node.setAccessible(summary.isAccessible());
                }
            }
            topo.setNode(node);
            List<DatastoreVm> vms = new ArrayList<>();
            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(datastoreMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(ManagedObjectReference vmMor : vmList) {
                DatastoreVm node1 = new DatastoreVm();
                node1.setId(vmMor.getValue());
                node1.setName(VmwareUtils.bean.getName(vmMor,morManager));
                vms.add(node1);
            }
            topo.setVms(vms);

            List<DatastoreHost> hosts = new ArrayList<DatastoreHost>();
            List<DatastoreHostMount> hostList = null;
            try {
                hostList = (List<DatastoreHostMount>) morManager.getDynamicProperty(datastoreMor, "host");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(DatastoreHostMount vmMor : hostList) {
                ManagedObjectReference key = vmMor.getKey();
                DatastoreHost node1 = new DatastoreHost();
                node1.setId(key.getValue());
                node1.setName(VmwareUtils.bean.getName(key,morManager));
                hosts.add(node1);
            }
            topo.setHosts(hosts);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }


    public static BaseResponse fetchDatastoreVmDisk(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        JSONObject result = new JSONObject();
        try {
            JSONObject cloud = request.getCloud();
            String vmId = cloud.getString("vmId");

            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            JSONArray diskArray = new JSONArray();
            JSONArray guestArray = new JSONArray();
            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);
            VirtualMachineConfigInfo vmConfig = null;
            try {
                vmConfig = (VirtualMachineConfigInfo) morManager.getDynamicProperty(vmMor, "config");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            if (ObjectUtil.isNotNull(vmConfig) && ObjectUtil.isNotNull(vmConfig.getHardware()) && ObjectUtil.isNotEmpty(vmConfig.getHardware().getDevice())) {
                List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
                for (VirtualDevice d : devices) {
                    /**
                     * 磁盘信息
                     */
                    ManagedObjectReference datastoreMor = new ManagedObjectReference();
                    datastoreMor.setType(MORTypeEnum.Datastore.toString());
                    if ((d instanceof VirtualDisk)) {
                        VirtualDisk vd = (VirtualDisk) d;
                        JSONObject obj = new JSONObject();
                        if (vd != null && vd.getBacking() != null && vd
                                .getBacking() instanceof VirtualDiskRawDiskMappingVer1BackingInfo) {
                            VirtualDiskRawDiskMappingVer1BackingInfo backing = (VirtualDiskRawDiskMappingVer1BackingInfo) vd
                                    .getBacking();
                            obj.put("name", vd.getDeviceInfo().getLabel());
                            obj.put("size", vd.getCapacityInKB() / 1024 / 1024);
                            obj.put("type", "精简置备");
                            obj.put("dsName", (String) morManager
                                    .getDynamicProperty(backing.getDatastore(), "name"));
                            diskArray.add(obj);
                        } else if (vd != null && vd.getBacking() != null && vd
                                .getBacking() instanceof VirtualDiskFlatVer2BackingInfo) {
                            VirtualDiskFlatVer2BackingInfo backing = (VirtualDiskFlatVer2BackingInfo) vd
                                    .getBacking();
                            obj.put("name", vd.getDeviceInfo().getLabel());
                            obj.put("size", vd.getCapacityInKB() / 1024 / 1024);
                            obj.put("type", "精简置备");
                            obj.put("dsName", (String) morManager
                                    .getDynamicProperty(backing.getDatastore(), "name"));
                            diskArray.add(obj);
                        } else if (vd != null && vd.getBacking() != null && vd
                                .getBacking() instanceof VirtualDiskSparseVer2BackingInfo) {
                            VirtualDiskSparseVer2BackingInfo backing = (VirtualDiskSparseVer2BackingInfo) vd
                                    .getBacking();
                            obj.put("name", vd.getDeviceInfo().getLabel());
                            obj.put("size", vd.getCapacityInKB() / 1024 / 1024);
                            obj.put("type", "精简置备");
                            obj.put("dsName", (String) morManager
                                    .getDynamicProperty(backing.getDatastore(), "name"));
                            diskArray.add(obj);
                        }
                    }
                }
            }

            GuestInfo guestInfo = null;
            try {
                guestInfo = (GuestInfo) morManager.getDynamicProperty(vmMor, "guest");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            if (ObjectUtil.isNotNull(guestInfo)) {
                List<GuestDiskInfo> diskList = guestInfo.getDisk();
                for (GuestDiskInfo disk : diskList) {
                    JSONObject obj = new JSONObject();
                    obj.put("name", disk.getDiskPath());
                    obj.put("size", Float.valueOf(String.format("%.2f", disk.getCapacity() / 1024 / 1024 / 1024.0)));
                    obj.put("used", Float.valueOf(String.format("%.2f", (disk.getCapacity()-disk.getFreeSpace()) / 1024 / 1024 / 1024.0)));
                    obj.put("free", Float.valueOf(String.format("%.2f", disk.getFreeSpace() / 1024 / 1024 / 1024.0)));
                    guestArray.add(obj);
                }
            }


            result.put("logicDisk", guestArray);
            result.put("phyDisk", diskArray);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(result);
    }


    public static BaseResponse fetchNetwork(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        NetTopo topo = new NetTopo();
        try {
            JSONObject cloud = request.getCloud();
            String hostId = cloud.getString("hostId");

            MorManager morManager = new MorManager();
            morManager.setConnection(connection);

            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);
            TopoNode node = new TopoNode();
            List<TopoNode> nets = new ArrayList<TopoNode>();

            List<ManagedObjectReference> networkList = null;
            try {
                networkList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "network");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for (ManagedObjectReference netMor : networkList) {
                node = new TopoNode();
                node.setId(netMor.getValue());
                node.setName(VmwareUtils.bean.getName(netMor,morManager));
                nets.add(node);
            }
            topo.setNets(nets);


            List<NetTopoNode> hostNics = new ArrayList<NetTopoNode>();

            List<HostVirtualSwitch> vswitchs = null;
            try {
                vswitchs = (List<HostVirtualSwitch>) morManager.getDynamicProperty(hostMor, "config.network.vswitch");
            } catch (Exception e) {
                log.error("获取宿主机config.network.vswitch信息异常{}", e);
            }
            for(HostVirtualSwitch vs : vswitchs) {
                List<String> pnic = vs.getPnic();
                NetTopoNode netTopoNode = new NetTopoNode();
                netTopoNode.setId(vs.getName());
                netTopoNode.setName(vs.getName());
                List<TopoNode> nicss = new ArrayList<TopoNode>();
                for(String nicc : pnic) {
                    TopoNode nicNode = new TopoNode();
                    nicNode.setId(nicc.replace("key-vim.host.PhysicalNic-", ""));
                    nicNode.setName(nicc.replace("key-vim.host.PhysicalNic-", ""));
                    nicss.add(nicNode);
                }
                netTopoNode.setNics(nicss);
                hostNics.add(netTopoNode);
            }
            topo.setHostNics(hostNics);
            List<NetTopoNode> vmNics = new ArrayList<>();
            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            for(ManagedObjectReference vmMor : vmList) {
                NetTopoNode vmnode = new NetTopoNode();
                vmnode.setId(vmMor.getValue());
                vmnode.setName(VmwareUtils.bean.getName(vmMor,morManager));
                List<TopoNode> nics = new ArrayList<TopoNode>();
                VirtualMachineConfigInfo vmConfig =null;
                try {
                    vmConfig = (VirtualMachineConfigInfo)morManager.getDynamicProperty(vmMor, "config");
                } catch (Exception e) {
                    log.error("获取云主机config信息异常{}",e);
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取云主机config信息异常");
                }
                if (ObjectUtil.isNotNull(vmConfig) && ObjectUtil.isNotNull(vmConfig.getHardware()) && ObjectUtil.isNotEmpty(vmConfig.getHardware().getDevice())) {
                    List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
                    for (VirtualDevice d : devices) {
                        CmdbNetcardRes nic = new CmdbNetcardRes();
                        nic.setCategory("vm");
                        nic.setCloud_type(accessBean.getCloudType());
                        nic.setAccount_id(accessBean.getCmpId());
                        if (d instanceof VirtualE1000) {
                            VirtualE1000 ve = (VirtualE1000) d;

                            TopoNode net = new TopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            nics.add(net);
                        } else if (d instanceof VirtualE1000E) {
                            VirtualE1000E ve = (VirtualE1000E) d;
                            TopoNode net = new TopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            nics.add(net);
                        } else if (d instanceof VirtualVmxnet3) {
                            VirtualVmxnet3 ve = (VirtualVmxnet3) d;
                            TopoNode net = new TopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            nics.add(net);
                        } else if (d instanceof VirtualVmxnet2) {
                            VirtualVmxnet2 ve = (VirtualVmxnet2) d;
                            TopoNode net = new TopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            nics.add(net);
                        } else {
                            continue;
                        }
                    }
                }
                vmnode.setNics(nics);
                vmNics.add(vmnode);
            }
            topo.setVmNics(vmNics);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }

    public static BaseResponse fetchNetworkHostNic(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        NetTopoRelation topo = new NetTopoRelation();
        try {
            JSONObject cloud = request.getCloud();
            String hostId = cloud.getString("hostId");
            String nicName = cloud.getString("nicId");

            MorManager morManager = new MorManager();
            morManager.setConnection(connection);

            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);
            TopoNode node = new TopoNode();
            List<TopoNode> nodes = new ArrayList<>();


            List<ManagedObjectReference> networks = null;
            try {
                networks = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "network");
            } catch (Exception e) {
                log.error("获取宿主机config.network.vswitch信息异常{}", e);
            }
            Map<String, String> netMap = new HashMap<>();
            for(ManagedObjectReference hp : networks) {
                netMap.put(VmwareUtils.bean.getName(hp,morManager), hp.getValue());
            }

            HostConfigInfo configInfo = null;
            try{
                configInfo = (HostConfigInfo)morManager.getDynamicProperty(hostMor,"config");
            }catch(Exception e){
                e.getMessage();
            }
            HostNetworkInfo infos = configInfo.getNetwork();
            List<HostVirtualSwitch> switchss = infos.getVswitch();
            List<String> activeNic = new ArrayList<>();
            if(switchss!=null)
                for(int i=0;i<switchss.size();i++){
                    if(switchss.get(i)!=null&&switchss.get(i).getSpec()!=null&&switchss.get(i).getSpec().getPolicy()!=null&&switchss.get(i).getSpec().getPolicy().getNicTeaming()!=null&&switchss.get(i).getSpec().getPolicy().getNicTeaming().getNicOrder()!=null){
                        activeNic = switchss.get(i).getSpec().getPolicy().getNicTeaming().getNicOrder().getActiveNic();
                    }
                }

            List<HostPortGroup> hostPortgroupList = null;
            try {
                hostPortgroupList = (List<HostPortGroup>) morManager.getDynamicProperty(hostMor, "config.network.portgroup");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            if(ObjectUtil.isNotEmpty(hostPortgroupList)) {
                for (HostPortGroup group : hostPortgroupList) {
                    if(activeNic.contains(nicName)) {
                        node = new TopoNode();
                        node.setId(netMap.get(group.getSpec().getName())==null?"":netMap.get(group.getSpec().getName()));
                        node.setName(group.getSpec().getName());
                        nodes.add(node);
                    }
                }
            }
            topo.setNets(nodes);

            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            List<VmNetTopoNode> nics = new ArrayList<>();
            for(ManagedObjectReference vmMor : vmList) {
                NetTopoNode vmnode = new NetTopoNode();
                vmnode.setId(vmMor.getValue());
                vmnode.setName(VmwareUtils.bean.getName(vmMor,morManager));
                VirtualMachineConfigInfo vmConfig =null;
                try {
                    vmConfig = (VirtualMachineConfigInfo)morManager.getDynamicProperty(vmMor, "config");
                } catch (Exception e) {
                    log.error("获取云主机config信息异常{}",e);
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取云主机config信息异常");
                }
                if (ObjectUtil.isNotNull(vmConfig) && ObjectUtil.isNotNull(vmConfig.getHardware()) && ObjectUtil.isNotEmpty(vmConfig.getHardware().getDevice())) {
                    List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
                    for (VirtualDevice d : devices) {
                        CmdbNetcardRes nic = new CmdbNetcardRes();
                        nic.setCategory("vm");
                        nic.setCloud_type(accessBean.getCloudType());
                        nic.setAccount_id(accessBean.getCmpId());
                        if (d instanceof VirtualE1000) {
                            VirtualE1000 ve = (VirtualE1000) d;
                            VmNetTopoNode net = new VmNetTopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            net.setVmId(vmMor.getValue());
                            nics.add(net);
                        } else if (d instanceof VirtualE1000E) {
                            VirtualE1000E ve = (VirtualE1000E) d;
                            VmNetTopoNode net = new VmNetTopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            net.setVmId(vmMor.getValue());
                            nics.add(net);
                        } else if (d instanceof VirtualVmxnet3) {
                            VirtualVmxnet3 ve = (VirtualVmxnet3) d;
                            VmNetTopoNode net = new VmNetTopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            net.setVmId(vmMor.getValue());
                            nics.add(net);
                        } else if (d instanceof VirtualVmxnet2) {
                            VirtualVmxnet2 ve = (VirtualVmxnet2) d;
                            VmNetTopoNode net = new VmNetTopoNode();
                            net.setId(ve.getKey()+"");
                            net.setName(ve.getDeviceInfo().getLabel());
                            net.setVmId(vmMor.getValue());
                            nics.add(net);
                        } else {
                            continue;
                        }
                    }
                }
            }
            topo.setVmNics(nics);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }

    public static BaseResponse fetchNetworkVmNic(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        NetTopoRelation topo = new NetTopoRelation();
        try {
            JSONObject cloud = request.getCloud();
            String vmId = cloud.getString("vmId");
            String nicId = cloud.getString("nicId");

            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            List<TopoNode> nets = new ArrayList<TopoNode>();
            List<TopoNode> hosts = new ArrayList<>();

            String portgroudID = "";

            ManagedObjectReference vmMor = VmwareUtils.bean.getMor(MORTypeEnum.VirtualMachine.toString(), vmId);
            VirtualMachineConfigInfo vmConfig =null;
            try {
                vmConfig = (VirtualMachineConfigInfo)morManager.getDynamicProperty(vmMor, "config");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}",e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取云主机config信息异常");
            }
            if (ObjectUtil.isNotNull(vmConfig) && ObjectUtil.isNotNull(vmConfig.getHardware()) && ObjectUtil.isNotEmpty(vmConfig.getHardware().getDevice())) {
                List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
                for (VirtualDevice d : devices) {
                    CmdbNetcardRes nic = new CmdbNetcardRes();
                    nic.setCategory("vm");
                    nic.setCloud_type(accessBean.getCloudType());
                    nic.setAccount_id(accessBean.getCmpId());
                    if (d instanceof VirtualE1000) {
                        VirtualE1000 ve = (VirtualE1000) d;
                        VmNetTopoNode net = new VmNetTopoNode();
                        net.setId(ve.getKey()+"");
                        net.setName(ve.getDeviceInfo().getLabel());
                        net.setVmId(vmMor.getValue());
                        if(nicId.equals(net.getId())) {
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                        }
                    } else if (d instanceof VirtualE1000E) {
                        VirtualE1000E ve = (VirtualE1000E) d;
                        VmNetTopoNode net = new VmNetTopoNode();
                        net.setId(ve.getKey()+"");
                        net.setName(ve.getDeviceInfo().getLabel());
                        net.setVmId(vmMor.getValue());
                        if(nicId.equals(net.getId())) {
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                        }
                    } else if (d instanceof VirtualVmxnet3) {
                        VirtualVmxnet3 ve = (VirtualVmxnet3) d;
                        VmNetTopoNode net = new VmNetTopoNode();
                        net.setId(ve.getKey()+"");
                        net.setName(ve.getDeviceInfo().getLabel());
                        net.setVmId(vmMor.getValue());
                        if(nicId.equals(net.getId())) {
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                        }
                    } else if (d instanceof VirtualVmxnet2) {
                        VirtualVmxnet2 ve = (VirtualVmxnet2) d;
                        VmNetTopoNode net = new VmNetTopoNode();
                        net.setId(ve.getKey()+"");
                        net.setName(ve.getDeviceInfo().getLabel());
                        net.setVmId(vmMor.getValue());
                        if(nicId.equals(net.getId())) {
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                        }
                    } else {
                        continue;
                    }
                }
            }

            TopoNode node = new TopoNode();
            node.setId(portgroudID);
            node.setName(portgroudID);
            nets.add(node);
            topo.setNets(nets);

        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }

    public static BaseResponse fetchNetworkPortgroup(JSONObject arguments) {
        CloudAccessBean accessBean = BaseClient.auths.get();
        RequestBody request = BaseClient.bodys.get().toJavaObject(RequestBody.class);
        VcConnection connection = (VcConnection) GuavaCacheUtils.getObj(GuavaCacheUtils.Mode.AUTO, accessBean.getCmpId());
        NetTopoRelation topo = new NetTopoRelation();
        try {
            JSONObject cloud = request.getCloud();
            String hostId = cloud.getString("hostId");
            String portgroupId = cloud.getString("portgroupId");

            MorManager morManager = new MorManager();
            morManager.setConnection(connection);
            ManagedObjectReference hostMor = VmwareUtils.bean.getMor(MORTypeEnum.HostSystem.toString(), hostId);


            List<HostVirtualSwitch> vswitchs = null;
            try {
                vswitchs = (List<HostVirtualSwitch>) morManager.getDynamicProperty(hostMor, "config.network.vswitch");
            } catch (Exception e) {
                log.error("获取宿主机config.network.vswitch信息异常{}", e);
            }
            List<TopoNode> nodes = new ArrayList<>();
            for(HostVirtualSwitch vs : vswitchs) {
                List<String> pnic = vs.getPnic();
                for(String nicc : pnic) {
                    TopoNode nicNode = new TopoNode();
                    nicNode.setId(nicc.replace("key-vim.host.PhysicalNic-", ""));
                    nicNode.setName(nicc.replace("key-vim.host.PhysicalNic-", ""));
                    nodes.add(nicNode);
                }
            }
            topo.setHostNics(nodes);

            List<ManagedObjectReference> vmList = null;
            try {
                vmList = (List<ManagedObjectReference>) morManager.getDynamicProperty(hostMor, "vm");
            } catch (Exception e) {
                log.error("获取云主机config信息异常{}", e);
                throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取云主机config信息异常");
            }
            List<VmNetTopoNode> nics = new ArrayList<>();
            for(ManagedObjectReference vmMor : vmList) {
                NetTopoNode vmnode = new NetTopoNode();
                vmnode.setId(vmMor.getValue());
                vmnode.setName(VmwareUtils.bean.getName(vmMor,morManager));
                VirtualMachineConfigInfo vmConfig =null;
                try {
                    vmConfig = (VirtualMachineConfigInfo)morManager.getDynamicProperty(vmMor, "config");
                } catch (Exception e) {
                    log.error("获取云主机config信息异常{}",e);
                    throw new BaseException(BaseResponse.FAIL_OP_CLOUD,e ,"获取云主机config信息异常");
                }
                if (ObjectUtil.isNotNull(vmConfig) && ObjectUtil.isNotNull(vmConfig.getHardware()) && ObjectUtil.isNotEmpty(vmConfig.getHardware().getDevice())) {
                    List<VirtualDevice> devices = vmConfig.getHardware().getDevice();
                    for (VirtualDevice d : devices) {
                        CmdbNetcardRes nic = new CmdbNetcardRes();
                        nic.setCategory("vm");
                        nic.setCloud_type(accessBean.getCloudType());
                        nic.setAccount_id(accessBean.getCmpId());
                        if (d instanceof VirtualE1000) {
                            VirtualE1000 ve = (VirtualE1000) d;
                            String portgroudID = "";
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                            if(portgroudID.equals(portgroupId)) {
                                VmNetTopoNode node = new VmNetTopoNode();
                                node.setId(ve.getKey()+"");
                                node.setName(ve.getDeviceInfo().getLabel());
                                node.setVmId(vmMor.getValue());
                                nics.add(node);
                            }
                        } else if (d instanceof VirtualE1000E) {
                            VirtualE1000E ve = (VirtualE1000E) d;
                            String portgroudID = "";
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                            if(portgroudID.equals(portgroupId)) {
                                VmNetTopoNode node = new VmNetTopoNode();
                                node.setId(ve.getKey()+"");
                                node.setName(ve.getDeviceInfo().getLabel());
                                node.setVmId(vmMor.getValue());
                                nics.add(node);
                            }
                        } else if (d instanceof VirtualVmxnet3) {
                            VirtualVmxnet3 ve = (VirtualVmxnet3) d;
                            String portgroudID = "";
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                            if(portgroudID.equals(portgroupId)) {
                                VmNetTopoNode node = new VmNetTopoNode();
                                node.setId(ve.getKey()+"");
                                node.setName(ve.getDeviceInfo().getLabel());
                                node.setVmId(vmMor.getValue());
                                nics.add(node);
                            }
                        } else if (d instanceof VirtualVmxnet2) {
                            VirtualVmxnet2 ve = (VirtualVmxnet2) d;
                            String portgroudID = "";
                            if (ve.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                                VirtualEthernetCardDistributedVirtualPortBackingInfo v = (VirtualEthernetCardDistributedVirtualPortBackingInfo) ve
                                        .getBacking();
                                portgroudID = v.getPort().getPortgroupKey();
                            } else if (ve
                                    .getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                                VirtualEthernetCardNetworkBackingInfo vnet = (VirtualEthernetCardNetworkBackingInfo) ve
                                        .getBacking();
                                portgroudID = vnet.getNetwork().getValue();
                            }
                            if(portgroudID.equals(portgroupId)) {
                                VmNetTopoNode node = new VmNetTopoNode();
                                node.setId(ve.getKey()+"");
                                node.setName(ve.getDeviceInfo().getLabel());
                                node.setVmId(vmMor.getValue());
                                nics.add(node);
                            }
                        } else {
                            continue;
                        }
                    }
                }
            }
            topo.setVmNics(nics);
        } catch (Exception e) {
            log.error("获取拓扑异常{}", e);
            throw new BaseException(BaseResponse.FAIL_OP_CLOUD, e, "获取拓扑异常");
        }
        return new BaseDataResponse<>(topo);
    }
}
