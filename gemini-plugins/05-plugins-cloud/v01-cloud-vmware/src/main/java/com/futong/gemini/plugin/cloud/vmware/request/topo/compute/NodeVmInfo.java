package com.futong.gemini.plugin.cloud.vmware.request.topo.compute;


import lombok.Data;

@Data
public class NodeVmInfo {


	/**
	 * Id主键
	 */
	private String id;

	/**
	 * 名称
	 */
	private String name;

	private String ipAddress;
	/**
	 * CPU总量：单位GHz
	 */
	private float cpuTotal;

	/**
	 * 内存总量：单位GB
	 */
	private float memorySize;

	/**
	 * 存储总量：单位GB
	 */
	private float diskSize;

	/**
	 * CPU总核数：单位核
	 */
	private Integer cpuCoreNum;

	private Integer diskNums;

	private Integer netNums;

	private String status;

	private String osType;

	private String toolsVersion;
}
