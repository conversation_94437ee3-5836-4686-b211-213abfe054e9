package com.futong.gemini.plugin.cloud.vmware.request.topo.datastore;

import lombok.Data;

/***
 * 物理机和其所挂载的datastore管理类
 *
 * <AUTHOR>
 *
 */
@Data
public class HostDatastorePath {
	/***
	 * 主键ID
	 */

	private String _id;

	/***
	 * 物理主机ID
	 */
	private String hostId;
	/**
	 * 关联datatoreID
	 */
	private String datastoreId;
	/**
	 * 关联datatore名称
	 */
	private String datastoreName;
	private String hbaName;

	/**
	 * 读
	 */
	private long read;

	/**
	 * 写
	 */
	private long write;
	/**
	 * paths
	 */
	private String paths;

	/**
	 * 存储卡类型
	 */
	private String type;
	/**
	 * status
	 */
	private String status;
	/**
	 * vcenter地址
	 */
	private String vcIp;

	/**
	 * dcId
	 */
	private String dcId;
	private long writeTime;

	/**
     * 资源在CMP上的唯一标识
     */
    private String cmpId;
}
