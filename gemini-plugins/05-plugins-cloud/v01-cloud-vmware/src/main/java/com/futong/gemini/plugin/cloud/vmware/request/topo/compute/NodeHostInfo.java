package com.futong.gemini.plugin.cloud.vmware.request.topo.compute;


import lombok.Data;

@Data
public class NodeHostInfo {


	/**
	 * Id主键
	 */
	private String id;

	/**
	 * 名称
	 */
	private String name;


	/**
	 * 虚拟机数量
	 */
	private Integer vmNum;

	/**
	 * CPU总量：单位GHz
	 */
	private float cpuTotal;

	/**
	 * 内存总量：单位GB
	 */
	private float memorySize;

	/**
	 * 存储总量：单位GB
	 */
	private float diskSize;

	/**
	 * CPU总核数：单位核
	 */
	private Integer cpuCoreNum;


	private String status;

	/**
	 * 当集群HA主机自治功能打开时有效，默认为空，表示关闭状态，集权HA主机自治功能未代开<br>
	 * closed 关闭，<br>
	 * alive 正常，<br>
	 * fault 故障，<br>
	 * electing 选举中，<br>
	 * partitioned 网络分割，<br>
	 * isolated 网络隔离，<br>
	 * agentunreachable 无法访问代理，<br>
	 * unknown未知。
	 */
	private String haState;

	/**
	 * 型号
	 */
	private String version;

	/**
	 * 主板制造商
	 */
	private String boardMfg;
}
