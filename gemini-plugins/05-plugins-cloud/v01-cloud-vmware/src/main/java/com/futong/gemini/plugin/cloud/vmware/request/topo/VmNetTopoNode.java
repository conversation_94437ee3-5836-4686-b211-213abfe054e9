package com.futong.gemini.plugin.cloud.vmware.request.topo;

import lombok.Data;

/**
 * topo基础数据
 *
 * <AUTHOR>
 *
 */
@Data
public class VmNetTopoNode {

	private String id;

	private String name;

	private String vmId;

	/**
	 * 类型：仅供虚拟机使用：1linux、2windows
	 */
	private Integer type;

	public VmNetTopoNode(String id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	public VmNetTopoNode() {
		super();
	}
}
