package com.futong.gemini.plugin.cloud.vmware.request.topo.net;

import com.futong.gemini.plugin.cloud.vmware.request.topo.TopoNode;
import com.futong.gemini.plugin.cloud.vmware.request.topo.VmNetTopoNode;
import lombok.Data;

import java.util.List;

/**
 * 网络拓扑虚拟机信息
 *
 * <AUTHOR>
 *
 */
@Data
public class NetTopoRelation {

	/**
	 * 网络信息
	 */
	private List<TopoNode> nets;

	private List<VmNetTopoNode> vmNics;

	private List<TopoNode> hostNics;
}
